// TypeBox schema for claim-payments service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, imageSchema } from '../../utils/common/typebox-schemas.js'
const refunds = Type.Object({
  ded: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  amount: Type.Number(),
  refundedAt: Type.Optional(Type.Any()),
  status: Type.Optional(
      Type.Union([
        Type.Literal("pending"),
        Type.Literal("complete"),
        Type.Literal("cancelled"),
      ])
  ),
  confirmation: Type.String(),
})
export const claimPaymentsSchema = Type.Object({
  _id: ObjectIdSchema(),
  claim: ObjectIdSchema(),
  visit: Type.Optional(ObjectIdSchema()),
  plan: ObjectIdSchema(),
  person: ObjectIdSchema(),
  org: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  provider: ObjectIdSchema(),
  coverage: Type.Optional(ObjectIdSchema()),
  enrollment: Type.Optional(ObjectIdSchema()),
  preventive: Type.Optional(Type.Boolean()),
  allowedMethods: Type.Optional(
    Type.Array(
      Type.Union([
        Type.Literal("card"),
        Type.Literal("ach"),
        Type.Literal("ca"),
      ])
    )
  ),
  method: Type.Optional(
    Type.Union([
      Type.Literal("card"),
      Type.Literal("ach"),
      Type.Literal("ca"),
      Type.Literal("ext"),
    ])
  ),
  checkoutSession: Type.Optional(Type.String()),
  customerId: Type.Optional(Type.String()),
  payment_method: Type.Optional(Type.String()),
  transactionId: Type.Optional(Type.String()),
  providerCareAccount: Type.Optional(ObjectIdSchema()),
  careAccount: Type.Optional(ObjectIdSchema()),
  card: Type.Optional(ObjectIdSchema()),
  budget: Type.Optional(ObjectIdSchema()),
  amount: Type.Number(),
  ded: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  due: Type.Optional(Type.String()),
  refunds: Type.Optional(
    Type.Array(
      refunds
    )
  ),
  type: Type.Optional(Type.String()),
  confirmation: Type.Optional(Type.String()),
  confirmedAt: Type.Optional(Type.Any()),
  fullPayment: Type.Optional(Type.Boolean()),
  status: Type.Optional(
    Type.Union([
      Type.Literal("request"),
      Type.Literal("offer"),
      Type.Literal("pending"),
      Type.Literal("paid"),
      Type.Literal("cancelled"),
      Type.Literal("refunded"),
      Type.Literal("returned"),
    ])
  ),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  ...commonFields
}, { $id: 'ClaimPayments', additionalProperties: false })

export type ClaimPayments = Static<typeof claimPaymentsSchema>
export const claimPaymentsValidator = getValidator(claimPaymentsSchema, dataValidator)
export const claimPaymentsResolver = resolve<ClaimPayments, HookContext>({})
export const claimPaymentsExternalResolver = resolve<ClaimPayments, HookContext>({})

export const claimPaymentsDataSchema = Type.Object({
  ...Type.Omit(claimPaymentsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimPaymentsData = Static<typeof claimPaymentsDataSchema>
export const claimPaymentsDataValidator = getValidator(claimPaymentsDataSchema, dataValidator)
export const claimPaymentsDataResolver = resolve<ClaimPaymentsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const claimPaymentsQueryProperties = Type.Pick(claimPaymentsSchema, ['_id', 'claim', 'visit', 'plan', 'person', 'org', 'patient', 'provider', 'coverage', 'enrollment', 'providerCareAccount', 'careAccount', 'card', 'budget'])


const pushPullOpts = [
  {path: 'allowedMethods', type: {type: 'string', enum: ['card', 'ach', 'ca']}},
  {path: 'refunds', type: refunds}
]


export const claimPaymentsPatchSchema = commonPatch(claimPaymentsSchema, {pushPullOpts, pickedForSet: claimPaymentsQueryProperties})
export type ClaimPaymentsPatch = Static<typeof claimPaymentsPatchSchema>
export const claimPaymentsPatchValidator = getValidator(claimPaymentsPatchSchema, dataValidator)
export const claimPaymentsPatchResolver = resolve<ClaimPaymentsPatch, HookContext>({})
export const claimPaymentsQuerySchema = queryWrapper(claimPaymentsQueryProperties)
export type ClaimPaymentsQuery = Static<typeof claimPaymentsQuerySchema>
export const claimPaymentsQueryValidator = getValidator(claimPaymentsQuerySchema, queryValidator)
export const claimPaymentsQueryResolver = resolve<ClaimPaymentsQuery, HookContext>({})
