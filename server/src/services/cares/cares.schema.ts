// TypeBox schema for cares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper, commonPatch, imageSchema } from '../../utils/common/typebox-schemas.js'

export const caresSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  provider: Type.Optional(ObjectIdSchema()),
  session: Type.Optional(Type.String()),
  rx: Type.Optional(Type.Boolean()),
  prefContact: Type.Optional(Type.Union([Type.Literal("phone"), Type.Literal("email")])),
  providerName: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  zip: Type.Optional(Type.String()),
  msa: Type.Optional(Type.String()),
  income: Type.Optional(Type.Number()),
  hhCount: Type.Optional(Type.Number()),
  carrier: Type.Optional(Type.String()),
  assignedTo: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.Union([
    Type.Literal(1),
    Type.Literal(2),
    Type.Literal(3),
    Type.Literal(4),
    Type.Literal(5),
    Type.Literal(6),
  ])),
  disposition: Type.Optional(Type.Union([
    Type.Literal(1),
    Type.Literal(2),
    Type.Literal(3),
    Type.Literal(4),
    Type.Literal(5),
  ])),
  files: Type.Optional(imageSchema),
  savePercent: Type.Optional(Type.Number()),
  mandate: Type.Optional(Type.Object({
    acceptedAt: Type.Optional(Type.Any()),
    ip: Type.Optional(Type.String()),
    ua: Type.Optional(Type.String()),
    copy: Type.Optional(Type.String()),
    fingerprint: Type.Optional(ObjectIdSchema()),
    login: Type.Optional(ObjectIdSchema()),
    email: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    signature: Type.Optional(Type.String()),
  })),
  personRelationship: Type.Optional(Type.String()),
  hipaa: Type.Optional(Type.String()),
  signature: Type.Optional(Type.String()),
  original_price: Type.Optional(Type.Number()),
  est_price: Type.Optional(Type.Number()),
  prices: Type.Optional(Type.Record(Type.String(), Type.Object({
    price: Type.Optional(ObjectIdSchema()),
    rev_code: Type.Optional(Type.String()),
    status: Type.Optional(Type.Union([Type.Literal("data"), Type.Literal("confirmed")])),
    threads: Type.Optional(Type.Array(ObjectIdSchema())),
  }))),
  ...commonFields
}, { $id: 'Cares', additionalProperties: false })

export type Cares = Static<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({})
export const caresExternalResolver = resolve<Cares, HookContext>({})

export const caresDataSchema = Type.Object({
  ...Type.Omit(caresSchema, ['_id']).properties
}, { additionalProperties: false })

export type CaresData = Static<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const caresQueryProperties = Type.Pick(caresSchema, ['_id', 'person', 'plan', 'patient', 'provider', 'assignedTo', 'threads', 'fingerprint', 'login', 'price', 'threads'])

export const caresPatchSchema = commonPatch(caresSchema, ['$addToSet', '$pull'], caresQueryProperties)
export type CaresPatch = Static<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})
export const caresQuerySchema = queryWrapper(caresQueryProperties)
export type CaresQuery = Static<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
