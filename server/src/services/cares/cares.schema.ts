// TypeBox schema for cares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, imageSchema } from '../../utils/common/typebox-schemas.js'
import { paidSchema } from '../claims/utils/index.js'

export const caresSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: ObjectIdSchema(),
  org: Type.Optional(ObjectIdSchema()),
  plan: ObjectIdSchema(),
  status: Type.Optional(
    Type.Union([
      Type.Literal(0),
      Type.Literal(1),
      Type.Literal(2),
      Type.Literal(3),
      Type.Literal(4),
      Type.Literal(5),
    ])
  ),
  planPriority: Type.Optional(
    Type.Union([
      Type.Literal(0),
      Type.Literal(1),
      Type.Literal(2),
      Type.Literal(3),
      Type.Literal(4),
      Type.Literal(5),
    ])
  ),
  patientPriority: Type.Optional(
    Type.Union([
      Type.Literal(0),
      Type.Literal(1),
      Type.Literal(2),
      Type.Literal(3),
      Type.Literal(4),
      Type.Literal(5),
    ])
  ),
  providerPriority: Type.Optional(
    Type.Union([
      Type.Literal(0),
      Type.Literal(1),
      Type.Literal(2),
      Type.Literal(3),
      Type.Literal(4),
      Type.Literal(5),
    ])
  ),
  initDate: Type.Optional(Type.Any()),
  name: Type.Optional(Type.String()),
  targetDate: Type.Optional(Type.Any()),
  lastVisit: Type.Optional(Type.Any()),
  visits: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
  parent: Type.Optional(ObjectIdSchema()),
  children: Type.Optional(Type.Array(ObjectIdSchema())),
  patient: ObjectIdSchema(),
  related: Type.Optional(Type.Array(ObjectIdSchema())),
  conditions: Type.Optional(
    Type.Array(
      Type.Object({
        id: Type.Optional(ObjectIdSchema()),
        name: Type.Optional(Type.String()),
        medical_name: Type.Optional(Type.String()),
        code: Type.Optional(Type.String()),
        standard: Type.Optional(Type.String()),
        notes: Type.Optional(Type.String()),
        loggedAt: Type.Optional(Type.Any()),
        loggedBy: Type.Optional(ObjectIdSchema()),
      })
    )
  ),
  preventive: Type.Optional(Type.Boolean()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  total: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  balance: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  ...paidSchema.properties,
  ...commonFields
}, { $id: 'Cares', additionalProperties: false })

export type Cares = Static<typeof caresSchema>
export const caresValidator = getValidator(caresSchema, dataValidator)
export const caresResolver = resolve<Cares, HookContext>({})
export const caresExternalResolver = resolve<Cares, HookContext>({})

export const caresDataSchema = Type.Object({
  ...Type.Omit(caresSchema, ['_id']).properties
}, { additionalProperties: false })

export type CaresData = Static<typeof caresDataSchema>
export const caresDataValidator = getValidator(caresDataSchema, dataValidator)
export const caresDataResolver = resolve<CaresData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const caresQueryProperties = Type.Pick(caresSchema, ['_id', 'person', 'org', 'plan', 'parent', 'patient', 'visits', 'providers', 'practitioners', 'children', 'related', 'conditions', 'threads'])

export const caresPatchSchema = commonPatch(caresSchema, {pushPullOpts: [], pickedForSet: caresQueryProperties})
export type CaresPatch = Static<typeof caresPatchSchema>
export const caresPatchValidator = getValidator(caresPatchSchema, dataValidator)
export const caresPatchResolver = resolve<CaresPatch, HookContext>({})
export const caresQuerySchema = queryWrapper(caresQueryProperties)
export type CaresQuery = Static<typeof caresQuerySchema>
export const caresQueryValidator = getValidator(caresQuerySchema, queryValidator)
export const caresQueryResolver = resolve<CaresQuery, HookContext>({})
