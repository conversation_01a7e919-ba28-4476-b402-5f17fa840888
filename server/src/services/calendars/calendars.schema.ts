// TypeBox schema for calendars service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, rRuleSchema } from '../../utils/common/typebox-schemas.js'

const daySchedule = Type.Object(
    {
      all: Type.Optional(Type.Boolean()),
      times: Type.Optional(
          Type.Array(
              Type.Object(
                  {
                    start: Type.Optional(Type.Number()), // min/max commented out in source
                    end: Type.Optional(Type.Number()),
                  },
                  { additionalProperties: true }
              )
          )
      ),
    },
    { additionalProperties: true }
);

const scheduleSchema = Type.Object(
    {
      days: Type.Optional(
          Type.Object(
              {
                '0': Type.Optional(daySchedule),
                '1': Type.Optional(daySchedule),
                '2': Type.Optional(daySchedule),
                '3': Type.Optional(daySchedule),
                '4': Type.Optional(daySchedule),
                '5': Type.Optional(daySchedule),
                '6': Type.Optional(daySchedule),
              },
              { additionalProperties: true }
          )
      ),
      blackoutDates: Type.Optional(
          Type.Array(
              Type.Object(
                  {
                    start: Type.Optional(Type.String()),
                    end: Type.Optional(Type.String()),
                    recurrence: Type.Optional(rRuleSchema),
                  },
                  { additionalProperties: true }
              )
          )
      ),
    },
    { additionalProperties: true }
);

const notifySchema = Type.Object(
    {
      active: Type.Optional(Type.Boolean()),
      contactPath: Type.Optional(Type.String()),
      contact: Type.Optional(Type.String()),
    },
    { additionalProperties: true }
);

const notificationsSchema = Type.Object(
    {
      before: Type.Optional(Type.Number()),
      sms: Type.Optional(notifySchema),
      email: Type.Optional(notifySchema),
      internal: Type.Optional(notifySchema),
    },
    { additionalProperties: true }
);
export const calendarsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  ownerDefault: Type.Optional(Type.Boolean()),
  owner: Type.Optional(ObjectIdSchema()),
  ownerService: Type.Optional(Type.String()),
  editors: Type.Optional(Type.Array(ObjectIdSchema())),
  archived: Type.Optional(Type.Array(ObjectIdSchema())),
  past: Type.Optional(Type.Array(ObjectIdSchema())),
  future: Type.Optional(Type.Array(ObjectIdSchema())),
  notify: notificationsSchema,
  schedule: scheduleSchema,
  tokens: Type.Optional(Type.Object({
    google: Type.Optional(Type.String())
  })),
  ...commonFields
}, { $id: 'Calendars', additionalProperties: false })

export type Calendars = Static<typeof calendarsSchema>
export const calendarsValidator = getValidator(calendarsSchema, dataValidator)
export const calendarsResolver = resolve<Calendars, HookContext>({})
export const calendarsExternalResolver = resolve<Calendars, HookContext>({})

export const calendarsDataSchema = Type.Object({
  ...Type.Omit(calendarsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CalendarsData = Static<typeof calendarsDataSchema>
export const calendarsDataValidator = getValidator(calendarsDataSchema, dataValidator)
export const calendarsDataResolver = resolve<CalendarsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const calendarsQueryProperties = Type.Pick(calendarsSchema, ['_id', 'owner', 'editors', 'archived', 'past', 'future'])

export const calendarsPatchSchema = commonPatch(calendarsSchema, {pushPullOpts: [], pickedForSet: calendarsQueryProperties})
export type CalendarsPatch = Static<typeof calendarsPatchSchema>
export const calendarsPatchValidator = getValidator(calendarsPatchSchema, dataValidator)
export const calendarsPatchResolver = resolve<CalendarsPatch, HookContext>({})
export const calendarsQuerySchema = queryWrapper(calendarsQueryProperties)
export type CalendarsQuery = Static<typeof calendarsQuerySchema>
export const calendarsQueryValidator = getValidator(calendarsQuerySchema, queryValidator)
export const calendarsQueryResolver = resolve<CalendarsQuery, HookContext>({})
