// TypeBox schema for care-accounts service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

import { LastSyncSchema } from './utils/schemas.js'

// Main data model schema
export const careAccountsSchema = Type.Object({
  _id: ObjectIdSchema(),
  amount: Type.Optional(Type.Number()),
  approvers: Type.Optional(Type.Array(ObjectIdSchema())),
  assigned_amount: Type.Optional(Type.Number()),
  assigned_recurs: Type.Optional(Type.Number()),
  budgets: Type.Optional(Type.Array(ObjectIdSchema())),
  connect_id: Type.Optional(Type.String()),
  stripe_id: Type.Optional(Type.String()),
  moov_id: Type.Optional(Type.String()),
  wallet_id: Type.Optional(Type.String()),
  last4: Type.Optional(Type.String()),
  lastInc: Type.Optional(Type.String()),
  lastSync: LastSyncSchema,
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  members: Type.Optional(Type.Array(ObjectIdSchema())),
  ramp_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_whitelist: Type.Optional(Type.Array(Type.String())),
  mcc_blacklist: Type.Optional(Type.Array(Type.String())),
  name: Type.Optional(Type.String()),
  owner: ObjectIdSchema(),
  recurs: Type.Optional(Type.Number()),
  runSync: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  statusNote: Type.Optional(Type.String()),
  syncHistory: Type.Optional(Type.Array(LastSyncSchema)),
  ...commonFields
}, { $id: 'CareAccounts', additionalProperties: false })

export type CareAccounts = Static<typeof careAccountsSchema>
export const careAccountsValidator = getValidator(careAccountsSchema, dataValidator)
export const careAccountsResolver = resolve<CareAccounts, HookContext>({})
export const careAccountsExternalResolver = resolve<CareAccounts, HookContext>({})

// Schema for creating new data (excluding assigned_amount and assigned_recurs)
export const careAccountsDataSchema = Type.Object({
  ...Type.Omit(careAccountsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CareAccountsData = Static<typeof careAccountsDataSchema>
export const careAccountsDataValidator = getValidator(careAccountsDataSchema, dataValidator)
export const careAccountsDataResolver = resolve<CareAccountsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const careAccountsQueryProperties = Type.Pick(careAccountsSchema, ['_id', 'approvers', 'budgets', 'managers', 'members', 'owner'], { additionalProperties: true })

const pushPullOpts = [
  {path: 'syncHistory', type: LastSyncSchema},
  {path: 'mcc_whitelist', type: { type: 'string' }},
  {path: 'mcc_blacklist', type: { type: 'string' }},
  {path: 'budgets', type: ObjectIdSchema()}
]


export const careAccountsPatchSchema = commonPatch(careAccountsSchema, { pushPullOpts, pickedForSet: careAccountsQueryProperties })

export type CareAccountsPatch = Static<typeof careAccountsPatchSchema>
export const careAccountsPatchValidator = getValidator(careAccountsPatchSchema, dataValidator)
export const careAccountsPatchResolver = resolve<CareAccountsPatch, HookContext>({
  owner: async (val) => {
    return undefined // Prevent owner from being updated
  }
})

// Schema for allowed query properties
// Allow querying on any field from the main schema

export const careAccountsQuerySchema = Type.Intersect([
  queryWrapper(careAccountsQueryProperties),
  Type.Object({
    name: Type.Optional(Type.Any())
  }, { additionalProperties: false })
])

export type CareAccountsQuery = Static<typeof careAccountsQuerySchema>
export const careAccountsQueryValidator = getValidator(careAccountsQuerySchema, queryValidator)
export const careAccountsQueryResolver = resolve<CareAccountsQuery, HookContext>({})
