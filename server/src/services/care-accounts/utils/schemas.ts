import {ObjectIdSchema, Type} from '@feathersjs/typebox';

export const LastSyncSchema = Type.Optional(Type.Array(Type.Object({
    adjusted: Type.Optional(Type.Boolean()),
    balance: Type.Optional(Type.Number()),
    date: Type.Optional(Type.Any()),
    err: Type.Optional(Type.String()),
    amount: Type.Optional(Type.Number()),
    recurs: Type.Optional(Type.Number()),
    adjust_spent: Type.Optional(Type.Number()),
    adjust_spent_pending: Type.Optional(Type.Number()),
    adjust_amount: Type.Optional(Type.Number()),
    adjust_assigned_amount: Type.Optional(Type.Number()),
    adjust_recurs: Type.Optional(Type.Number()),
    adjust_assigned_recurs: Type.Optional(Type.Number()),
    freeze: Type.Optional(Type.Boolean()),
    excess: Type.Optional(Type.Number()),
    by: Type.Optional(ObjectIdSchema()),
})))
