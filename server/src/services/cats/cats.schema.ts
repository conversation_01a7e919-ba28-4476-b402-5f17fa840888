// TypeBox schema for cats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const catsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  avatar: Type.Optional(Type.String()),
  org: Type.Optional(ObjectIdSchema()),
  managers: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Cats = Static<typeof catsSchema>
export const catsValidator = getValidator(catsSchema, dataValidator)
export const catsResolver = resolve<Cats, HookContext>({})
export const catsExternalResolver = resolve<Cats, HookContext>({})

export const catsDataSchema = Type.Object({
  ...Type.Omit(catsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CatsData = Static<typeof catsDataSchema>
export const catsDataValidator = getValidator(catsDataSchema, dataValidator)
export const catsDataResolver = resolve<CatsData, HookContext>({})

export const catsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(catsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type CatsPatch = Static<typeof catsPatchSchema>
export const catsPatchValidator = getValidator(catsPatchSchema, dataValidator)
export const catsPatchResolver = resolve<CatsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const catsQueryProperties = Type.Object({
  ...Type.Pick(catsSchema, ['_id', 'org', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const catsQuerySchema = queryWrapper(catsQueryProperties)
export type CatsQuery = Static<typeof catsQuerySchema>
export const catsQueryValidator = getValidator(catsQuerySchema, queryValidator)
export const catsQueryResolver = resolve<CatsQuery, HookContext>({})
