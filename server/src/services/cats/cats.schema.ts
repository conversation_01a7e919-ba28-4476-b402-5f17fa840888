// TypeBox schema for cats service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch, imageSchema } from '../../utils/common/typebox-schemas.js'

export const catsSchema = Type.Object({
  _id: ObjectIdSchema(),
  avatar: Type.Optional(imageSchema),
  org: Type.Optional(ObjectIdSchema()),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  images: Type.Optional(imageSchema),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  code_regex: Type.Optional(Type.String()),
  conditions: Type.Optional(Type.Array(ObjectIdSchema())),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  meds: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: 'Cats', additionalProperties: true })

export type Cats = Static<typeof catsSchema>
export const catsValidator = getValidator(catsSchema, dataValidator)
export const catsResolver = resolve<Cats, HookContext>({})
export const catsExternalResolver = resolve<Cats, HookContext>({})

export const catsDataSchema = Type.Object({
  ...Type.Omit(catsSchema, ['_id']).properties
}, { additionalProperties: true })

export type CatsData = Static<typeof catsDataSchema>
export const catsDataValidator = getValidator(catsDataSchema, dataValidator)
export const catsDataResolver = resolve<CatsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const catsQueryProperties = Type.Pick(catsSchema, ['_id', 'org', 'managers', 'writers', 'conditions', 'procedures', 'meds'])
const pushPullOpts = 

export const catsPatchSchema = commonPatch(catsSchema, {pushPullOpts: [], pickedForSet: catsQueryProperties})
export type CatsPatch = Static<typeof catsPatchSchema>
export const catsPatchValidator = getValidator(catsPatchSchema, dataValidator)
export const catsPatchResolver = resolve<CatsPatch, HookContext>({})
export const catsQuerySchema = queryWrapper(catsQueryProperties)
export type CatsQuery = Static<typeof catsQuerySchema>
export const catsQueryValidator = getValidator(catsQuerySchema, queryValidator)
export const catsQueryResolver = resolve<CatsQuery, HookContext>({})
