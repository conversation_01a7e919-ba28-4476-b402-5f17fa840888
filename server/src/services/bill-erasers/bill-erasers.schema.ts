// TypeBox schema for bill-erasers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import {
  commonFields,
  queryWrapper,
  imageSchema,
  commonPatch
} from '../../utils/common/typebox-schemas.js'

export const billErasersSchema = Type.Object({
  _id: ObjectIdSchema(),
  person: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  session: Type.Optional(Type.String()),
  rx: Type.Optional(Type.Boolean()),
  prefContact: Type.Optional(Type.Union([Type.Literal("phone"), Type.Literal("email")])),
  providerName: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  state: Type.Optional(Type.String()),
  zip: Type.Optional(Type.String()),
  msa: Type.Optional(Type.String()),
  income: Type.Optional(Type.Number()),
  hhCount: Type.Optional(Type.Number()),
  carrier: Type.Optional(Type.String()),
  assignedTo: Type.Optional(Type.Array(ObjectIdSchema())),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  status: Type.Optional(Type.Union([
    Type.Literal(1),
    Type.Literal(2),
    Type.Literal(3),
    Type.Literal(4),
    Type.Literal(5),
    Type.Literal(6),
  ])),
  disposition: Type.Optional(Type.Union([
    Type.Literal(1),
    Type.Literal(2),
    Type.Literal(3),
    Type.Literal(4),
    Type.Literal(5),
  ])),
  files: Type.Optional(imageSchema),
  savePercent: Type.Optional(Type.Number()),
  mandate: Type.Optional(Type.Object({
    acceptedAt: Type.Optional(Type.Any()),
    ip: Type.Optional(Type.String()),
    ua: Type.Optional(Type.String()),
    copy: Type.Optional(Type.String()),
    fingerprint: Type.Optional(ObjectIdSchema()),
    login: Type.Optional(ObjectIdSchema()),
    email: Type.Optional(Type.String()),
    phone: Type.Optional(Type.String()),
    signature: Type.Optional(Type.String()),
  })),
  personRelationship: Type.Optional(Type.String()),
  hipaa: Type.Optional(Type.String()),
  signature: Type.Optional(Type.String()),
  original_price: Type.Optional(Type.Number()),
  est_price: Type.Optional(Type.Number()),
  prices: Type.Optional(Type.Record(Type.String(), Type.Object({
    price: Type.Optional(ObjectIdSchema()),
    rev_code: Type.Optional(Type.String()),
    status: Type.Optional(Type.Union([Type.Literal("data"), Type.Literal("confirmed")])),
    threads: Type.Optional(Type.Array(ObjectIdSchema())),
  }))),
  ...commonFields
}, { $id: 'BillErasers', additionalProperties: false })

export type BillErasers = Static<typeof billErasersSchema>
export const billErasersValidator = getValidator(billErasersSchema, dataValidator)
export const billErasersResolver = resolve<BillErasers, HookContext>({})
export const billErasersExternalResolver = resolve<BillErasers, HookContext>({})

export const billErasersDataSchema = Type.Object({
  ...Type.Omit(billErasersSchema, ['_id']).properties
}, { additionalProperties: false })

export type BillErasersData = Static<typeof billErasersDataSchema>
export const billErasersDataValidator = getValidator(billErasersDataSchema, dataValidator)
export const billErasersDataResolver = resolve<BillErasersData, HookContext>({})

const billErasersQueryProperties = Type.Pick(billErasersSchema, ['_id', 'person', 'plan', 'provider'])

const pushPull = [
  {path: 'files', type: imageSchema}
]

export const billErasersPatchSchema = commonPatch(billErasersSchema, { pushPullOpts: pushPull, pickedForSet: billErasersQueryProperties })
export type BillErasersPatch = Static<typeof billErasersPatchSchema>
export const billErasersPatchValidator = getValidator(billErasersPatchSchema, dataValidator)
export const billErasersPatchResolver = resolve<BillErasersPatch, HookContext>({})

// Allow querying on any field from the main schema
export const billErasersQuerySchema = queryWrapper(billErasersQueryProperties)
export type BillErasersQuery = Static<typeof billErasersQuerySchema>
export const billErasersQueryValidator = getValidator(billErasersQuerySchema, queryValidator)
export const billErasersQueryResolver = resolve<BillErasersQuery, HookContext>({})
