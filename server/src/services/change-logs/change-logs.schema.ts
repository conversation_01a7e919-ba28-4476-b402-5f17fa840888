// TypeBox schema for change-logs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const changeLogsSchema = Type.Object({
  _id: ObjectIdSchema(),
  service: Type.String(),
  recordId: ObjectIdSchema(),
  ...commonFields
}, { $id: 'ChangeLogs', additionalProperties: true })

export type ChangeLogs = Static<typeof changeLogsSchema>
export const changeLogsValidator = getValidator(changeLogsSchema, dataValidator)
export const changeLogsResolver = resolve<ChangeLogs, HookContext>({})
export const changeLogsExternalResolver = resolve<ChangeLogs, HookContext>({})

export const changeLogsDataSchema = Type.Object({
  ...Type.Omit(changeLogsSchema, ['_id']).properties
}, { additionalProperties: true })

export type ChangeLogsData = Static<typeof changeLogsDataSchema>
export const changeLogsDataValidator = getValidator(changeLogsDataSchema, dataValidator)
export const changeLogsDataResolver = resolve<ChangeLogsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const changeLogsQueryProperties = Type.Pick(changeLogsSchema, ['_id', 'recordId'])

export const changeLogsPatchSchema = commonPatch(changeLogsSchema, {pushPullOpts: [], pickedForSet: changeLogsQueryProperties})
export type ChangeLogsPatch = Static<typeof changeLogsPatchSchema>
export const changeLogsPatchValidator = getValidator(changeLogsPatchSchema, dataValidator)
export const changeLogsPatchResolver = resolve<ChangeLogsPatch, HookContext>({})
export const changeLogsQuerySchema = queryWrapper(changeLogsQueryProperties)
export type ChangeLogsQuery = Static<typeof changeLogsQuerySchema>
export const changeLogsQueryValidator = getValidator(changeLogsQuerySchema, queryValidator)
export const changeLogsQueryResolver = resolve<ChangeLogsQuery, HookContext>({})
