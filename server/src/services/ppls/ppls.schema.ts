// TypeBox schema for ppls service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, PhoneSchema, ServiceAddressSchema, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const pplsSchema = Type.Object({
  _id: ObjectIdSchema(),
  firstName: Type.String(),
  lastName: Type.String(),
  middleName: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(PhoneSchema),
  address: Type.Optional(ServiceAddressSchema),
  dateOfBirth: Type.Optional(Type.Any()),
  gender: Type.Optional(Type.String()),
  ssn: Type.Optional(Type.String()),
  login: Type.Optional(ObjectIdSchema()),
  household: Type.Optional(ObjectIdSchema()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  name: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Ppls = Static<typeof pplsSchema>
export const pplsValidator = getValidator(pplsSchema, dataValidator)
export const pplsResolver = resolve<Ppls, HookContext>({})
export const pplsExternalResolver = resolve<Ppls, HookContext>({})

export const pplsDataSchema = Type.Object({
  ...Type.Omit(pplsSchema, ['_id']).properties
}, { additionalProperties: false })

export type PplsData = Static<typeof pplsDataSchema>
export const pplsDataValidator = getValidator(pplsDataSchema, dataValidator)
export const pplsDataResolver = resolve<PplsData, HookContext>({})

export const pplsPatchSchema = Type.Object({
  ...Type.Partial(pplsDataSchema).properties,
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $addToSet: Type.Optional(Type.Object({
    cams: Type.Optional(ObjectIdSchema())
  ,
  // Missing MongoDB operators
  $push: Type.Optional(addToSet([])),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    cams: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false }))
}, { additionalProperties: false })
export type PplsPatch = Static<typeof pplsPatchSchema>
export const pplsPatchValidator = getValidator(pplsPatchSchema, dataValidator)
export const pplsPatchResolver = resolve<PplsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const pplsQueryProperties = Type.Object({
  ...Type.Pick(pplsSchema, ['_id', 'login', 'household', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const pplsQuerySchema = queryWrapper(pplsQueryProperties)
export type PplsQuery = Static<typeof pplsQuerySchema>
export const pplsQueryValidator = getValidator(pplsQuerySchema, queryValidator)
export const pplsQueryResolver = resolve<PplsQuery, HookContext>({})
