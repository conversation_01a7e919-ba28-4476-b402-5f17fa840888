import {Type} from '@feathersjs/typebox';

export const getClaimPrice = (claim:any) => {
    const { subtotal, taxes, fees } = claim || { total: 0 }
    let fee = 0;
    let tax = 0;
    for(const k in taxes || {}){
        tax += taxes[k].amount || 0;
    }
    for(const k in fees || {}){
        fee += fees[k].amount || 0;
    }
    return {
        subtotal: subtotal || 0,
        total: subtotal+fee+tax,
        tax,
        fee
    }
}
const paids = Type.Optional(
    Type.Object({
        amount: Type.Optional(Type.Number()),
        ded: Type.Optional(Type.Number()),
        coins: Type.Optional(Type.Number()),
    })
)

export const paidSchema = Type.Object({
    pending: paids,
    request: paids,
    offer: paids,
    paid: paids,
})
