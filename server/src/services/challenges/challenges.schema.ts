// TypeBox schema for challenges service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const challengesSchema = Type.Object({
  _id: ObjectIdSchema(),
  kind: Type.Optional(
    Type.Union([Type.Literal("registration"), Type.Literal("authentication")])
  ),
  login: Type.Optional(ObjectIdSchema()),
  challenge: Type.Optional(
    Type.String({
      description: "Opaque WebAuthn challenge (base64url or raw)",
    })
  ),
  connectionId: Type.Optional(
    Type.String({ description: "Feathers socket connection id" })
  ),
  expiresAt: Type.Optional(
    Type.Number({ description: "epoch ms; short TTL (e.g., 2–5 minutes)" })
  ),
  ...commonFields
}, { $id: 'Challenges', additionalProperties: false })

export type Challenges = Static<typeof challengesSchema>
export const challengesValidator = getValidator(challengesSchema, dataValidator)
export const challengesResolver = resolve<Challenges, HookContext>({})
export const challengesExternalResolver = resolve<Challenges, HookContext>({})

export const challengesDataSchema = Type.Object({
  ...Type.Omit(challengesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ChallengesData = Static<typeof challengesDataSchema>
export const challengesDataValidator = getValidator(challengesDataSchema, dataValidator)
export const challengesDataResolver = resolve<ChallengesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const challengesQueryProperties = Type.Pick(challengesSchema, ['_id', 'login'])

export const challengesPatchSchema = commonPatch(challengesSchema, {pushPullOpts: [], pickedForSet: challengesQueryProperties})
export type ChallengesPatch = Static<typeof challengesPatchSchema>
export const challengesPatchValidator = getValidator(challengesPatchSchema, dataValidator)
export const challengesPatchResolver = resolve<ChallengesPatch, HookContext>({})
export const challengesQuerySchema = queryWrapper(challengesQueryProperties)
export type ChallengesQuery = Static<typeof challengesQuerySchema>
export const challengesQueryValidator = getValidator(challengesQuerySchema, queryValidator)
export const challengesQueryResolver = resolve<ChallengesQuery, HookContext>({})
