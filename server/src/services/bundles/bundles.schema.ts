// TypeBox schema for bundles service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const bundlesSchema = Type.Object({
  _id: ObjectIdSchema(),
  provider: ObjectIdSchema(),
  public: Type.Optional(Type.Boolean()),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  price: Type.Optional(Type.Number()),
  locations: Type.Optional(Type.Array(Type.String())),
  prices: Type.Optional(Type.Array(ObjectIdSchema())),
  cats: Type.Optional(Type.Array(ObjectIdSchema())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  managers: Type.Optional(Type.Array(ObjectIdSchema())),
  writers: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: 'Bundles', additionalProperties: false })

export type Bundles = Static<typeof bundlesSchema>
export const bundlesValidator = getValidator(bundlesSchema, dataValidator)
export const bundlesResolver = resolve<Bundles, HookContext>({})
export const bundlesExternalResolver = resolve<Bundles, HookContext>({})

export const bundlesDataSchema = Type.Object({
  ...Type.Omit(bundlesSchema, ['_id']).properties
}, { additionalProperties: false })

export type BundlesData = Static<typeof bundlesDataSchema>
export const bundlesDataValidator = getValidator(bundlesDataSchema, dataValidator)
export const bundlesDataResolver = resolve<BundlesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const bundlesQueryProperties = Type.Pick(bundlesSchema, ['_id', 'provider', 'plans', 'prices', 'cats', 'networks', 'managers', 'writers'])
const pushPullOpts = [
  { path: 'prices', type: ObjectIdSchema() },
  { path: 'networks', type: ObjectIdSchema() },
  { path: 'managers', type: ObjectIdSchema() },
  { path: 'writers', type: ObjectIdSchema() },
  { path: 'cats', type: ObjectIdSchema() },
  { path: 'plans', type: ObjectIdSchema() },
]

export const bundlesPatchSchema = commonPatch(bundlesSchema, { pushPullOpts, pickedForSet: bundlesQueryProperties })
export type BundlesPatch = Static<typeof bundlesPatchSchema>
export const bundlesPatchValidator = getValidator(bundlesPatchSchema, dataValidator)
export const bundlesPatchResolver = resolve<BundlesPatch, HookContext>({})
export const bundlesQuerySchema = queryWrapper(bundlesQueryProperties)
export type BundlesQuery = Static<typeof bundlesQuerySchema>
export const bundlesQueryValidator = getValidator(bundlesQuerySchema, queryValidator)
export const bundlesQueryResolver = resolve<BundlesQuery, HookContext>({})
