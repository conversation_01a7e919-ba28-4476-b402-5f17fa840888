// TypeBox schema for logins service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, phoneSchema, queryWrapper } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const loginsSchema = Type.Object({
  _id: ObjectIdSchema(),
  email: Type.String(),
  name: Type.Optional(Type.String()),
  did: Type.Optional(Type.String()),
  password: Type.Optional(Type.String()),
  phone: Type.Optional(phoneSchema),
  person: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  verified: Type.Optional(Type.Boolean()),
  isVerified: Type.Optional(Type.Boolean()),
  emailVerified: Type.Optional(Type.Boolean()),
  phoneVerified: Type.Optional(Type.Boolean()),
  twoFactorEnabled: Type.Optional(Type.Boolean()),
  lastLogin: Type.Optional(Type.Any()),
  loginCount: Type.Optional(Type.Number()),
  failedAttempts: Type.Optional(Type.Number()),
  lockedUntil: Type.Optional(Type.Any()),
  resetToken: Type.Optional(Type.String()),
  resetTokenExpires: Type.Optional(Type.Any()),
  verificationToken: Type.Optional(Type.String()),
  verificationTokenExpires: Type.Optional(Type.Any()),
  roles: Type.Optional(Type.Array(Type.String())),
  permissions: Type.Optional(Type.Array(Type.String())),
  orgs: Type.Optional(Type.Array(ObjectIdSchema())),
  groups: Type.Optional(Type.Array(ObjectIdSchema())),
  teams: Type.Optional(Type.Array(ObjectIdSchema())),
  preferences: Type.Optional(Type.Record(Type.String(), Type.Any())),
  settings: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  suspended: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Logins = Static<typeof loginsSchema>
export const loginsValidator = getValidator(loginsSchema, dataValidator)
export const loginsResolver = resolve<Logins, HookContext>({})
export const loginsExternalResolver = resolve<Logins, HookContext>({})

// Schema for creating new data
export const loginsDataSchema = Type.Object({
  ...Type.Omit(loginsSchema, ['_id']).properties
}, { additionalProperties: false })

export type LoginsData = Static<typeof loginsDataSchema>
export const loginsDataValidator = getValidator(loginsDataSchema, dataValidator)
export const loginsDataResolver = resolve<LoginsData, HookContext>({})

// Schema for updating existing data
export const loginsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(loginsSchema, ['_id'])).properties,
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
  $push: Type.Optional(Type.Object({
    roles: Type.Optional(Type.String()),
    permissions: Type.Optional(Type.String()),
    orgs: Type.Optional(ObjectIdSchema()),
    groups: Type.Optional(ObjectIdSchema()),
    teams: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    roles: Type.Optional(Type.String()),
    permissions: Type.Optional(Type.String()),
    orgs: Type.Optional(ObjectIdSchema()),
    groups: Type.Optional(ObjectIdSchema()),
    teams: Type.Optional(ObjectIdSchema())
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type LoginsPatch = Static<typeof loginsPatchSchema>
export const loginsPatchValidator = getValidator(loginsPatchSchema, dataValidator)
export const loginsPatchResolver = resolve<LoginsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const loginsQueryProperties = Type.Object({
  ...Type.Pick(loginsSchema, ['_id', 'person', 'fingerprint', 'orgs', 'groups', 'teams', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const loginsQuerySchema = queryWrapper(loginsQueryProperties)

export type LoginsQuery = Static<typeof loginsQuerySchema>
export const loginsQueryValidator = getValidator(loginsQuerySchema, queryValidator)
export const loginsQueryResolver = resolve<LoginsQuery, HookContext>({})
