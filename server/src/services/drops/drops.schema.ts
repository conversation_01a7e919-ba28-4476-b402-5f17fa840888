// TypeBox schema for drops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const dropsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  tags: Type.Optional(Type.Array(Type.String())),
}, { additionalProperties: false })

export type Drops = Static<typeof dropsSchema>
export const dropsValidator = getValidator(dropsSchema, dataValidator)
export const dropsResolver = resolve<Drops, HookContext>({})
export const dropsExternalResolver = resolve<Drops, HookContext>({})

export const dropsDataSchema = Type.Object({
  ...Type.Omit(dropsSchema, ['_id']).properties
}, { additionalProperties: false })

export type DropsData = Static<typeof dropsDataSchema>
export const dropsDataValidator = getValidator(dropsDataSchema, dataValidator)
export const dropsDataResolver = resolve<DropsData, HookContext>({})

export const dropsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(dropsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type DropsPatch = Static<typeof dropsPatchSchema>
export const dropsPatchValidator = getValidator(dropsPatchSchema, dataValidator)
export const dropsPatchResolver = resolve<DropsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const dropsQueryProperties = Type.Object({
  ...Type.Pick(dropsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const dropsQuerySchema = queryWrapper(dropsQueryProperties)
export type DropsQuery = Static<typeof dropsQuerySchema>
export const dropsQueryValidator = getValidator(dropsQuerySchema, queryValidator)
export const dropsQueryResolver = resolve<DropsQuery, HookContext>({})
