{"bill-erasers": {"prices": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"price": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "rev_code": {"type": "string"}, "status": {"type": "string", "enum": ["data", "confirmed"]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}, "bills": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "cams": {"extras": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"due": {"type": "string"}, "awarded": {"type": "string"}, "off": {"type": "boolean"}, "banks": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat", "units"]}, "unit": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "amount": {"type": "number"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "terms": {"type": "string"}, "limit": {"type": "number"}}}}}}, "caps": {"caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"description": {"type": "string"}, "ucan": {"type": "string"}, "logins": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}, "cares": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "claim-payments": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "claim-reqs": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}, "claimData.taxes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "claimData.fees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "claimData.files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "claims": {"taxes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "fees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "cobras": {"coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"recurs": {"type": "number"}, "participants": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}, "comps": {"stages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"label": {"type": "string"}, "color": {"type": "string"}}}}}, "extras": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"due": {"type": "string"}, "awarded": {"type": "string"}, "off": {"type": "boolean"}, "banks": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat", "units"]}, "unit": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "amount": {"type": "number"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "terms": {"type": "string"}, "limit": {"type": "number"}}}}}}, "contracts": {"parties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"relationship": {"type": "string"}, "by": {"type": "string"}, "byTitle": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "idService": {"type": "string"}, "aka": {"type": "string"}, "tag": {"type": "string"}, "legalName": {"type": "string"}, "address": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "ack": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}}}}}, "meta.pay.refSplit": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"percent": {"type": "number"}, "ref": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}, "sections[^([0-9]|[1-9][0-9]|1[01][0-9]|120)$].sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}, "coverages": {"vectorIds": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadIds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}}}}}, "coins": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "deductibles": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "copays": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "premium.rateByAge": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "premium.fixedRates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "premium.multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "premium.weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "moops": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "catsBlacklist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "memo": {"type": "string"}}}}}, "av": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"value": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "at": {}}}}}, "networks": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"coins_discount": {"type": "number"}, "coins_discount_type": {"type": "string", "enum": ["percent", "flat"]}, "ded_discount": {"type": "number"}, "ded_discount_type": {"type": "string", "enum": ["percent", "flat"]}}}}}}, "cross-sections": {"sections": {"type": "object", "patternProperties": {"^.*$": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "doc-requests": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "doc-templates": {"sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}, "sections[^([0-9]|[1-9][0-9]|1[01][0-9]|120)$].sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}, "enrollments": {"enrolled": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"age": {"type": "number"}, "dob": {"type": "string"}, "ssn": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "gender": {"type": "string"}, "relation": {"type": "string", "enum": ["self", "spouse", "child", "father", "mother", "grandfather", "grandmother", "grandson", "granddaughter", "son_in_law", "daughter_in_law", "uncle", "aunt", "nephew", "niece", "cousin", "guardian", "stepparent", "stepson", "stepdaughter", "adopted_child", "foster_child", "sister", "brother", "brother_in_law", "sister_in_law", "mother_in_law", "father_in_law", "ward", "sponsored_dependent", "dependent_minor_dependent", "ex_spouse", "court_appointed_guardian", "collateral_dependent", "life_partner", "annultant", "trustee", "other_relationship", "other_relative"]}, "zip": {"type": "string"}, "point": {"type": "array", "items": {"type": "number"}}, "monthsSinceSmoked": {"type": "number"}, "dependent": {"type": "boolean"}, "disabled": {"type": "boolean"}, "annualIncome": {"type": "number"}, "incarcerated": {"type": "boolean"}}}}}, "patientClaims": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}}}}}}}, "patientClaims[^.*$]": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}}}}}, "coverageClaims": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}}}}}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"ichra": {"type": "boolean"}, "shop": {"type": "boolean"}, "recurs": {"type": "number"}, "card": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "confirmedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "confirmedAt": {}, "confirmData": {"type": "object", "properties": {"aptc": {"type": "number"}, "policy_id": {"type": "string"}, "premium": {"type": "number"}, "income": {"type": "number"}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}, "participants": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "premium": {"type": "number"}, "participants_last": {"type": "number"}, "coverageType": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"]}, "postTax": {"type": "boolean"}, "policy": {"type": "string"}, "fullPolicy": {}, "ptc": {"type": "number"}, "individual_coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fullCoverage": {}, "optOut": {}, "optOutDisclosure": {"type": "string"}, "providers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "status": {"type": "number", "enum": [1, 2, 3, 4]}, "type": {"type": "string", "enum": ["individual", "family"]}, "slcsp": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "premium": {"type": "number"}}}, "aptc": {"type": "object", "properties": {"attest": {"type": "string"}, "income": {"type": "number"}, "aptc": {"type": "number"}, "hardship_exemption": {"type": "boolean"}, "in_coverage_gap": {"type": "boolean"}, "is_medicaid_chip": {"type": "boolean"}}}, "annualSpend": {"type": "number"}, "rxSpend": {"type": "number"}, "issuerBlacklist": {"type": "array", "items": {"type": "string"}}, "typeBlacklist": {"type": "array", "items": {"type": "string"}}, "conditions": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "procedures": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "meds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "coverages[^.*$].files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}, "contributions.byPlan": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "contributions.byCoverage": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"employer": {"type": "number"}, "employee": {"type": "number"}}}}}}, "funds": {"nominees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "statusUpdates": {"type": "array", "items": {"type": "object", "properties": {"from": {"type": "number"}, "to": {"type": "number"}, "at": {}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}}, "gps": {"coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "compare_id": {"type": "string"}, "similar": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}}, "mostSimilar": {"type": "string"}, "carrierName": {"type": "string", "$comment": "name of the insurance company"}, "webpage": {"type": "string", "$comment": "link to details webpage if available"}, "name": {"type": "string", "$comment": "name of the coverage"}, "openNetwork": {"type": "boolean"}, "plan_type": {"type": "string", "$comment": "network type such as HMO, PPO, EPO, POS"}, "type": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"], "$comment": "major medical, health share, direct care, excepted benefit"}, "description": {"type": "string", "$comment": "brief coverage description"}, "hsaQualified": {"type": "boolean", "$comment": "high deductible health plan - eligible for HSA contributions"}, "productDetailRef": {"type": "string", "$comment": "For health shares - this is the health share `_id:product_id`"}, "fortyPremium": {"type": "number"}, "maxAge": {"type": "number"}, "preventive": {"type": "boolean"}, "coinsurance": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}, "coins": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "deductible": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "deductibles": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "cap": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "caps": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "copays": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "premium": {"type": "object", "$comment": "Choose one of the premium structures flatPremium, rateByAge, fixedRates. The remaining properties are utilities for adjusting rates based on age, household count, etc.", "properties": {"flatPremium": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}, "$comment": "if the premium is a flat number per person, no age banding, no multi-person discount"}, "rateByAge": {"type": "object", "$comment": "flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "fixedRates": {"type": "object", "$comment": "this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "rateType": {"type": "string", "enum": ["flatPremium", "rateByAge", "fixedRates"], "$comment": "which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees"}, "multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc."}, "weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "A complex option for weighting rates by age such that multiDiscount doesn't apply equally to all ages"}, "baseDefault": {"type": "boolean", "$comment": "To be used with flatPremium - if true, this is the default rate if no rates are found"}, "breakpointAges": {"type": "array", "items": {"type": "number"}, "$comment": "This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates"}, "rateBreak": {"type": "string", "enum": ["graduated", "breakpoint"], "$comment": "Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property"}, "smokerFactor": {"type": "number", "$comment": "increase percentage for smoking status ie: 1.5 would be 50% more for smokers"}}}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "rates": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "special rate areas - to be added by a user later"}, "deductibleType": {"type": "string", "enum": ["annual", "event"], "$comment": "deprecated - now see deductible.type"}, "moop": {"type": "object", "$comment": "medical or combined moop amount for individuals and families respectively", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "moops": {"$comment": "any key with dedSchema as the details of the moop", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "monthsSinceSmoked": {"type": "number", "$comment": "at how many months someone is considered non-tobacco user"}, "catsBlacklist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "memo": {"type": "string"}}}}}, "covered": {"type": "string", "enum": ["individual", "group"]}, "knownKeys": {"type": "array", "items": {"type": "string"}}, "files": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}, "fromFile": {"type": "boolean"}}}}}, "coverages[^.*$].similar": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}}, "coverages[^.*$].coins": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "coverages[^.*$].deductibles": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "coverages[^.*$].caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "coverages[^.*$].copays": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "coverages[^.*$].premium.rateByAge": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "coverages[^.*$].premium.fixedRates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "coverages[^.*$].premium.multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "coverages[^.*$].premium.weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "coverages[^.*$].benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "coverages[^.*$].moops": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "coverages[^.*$].catsBlacklist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "memo": {"type": "string"}}}}}, "employerContributionReports": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "gps": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "updatedAt": {}, "data": {"type": "object", "properties": {"amount": {"type": "number"}, "family": {"type": "number"}, "match": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat"]}, "percentType": {"type": "string", "enum": ["cost", "income"]}, "postTax": {"type": "number"}, "includeExtras": {"type": "boolean"}}}}}}}}, "health-shares": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}, "products": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "guidelines": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}, "vectorStore": {"type": "object", "properties": {"id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}, "resetId": {"type": "string"}}}}}}}, "financials": {"type": "object", "patternProperties": {"^d{4}$": {"$comment": "Key is the year", "type": "object", "properties": {"total_revenue": {"type": "number"}, "sharing_expense": {"type": "number"}, "admin_expense": {"type": "number"}, "net_assets_start": {"type": "number"}, "net_assets": {"type": "number"}, "cash_on_hand": {"type": "number"}, "highest_paid_executive": {"type": "number"}}}}}}, "hosts": {"shopStatuses": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"label": {"type": "string"}, "color": {"type": "string"}}}}}, "plans": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"team": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "payContract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "videos.intro": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}, "title": {"type": "string"}, "author_name": {"type": "string"}, "author_url": {"type": "string"}, "type": {"type": "string"}, "height": {}, "width": {}, "version": {}, "provider_name": {"type": "string"}, "provider_url": {"type": "string"}, "thumbnail_height": {"type": "number"}, "thumbnail_width": {"type": "number"}, "thumbnail_url": {"type": "string"}}}}}, "states": {"type": "object", "patternProperties": {"[A-Z]{2}": {"type": "object", "properties": {"state": {"type": "string"}, "counties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"all": {"type": "boolean"}, "cities": {"type": "array", "items": {"type": "string"}}}}}}, "all": {"type": "boolean"}}}}}, "states[[A-Z]{2}].counties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"all": {"type": "boolean"}, "cities": {"type": "array", "items": {"type": "string"}}}}}}}, "households": {"qual_events": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}}, "incomes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}, "off": {"type": "boolean"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "class": {"type": "string", "enum": ["ee", "ic"]}, "estHours": {"type": "number"}}}}}, "deductions": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"off": {"type": "boolean"}, "amount": {"type": "number"}, "atl": {"type": "boolean"}}}}}, "members": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"relation": {"type": "string", "enum": ["self", "spouse", "child", "father", "mother", "grandfather", "grandmother", "grandson", "granddaughter", "son_in_law", "daughter_in_law", "uncle", "aunt", "nephew", "niece", "cousin", "guardian", "stepparent", "stepson", "stepdaughter", "adopted_child", "foster_child", "sister", "brother", "brother_in_law", "sister_in_law", "mother_in_law", "father_in_law", "ward", "sponsored_dependent", "dependent_minor_dependent", "ex_spouse", "court_appointed_guardian", "collateral_dependent", "life_partner", "annultant", "trustee", "other_relationship", "other_relative"]}, "dependent": {"type": "boolean"}, "annualIncome": {"type": "number"}, "address": {"type": "object", "additionalProperties": true, "properties": {"id": {"type": "string"}, "address1": {"type": "string"}, "address2": {"type": "string"}, "formatted": {"type": "string"}, "postal": {"type": "string"}, "city": {"type": "string"}, "region": {"type": "string"}, "country": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "googleAddress": {"type": "object"}, "name": {"type": "string"}, "tags": {"type": "object"}, "type": {"type": "object"}}}, "monthsSinceSmoked": {"type": "number"}, "disabled": {"type": "boolean"}, "incarcerated": {"type": "boolean"}, "latino": {"type": "boolean"}, "native": {"type": "boolean"}, "pregnant": {"type": "boolean"}, "us_citizen": {"type": "boolean"}, "adl_assist": {"type": "boolean"}, "medicaid": {"type": "boolean"}, "medicaid_ineligible": {"type": "boolean"}, "outside_coverage": {"type": "boolean"}, "outside_coverage_end": {"type": "string"}, "job_coverage": {"type": "boolean"}}}}}}, "ims": {"support": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fp": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "sendTo": {"type": "string", "enum": ["in-app", "phone", "email"]}, "lastAt": {}, "status": {"type": "number", "enum": [0, 1, 2]}, "offline": {"type": "boolean"}}}}}, "messages.items.errs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"code": {"type": "number"}, "message": {"type": "string"}, "pid": {"type": "string"}}}}}}, "markets": {"locations": {"type": "object", "patternProperties": {"[A-Z]{2}": {"type": "object", "properties": {"cities": {"type": "array", "items": {"type": "string"}}, "zips": {"type": "array", "items": {"type": "string"}}, "counties": {"type": "array", "items": {"type": "string"}}}}}}}, "meds": {"info.IN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.PIN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.MIN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SCD": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SCDF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SCDG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SCDC": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.GPCK": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.BN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.BPCK": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.DF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.DFG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SBD": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SBDG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SBDC": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SBDF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "info.SBDFP": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}}, "orgs": {"bankAccounts": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "default": {"type": "boolean"}}}}}, "asg": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["A", "B"]}, "orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["A", "B", "FSO", "M"]}}}}}}}}}, "asg[^.*$].orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["A", "B", "FSO", "M"]}}}}}, "controls": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"identical": {"type": "number"}, "common": {"type": "number"}, "control": {"type": "boolean"}, "orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"identical": {"type": "number"}, "owners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "parent": {}, "total": {"type": "number"}}}}}, "brotherSister": {"type": "boolean"}, "parentSub": {"type": "boolean"}}}}}, "controls[^.*$].orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"identical": {"type": "number"}, "owners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "parent": {}, "total": {"type": "number"}}}}}, "groups": {"type": "object", "patternProperties": {"^.*$": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "plan-docs": {"sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}, "sections[^([0-9]|[1-9][0-9]|1[01][0-9]|120)$].sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}, "plans": {"vectorStoreIds": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}}}}}, "team": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "string", "enum": ["pending", "canceled", "active"]}, "role": {"type": "string", "enum": ["care_director", "plan_guide", "compliance", "finance", "physician"]}, "roleDescription": {"type": "string"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}, "feeDescription": {"type": "string"}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "conflicts": {"type": "string"}, "approvedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "approvedAt": {}}}}}, "enrollments": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"active": {"type": "boolean"}, "description": {"type": "string"}, "open": {"type": "string"}, "close": {"type": "string"}, "enrolled": {"type": "number"}, "lastUpdate": {}, "lastEnrolled": {"type": "string"}, "contributions": {"type": "object", "properties": {"lastAutoSet": {}, "lastManualSet": {}, "employer": {"type": "object", "properties": {"cafe": {"type": "number"}, "coverages": {"type": "number"}}}, "employee": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "needed": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "byPlan": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "byCoverage": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"employer": {"type": "number"}, "employee": {"type": "number"}}}}}}}, "open_enroll": {"type": "boolean"}, "ppls": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "groups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "sentThrough": {"type": "number"}}}}}, "enrollments[^.*$].contributions.byPlan": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "enrollments[^.*$].contributions.byCoverage": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"employer": {"type": "number"}, "employee": {"type": "number"}}}}}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string"}, "groups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "card": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "employeeBudget": {"type": "boolean"}, "employerContribution": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}, "type": {"type": "string", "enum": ["flat", "percent"]}}}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "employerContribution": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"amount": {"type": "number"}, "family": {"type": "number"}, "match": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat"]}, "percentType": {"type": "string", "enum": ["cost", "income"]}, "postTax": {"type": "number"}, "includeExtras": {"type": "boolean"}}}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "ppls": {"moovAccounts": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "isController": {"type": "boolean"}}}}}, "invites": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "at": {}, "reminded": {"type": "array", "items": {}}, "caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "required": ["id", "path"], "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "path": {"type": "string", "$comment": "This is the path of the caps.cap permission set for this person"}}}}}}}}}, "invites[^.*$].caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "required": ["id", "path"], "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "path": {"type": "string", "$comment": "This is the path of the caps.cap permission set for this person"}}}}}}, "practitioners": {"providers": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "providers": {"videos.general": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}, "title": {"type": "string"}, "author_name": {"type": "string"}, "author_url": {"type": "string"}, "type": {"type": "string"}, "height": {}, "width": {}, "version": {}, "provider_name": {"type": "string"}, "provider_url": {"type": "string"}, "thumbnail_height": {"type": "number"}, "thumbnail_width": {"type": "number"}, "thumbnail_url": {"type": "string"}}}}}, "videos.memberships": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}, "title": {"type": "string"}, "author_name": {"type": "string"}, "author_url": {"type": "string"}, "type": {"type": "string"}, "height": {}, "width": {}, "version": {}, "provider_name": {"type": "string"}, "provider_url": {"type": "string"}, "thumbnail_height": {"type": "number"}, "thumbnail_width": {"type": "number"}, "thumbnail_url": {"type": "string"}}}}}, "videos.bundles": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}, "title": {"type": "string"}, "author_name": {"type": "string"}, "author_url": {"type": "string"}, "type": {"type": "string"}, "height": {}, "width": {}, "version": {}, "provider_name": {"type": "string"}, "provider_url": {"type": "string"}, "thumbnail_height": {"type": "number"}, "thumbnail_width": {"type": "number"}, "thumbnail_url": {"type": "string"}}}}}, "regularHours": {"type": "object", "patternProperties": {"^[0-6]$": {"type": "object", "properties": {"open": {"type": "object", "properties": {"hour": {"type": "number"}, "minute": {"type": "number"}}}, "close": {"type": "object", "properties": {"hour": {"type": "number"}, "minute": {"type": "number"}}}}}}}}, "rates": {"areas.items.premium.rateByAge": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "areas.items.premium.fixedRates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "areas.items.premium.multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "areas.items.premium.weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "premium.rateByAge": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "premium.fixedRates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "premium.multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}, "premium.weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}}}, "refs": {"teams": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"calendar": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "se-plans": {"rating_areas": {"type": "object", "patternProperties": {"^d+$": {"type": "object", "properties": {"name": {"type": "string"}, "zips": {"type": "array", "items": {"type": "string"}}, "fips": {"type": "array", "items": {"type": "string"}}, "cities": {"type": "array", "items": {"type": "string"}}, "county": {"type": "string"}, "rates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}}}}}, "rating_areas[^d+$].rates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "csr": {"type": "object", "patternProperties": {"^(02|03|04)$": {"type": "object", "properties": {"acaPlan": {"type": "boolean"}, "plan_id": {"type": "string"}, "state_code": {"type": "string"}, "business_year": {"type": "number"}, "import_date": {}, "rate_expiration": {}, "rate_effective_date": {}, "state_plan_id": {"type": "string"}, "issuer_id": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "premium": {"type": "number"}, "aptc_eligible_premium": {"type": "number"}, "eligible_dependents": {"type": "array", "items": {"type": "string"}}, "hsa_eligible": {"type": "boolean"}, "carrierName": {"type": "string"}, "carrierLogo": {"type": "string"}, "plan_type": {"type": "string"}, "benefits_url": {"type": "string"}, "formulary_url": {"type": "string"}, "network_url": {"type": "string"}, "brochure_url": {"type": "string"}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "exclusions": {"type": "string"}, "on_exchange": {"type": "boolean"}, "off_exchange": {"type": "boolean"}, "tobacco_lookback": {"type": "number"}, "tobacco": {"type": "string"}, "moop": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "deductible": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "metal": {"type": "string"}, "coins": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}, "copay": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}}}}}, "csr[^(02|03|04)$].benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "shops": {"attest": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}}}, "spend_dist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"spend": {"type": "number"}, "count": {"type": "number"}}}}}, "distribution": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}}}, "distribution[^.*$]": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "distribution_ptc": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}}}, "distribution_ptc[^.*$]": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "choices": {"type": "object", "patternProperties": {"^.*$": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "specs": {"changes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"newVal": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}]}, "oldVal": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}]}}}}}}, "visits": {"files": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"uploadId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fileId": {"type": "string"}, "storage": {"type": "string"}, "appPath": {"anyOf": [{"type": "boolean"}, {"type": "null"}]}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "subPath": {"anyOf": [{"type": "null"}, {"type": "array", "items": {"type": "string"}}]}, "url": {"type": "string"}}}}}}, "wallets": {"methods": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}}}