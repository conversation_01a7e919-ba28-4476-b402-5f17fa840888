{"ai-chats": {"$id": "AiChats", "type": "object", "additionalProperties": false, "required": ["_id", "subject", "chatName", "person"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "chatName": {"type": "string"}, "chatId": {"type": "string"}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "chats": {"type": "array", "items": {"type": "array", "items": {"type": "object", "properties": {"session": {"type": "string"}, "createdAt": {}, "subject": {"type": "string", "enum": ["plan_docs", "contracts", "coverages", "medical", "shops"]}, "question": {"type": "string"}, "annotations": {"type": "array", "items": {}}, "answer": {"type": "string"}}}}}}}, "bank-accounts": {"$id": "BankAccounts", "type": "object", "additionalProperties": false, "required": ["_id", "owner", "type", "accountNumber", "routingNumber"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "nickname": {"type": "string"}, "hsa": {"type": "boolean"}, "type": {"type": "string", "enum": ["business", "individual"]}, "description": {"type": "string"}, "terms_accepted": {"type": "string"}, "business_rep": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "accountNumber": {"type": "string"}, "last4": {"type": "string"}, "routingNumber": {"type": "string"}, "accountType": {"type": "string", "enum": ["checking", "savings"]}, "bankName": {"type": "string"}, "canAch": {"type": "boolean"}, "canWire": {"type": "boolean"}, "verification": {"type": "object", "properties": {"exceptionDetails": {"type": "object", "properties": {"achReturnCode": {"type": "string"}, "description": {"type": "string"}, "rtpRejectionCode": {"type": "string"}}}, "code": {"type": "string"}, "verified": {"type": "boolean"}, "status": {"type": "string", "enum": ["new", "sent-credit", "failed", "not-sent", "verified", "max-attempts-exceeded", "expired", "successful"]}, "verificationMethod": {"type": "string", "enum": ["instant", "ach", "micro"]}}}, "mandate": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}}}, "setupIntent": {"type": "string"}, "latestPaymentIntent": {"type": "string"}, "fca": {"type": "string"}, "moov_link_id": {"type": "string"}, "status": {"type": "string"}}}, "bill-erasers": {"$id": "Bill<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "session": {"type": "string"}, "rx": {"type": "boolean"}, "prefContact": {"type": "string", "enum": ["phone", "email"]}, "providerName": {"type": "string"}, "notes": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "msa": {"type": "string"}, "income": {"type": "number"}, "hhCount": {"type": "number"}, "carrier": {"type": "string"}, "assignedTo": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "status": {"type": "number", "enum": [1, 2, 3, 4, 5, 6]}, "disposition": {"type": "number", "enum": [1, 2, 3, 4, 5]}, "files": {"$comment": "***imageSchema used here***"}, "savePercent": {"type": "number"}, "mandate": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}, "personRelationship": {"type": "string"}, "hipaa": {"type": "string"}, "signature": {"type": "string"}, "original_price": {"type": "number"}, "est_price": {"type": "number"}, "prices": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"price": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "rev_code": {"type": "string"}, "status": {"type": "string", "enum": ["data", "confirmed"]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}, "bills": {"$id": "Bills", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "to": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "toName": {"type": "string"}, "toModel": {"type": "string", "enum": ["orgs", "ppls"]}, "toEmail": {"type": "string"}, "toAddress": {"$comment": "***addressSchema used here***"}, "toPhone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "from": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fromName": {"type": "string"}, "formModel": {"type": "string", "enum": ["orgs", "ppls"]}, "fromEmail": {"type": "string"}, "fromAddress": {"$comment": "***addressSchema used here***"}, "fromPhone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "logo": {"$comment": "***imageSchema used here***"}, "billDate": {}, "dueDate": {}, "currency": {"type": "string", "enum": ["usd"]}, "lastTaxTotal": {"type": "number"}, "lineItems": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "amount": {"type": "number"}, "code": {"type": "string"}, "itemId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "itemService": {"type": "string"}, "qty": {"type": "number"}, "settings": {"type": "object", "properties": {"tax": {"$comment": "***taxSchema used here***"}}}}}}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "settings": {"type": "object", "properties": {"tax": {"$comment": "***taxSchema used here***"}}}, "payments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "recurrence": {"type": "object", "required": ["dtstart"], "properties": {"freq": {"type": "string"}, "dtstart": {"type": "string"}, "until": {"type": "string"}, "interval": {"type": "number"}, "count": {"type": "number"}, "bymonth": {"type": "array", "items": {"type": "number"}}, "byweekday": {"type": "array", "items": {"type": "string"}}, "byhour": {"type": "array", "items": {"type": "number"}}, "byminute": {"type": "array", "items": {"type": "number"}}, "bysecond": {"type": "array", "items": {"type": "number"}}, "bymonthday": {"type": "array", "items": {"type": "number"}}, "byyearday": {"type": "array", "items": {"type": "number"}}, "byweekno": {"type": "array", "items": {"type": "number"}}, "bysetpos": {"type": "array", "items": {"type": "number"}}, "wkst": {"type": "string"}, "tzid": {"type": "string"}}}, "paymentLink": {"type": "string"}, "status": {"type": "string", "enum": ["open", "paid", "closed"]}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "budgets": {"$id": "Budgets", "type": "object", "additionalProperties": false, "required": ["_id", "owner", "name", "moov_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "amount": {"type": "number"}, "approvers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "assigned_amount": {"type": "number"}, "assigned_recurs": {"type": "number"}, "expenses": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "careAccount": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "category": {"type": "string"}, "children": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "moov_id": {"type": "string"}, "connect_id": {"type": "string"}, "lastInc": {"type": "string"}, "lastSync": {"type": "object", "properties": {"adjusted": {"type": "boolean"}, "balance": {"type": "number"}, "date": {}, "err": {"type": "string"}, "amount": {"type": "number"}, "recurs": {"type": "number"}, "adjust_spent": {"type": "number"}, "adjust_spent_pending": {"type": "number"}, "adjust_amount": {"type": "number"}, "adjust_assigned_amount": {"type": "number"}, "adjust_recurs": {"type": "number"}, "adjust_assigned_recurs": {"type": "number"}, "freeze": {"type": "boolean"}, "excess": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "ramp_whitelist": {"type": "array", "items": {"type": "string"}}, "mcc_whitelist": {"type": "array", "items": {"type": "string"}}, "mcc_blacklist": {"type": "array", "items": {"type": "string"}}, "members": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "name": {"type": "string"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "parent": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "priorAuth": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}}}, "recurs": {"type": "number"}, "runSync": {"type": "string"}, "spent": {"type": "number"}, "spent_pending": {"type": "number"}, "spent_pending_sub": {"type": "number"}, "spent_sub": {"type": "number"}, "syncHistory": {"type": "array", "items": {"type": "object", "properties": {"adjusted": {"type": "boolean"}, "balance": {"type": "number"}, "date": {}, "err": {"type": "string"}, "amount": {"type": "number"}, "recurs": {"type": "number"}, "adjust_spent": {"type": "number"}, "adjust_spent_pending": {"type": "number"}, "adjust_amount": {"type": "number"}, "adjust_assigned_amount": {"type": "number"}, "adjust_recurs": {"type": "number"}, "adjust_assigned_recurs": {"type": "number"}, "freeze": {"type": "boolean"}, "excess": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "rampSpendProgram": {"type": "string"}, "priorMonths": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "recurs": {"type": "number"}, "parent": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "careAccount": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "spent_sub": {"type": "number"}, "spent_pending": {"type": "number"}, "spent_pending_sub": {"type": "number"}, "spent": {"type": "number"}}}}}}, "bundles": {"$id": "Bundles", "type": "object", "additionalProperties": false, "required": ["_id", "name", "provider"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "public": {"type": "boolean"}, "plans": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "name": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number"}, "locations": {"type": "array", "items": {"type": "string"}}, "prices": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "networks": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "writers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "calendars": {"$id": "Calendars", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "ownerDefault": {"type": "boolean"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ownerService": {"type": "string"}, "editors": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "archived": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "past": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "future": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "notify": {"type": "object", "properties": {"before": {"type": "number"}, "sms": {"type": "object", "properties": {"active": {"type": "boolean"}, "contactPath": {"type": "string"}, "contact": {"type": "string"}}}, "email": {"type": "object", "properties": {"active": {"type": "boolean"}, "contactPath": {"type": "string"}, "contact": {"type": "string"}}}, "internal": {"type": "object", "properties": {"active": {"type": "boolean"}, "contactPath": {"type": "string"}, "contact": {"type": "string"}}}}}, "schedule": {"type": "object", "properties": {"days": {"type": "object", "properties": {"0": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "1": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "2": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "3": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "4": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "5": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}, "6": {"type": "object", "properties": {"all": {"type": "boolean"}, "times": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "number"}, "end": {"type": "number"}}}}}}}}, "blackoutDates": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "string"}, "end": {"type": "string"}, "recurrence": {"type": "object", "required": ["dtstart"], "properties": {"freq": {"type": "string"}, "dtstart": {"type": "string"}, "until": {"type": "string"}, "interval": {"type": "number"}, "count": {"type": "number"}, "bymonth": {"type": "array", "items": {"type": "number"}}, "byweekday": {"type": "array", "items": {"type": "string"}}, "byhour": {"type": "array", "items": {"type": "number"}}, "byminute": {"type": "array", "items": {"type": "number"}}, "bysecond": {"type": "array", "items": {"type": "number"}}, "bymonthday": {"type": "array", "items": {"type": "number"}}, "byyearday": {"type": "array", "items": {"type": "number"}}, "byweekno": {"type": "array", "items": {"type": "number"}}, "bysetpos": {"type": "array", "items": {"type": "number"}}, "wkst": {"type": "string"}, "tzid": {"type": "string"}}}}}}}}, "tokens": {"type": "object", "properties": {"google": {"type": "string"}}}}}, "cams": {"$id": "<PERSON>s", "type": "object", "additionalProperties": false, "required": ["_id", "org", "person"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "comp": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "hireDate": {}, "hoursWorked": {"type": "number"}, "off": {"type": "boolean"}, "stage": {"type": "string"}, "active": {"type": "string"}, "group": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "terminated": {"type": "boolean"}, "terminatedAt": {}, "terminatedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "estHours": {"type": "number"}, "amount": {"type": "number"}, "terms": {"type": "string"}, "name": {"type": "string"}, "class": {"type": "string", "enum": ["ee", "ic"]}, "extras": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"due": {"type": "string"}, "awarded": {"type": "string"}, "off": {"type": "boolean"}, "banks": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat", "units"]}, "unit": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "amount": {"type": "number"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "terms": {"type": "string"}, "limit": {"type": "number"}}}}}}}, "caps": {"$id": "Caps", "type": "object", "additionalProperties": false, "required": ["_id", "subject", "did"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subjectService": {"type": "string"}, "did": {"type": "string"}, "caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"description": {"type": "string"}, "ucan": {"type": "string"}, "logins": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}, "care-accounts": {"$id": "CareAccounts", "type": "object", "additionalProperties": false, "required": ["_id", "owner"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "amount": {"type": "number"}, "approvers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "assigned_amount": {"type": "number"}, "assigned_recurs": {"type": "number"}, "budgets": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "connect_id": {"type": "string"}, "stripe_id": {"type": "string"}, "moov_id": {"type": "string"}, "wallet_id": {"type": "string"}, "last4": {"type": "string"}, "lastInc": {"type": "string"}, "lastSync": {"type": "object", "properties": {"adjusted": {"type": "boolean"}, "balance": {"type": "number"}, "date": {}, "err": {"type": "string"}, "amount": {"type": "number"}, "recurs": {"type": "number"}, "adjust_spent": {"type": "number"}, "adjust_spent_pending": {"type": "number"}, "adjust_amount": {"type": "number"}, "adjust_assigned_amount": {"type": "number"}, "adjust_recurs": {"type": "number"}, "adjust_assigned_recurs": {"type": "number"}, "freeze": {"type": "boolean"}, "excess": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "members": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "ramp_whitelist": {"type": "array", "items": {"type": "string"}}, "mcc_whitelist": {"type": "array", "items": {"type": "string"}}, "mcc_blacklist": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "recurs": {"type": "number"}, "runSync": {"type": "string"}, "status": {"type": "string"}, "statusNote": {"type": "string"}, "syncHistory": {"type": "array", "items": {"type": "object", "properties": {"adjusted": {"type": "boolean"}, "balance": {"type": "number"}, "date": {}, "err": {"type": "string"}, "amount": {"type": "number"}, "recurs": {"type": "number"}, "adjust_spent": {"type": "number"}, "adjust_spent_pending": {"type": "number"}, "adjust_amount": {"type": "number"}, "adjust_assigned_amount": {"type": "number"}, "adjust_recurs": {"type": "number"}, "adjust_assigned_recurs": {"type": "number"}, "freeze": {"type": "boolean"}, "excess": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "cares": {"$id": "Cares", "type": "object", "additionalProperties": false, "required": ["_id", "person", "plan", "patient"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "planPriority": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "patientPriority": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "providerPriority": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "initDate": {}, "name": {"type": "string"}, "targetDate": {}, "lastVisit": {}, "visits": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "providers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "parent": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "children": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "related": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "conditions": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "medical_name": {"type": "string"}, "code": {"type": "string"}, "standard": {"type": "string"}, "notes": {"type": "string"}, "loggedAt": {}, "loggedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "preventive": {"type": "boolean"}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "total": {"type": "number"}, "subtotal": {"type": "number"}, "pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "balance": {"type": "number"}, "balanceSyncedAt": {}}}, "cats": {"$id": "Cats", "type": "object", "additionalProperties": true, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "writers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "images": {"$comment": "***imageSchema used here***"}, "name": {"type": "string"}, "description": {"type": "string"}, "code_regex": {"type": "string"}, "conditions": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "procedures": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "meds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "challenges": {"$id": "Challenges", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "kind": {"enum": ["registration", "authentication"]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "challenge": {"type": "string", "description": "Opaque WebAuthn challenge (base64url or raw)"}, "connectionId": {"type": "string", "description": "Feathers socket connection id"}, "expiresAt": {"type": "number", "description": "epoch ms; short TTL (e.g., 2–5 minutes)"}}}, "change-logs": {"$id": "ChangeLogs", "type": "object", "additionalProperties": true, "required": ["_id", "recordId", "service"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "service": {"type": "string"}, "recordId": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "claim-payments": {"$id": "ClaimPayments", "type": "object", "additionalProperties": false, "required": ["_id", "amount", "claim", "org", "person", "plan", "patient", "provider"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "claim": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "visit": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "preventive": {"type": "boolean"}, "allowedMethods": {"type": "array", "items": {"type": "string", "enum": ["card", "ach", "ca"]}}, "method": {"type": "string", "enum": ["card", "ach", "ca", "ext"]}, "checkoutSession": {"type": "string"}, "customerId": {"type": "string"}, "payment_method": {"type": "string"}, "transactionId": {"type": "string"}, "providerCareAccount": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "careAccount": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "card": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "amount": {"type": "number"}, "ded": {"type": "number"}, "copay": {"type": "number"}, "coins": {"type": "number"}, "due": {"type": "string"}, "refunds": {"type": "array", "items": {"type": "object", "required": ["amount", "confirmation"], "properties": {"ded": {"type": "number"}, "coins": {"type": "number"}, "copay": {"type": "number"}, "amount": {"type": "number"}, "refundedAt": {}, "status": {"type": "string", "enum": ["pending", "complete", "cancelled"]}, "confirmation": {"type": "string"}}}}, "type": {"type": "string"}, "confirmation": {"type": "string"}, "confirmedAt": {}, "fullPayment": {"type": "boolean"}, "status": {"type": "string", "enum": ["request", "offer", "pending", "paid", "cancelled", "refunded", "returned"]}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "claim-reqs": {"$id": "ClaimReqs", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "care": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "visit": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "claim": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "practitioner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "providerOrg": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "status": {"type": "string", "enum": ["unopened", "pending", "approved", "rejected"]}, "removeRequest": {"type": "boolean"}, "claimData": {"type": "object", "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "visit": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "practitioner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "procedure": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "med": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "date": {}, "misc": {"type": "string"}, "enteredBy": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "auto": {"type": "boolean"}}}, "log": {"type": "object", "properties": {"code": {"type": "string"}, "standard": {"type": "string"}}}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}, "description": {"type": "string"}, "notes": {"type": "string"}, "preventive": {"type": "boolean"}, "adj": {"type": "object", "required": ["adjBy", "adjAt", "fp"], "properties": {"adjBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "adjAt": {}, "fp": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ded": {"type": "number"}, "coins": {"type": "number"}, "copay": {"type": "number"}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "waived_ded": {"type": "number"}, "waived_coins": {"type": "number"}, "waived_copay": {"type": "number"}, "preventive": {"type": "boolean"}, "amount": {"type": "number"}, "qty": {"type": "number"}, "total": {"type": "number"}, "notes": {"type": "string"}}}, "pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "amount": {"type": "number"}, "subtotal": {"type": "number"}, "total": {"type": "number"}, "qty": {"type": "number"}, "balanceSyncedAt": {}, "balance": {"type": "number"}, "adjHistory": {"type": "array", "items": {"type": "object", "required": ["adjBy", "adjAt", "fp"], "properties": {"adjBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "adjAt": {}, "fp": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ded": {"type": "number"}, "coins": {"type": "number"}, "copay": {"type": "number"}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "waived_ded": {"type": "number"}, "waived_coins": {"type": "number"}, "waived_copay": {"type": "number"}, "preventive": {"type": "boolean"}, "amount": {"type": "number"}, "qty": {"type": "number"}, "total": {"type": "number"}, "notes": {"type": "string"}}}}, "taxes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "fees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "payments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "reduced": {"type": "object", "properties": {"from": {"type": "number"}, "to": {"type": "number"}, "on": {"type": "string"}}}, "status": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "settings": {"type": "object", "properties": {"tax": {"$comment": "***taxSchema used here***"}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "env": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "host": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ref": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "changeLog": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "editMap": {"type": "object"}, "deleted": {"type": "boolean"}, "session_fp": {"type": "string"}, "deletedAt": {}, "updatedAt": {}, "createdAt": {}, "createdBy": {"$comment": "***updatesSchema used here***"}, "updatedBy": {"$comment": "***updatesSchema used here***"}, "updatedByHistory": {"$comment": "***updatesSchema used here***"}}}}}, "claims": {"$id": "<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": false, "required": ["_id", "visit", "plan", "patient", "person", "provider", "date"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "visit": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "practitioner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "procedure": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "med": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "date": {}, "misc": {"type": "string"}, "enteredBy": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "auto": {"type": "boolean"}}}, "log": {"type": "object", "properties": {"code": {"type": "string"}, "standard": {"type": "string"}}}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}, "description": {"type": "string"}, "notes": {"type": "string"}, "preventive": {"type": "boolean"}, "adj": {"type": "object", "required": ["adjBy", "adjAt", "fp"], "properties": {"adjBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "adjAt": {}, "fp": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ded": {"type": "number"}, "coins": {"type": "number"}, "copay": {"type": "number"}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "waived_ded": {"type": "number"}, "waived_coins": {"type": "number"}, "waived_copay": {"type": "number"}, "preventive": {"type": "boolean"}, "amount": {"type": "number"}, "qty": {"type": "number"}, "total": {"type": "number"}, "notes": {"type": "string"}}}, "pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "amount": {"type": "number"}, "subtotal": {"type": "number"}, "total": {"type": "number"}, "qty": {"type": "number"}, "balanceSyncedAt": {}, "balance": {"type": "number"}, "adjHistory": {"type": "array", "items": {"type": "object", "required": ["adjBy", "adjAt", "fp"], "properties": {"adjBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "adjAt": {}, "fp": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ded": {"type": "number"}, "coins": {"type": "number"}, "copay": {"type": "number"}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "waived_ded": {"type": "number"}, "waived_coins": {"type": "number"}, "waived_copay": {"type": "number"}, "preventive": {"type": "boolean"}, "amount": {"type": "number"}, "qty": {"type": "number"}, "total": {"type": "number"}, "notes": {"type": "string"}}}}, "taxes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "fees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}}}}}, "payments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "reduced": {"type": "object", "properties": {"from": {"type": "number"}, "to": {"type": "number"}, "on": {"type": "string"}}}, "status": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "settings": {"type": "object", "properties": {"tax": {"$comment": "***taxSchema used here***"}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "cobras": {"$id": "Cobras", "type": "object", "additionalProperties": false, "required": ["_id", "enrollment", "participant", "household", "deadline"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "participant": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "household": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "spec": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "event_type": {"type": "string", "enum": ["job", "death", "divorce", "medicare", "dependent"]}, "deadline": {"type": "string"}, "end_date": {"type": "string"}, "optOut": {"type": "boolean"}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"recurs": {"type": "number"}, "participants": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}, "comps": {"$id": "Comps", "type": "object", "additionalProperties": false, "required": ["_id", "amount", "interval", "org", "name", "key"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "key": {"type": "string"}, "video": {"$comment": "***imageSchema used here***"}, "references": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "ad": {"type": "string"}, "geo": {"$comment": "***geoJsonSchema used here***"}, "stages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"label": {"type": "string"}, "color": {"type": "string"}}}}}, "contact": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}}, "access": {"type": "string", "enum": ["public", "private"]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "estHours": {"type": "number"}, "amount": {"type": "number"}, "terms": {"type": "string"}, "name": {"type": "string"}, "class": {"type": "string", "enum": ["ee", "ic"]}, "extras": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"due": {"type": "string"}, "awarded": {"type": "string"}, "off": {"type": "boolean"}, "banks": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat", "units"]}, "unit": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "amount": {"type": "number"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "terms": {"type": "string"}, "limit": {"type": "number"}}}}}}}, "conditions": {"$id": "Conditions", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "standard": {"type": "string"}, "code": {"type": "string"}, "link": {"type": "string"}, "chapter": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "procedures": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "procedureComments": {"type": "string"}}}, "contracts": {"$id": "Contracts", "type": "object", "additionalProperties": false, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "public": {"type": "boolean"}, "template": {"type": "boolean"}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subjectService": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ownerService": {"type": "string"}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "tags": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["open", "sent", "rejected", "executed"]}, "rejectedBy": {"$comment": "***updatesSchema used here***"}, "pTags": {"type": "array", "items": {"type": "string"}}, "parties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"relationship": {"type": "string"}, "by": {"type": "string"}, "byTitle": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "idService": {"type": "string"}, "aka": {"type": "string"}, "tag": {"type": "string"}, "legalName": {"type": "string"}, "address": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "ack": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}}}}}, "meta": {"type": "object", "additionalProperties": true, "properties": {"pay": {"type": "object", "properties": {"fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}, "feeDescription": {"type": "string"}, "hostSplitType": {"type": "string", "enum": ["percent", "flat", "pepm", "pmpm"]}, "hostSplitAmount": {"type": "number"}, "refSplit": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"percent": {"type": "number"}, "ref": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}}}, "coverages": {"$id": "Coverages", "type": "object", "additionalProperties": false, "required": ["_id", "name", "covered", "type"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "vectorIds": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"uploadIds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}}}}}, "carrierName": {"type": "string", "$comment": "name of the insurance company"}, "webpage": {"type": "string", "$comment": "link to details webpage if available"}, "name": {"type": "string", "$comment": "name of the coverage"}, "openNetwork": {"type": "boolean"}, "plan_type": {"type": "string", "$comment": "network type such as HMO, PPO, EPO, POS"}, "type": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"], "$comment": "major medical, health share, direct care, excepted benefit"}, "description": {"type": "string", "$comment": "brief coverage description"}, "hsaQualified": {"type": "boolean", "$comment": "high deductible health plan - eligible for HSA contributions"}, "productDetailRef": {"type": "string", "$comment": "For health shares - this is the health share `_id:product_id`"}, "fortyPremium": {"type": "number"}, "maxAge": {"type": "number"}, "preventive": {"type": "boolean"}, "coinsurance": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}, "coins": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "deductible": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "deductibles": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "cap": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "caps": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "copays": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "premium": {"type": "object", "$comment": "Choose one of the premium structures flatPremium, rateByAge, fixedRates. The remaining properties are utilities for adjusting rates based on age, household count, etc.", "properties": {"flatPremium": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}, "$comment": "if the premium is a flat number per person, no age banding, no multi-person discount"}, "rateByAge": {"type": "object", "$comment": "flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "fixedRates": {"type": "object", "$comment": "this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "rateType": {"type": "string", "enum": ["flatPremium", "rateByAge", "fixedRates"], "$comment": "which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees"}, "multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc."}, "weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "A complex option for weighting rates by age such that multiDiscount doesn't apply equally to all ages"}, "baseDefault": {"type": "boolean", "$comment": "To be used with flatPremium - if true, this is the default rate if no rates are found"}, "breakpointAges": {"type": "array", "items": {"type": "number"}, "$comment": "This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates"}, "rateBreak": {"type": "string", "enum": ["graduated", "breakpoint"], "$comment": "Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property"}, "smokerFactor": {"type": "number", "$comment": "increase percentage for smoking status ie: 1.5 would be 50% more for smokers"}}}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "rates": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "special rate areas - to be added by a user later"}, "deductibleType": {"type": "string", "enum": ["annual", "event"], "$comment": "deprecated - now see deductible.type"}, "moop": {"type": "object", "$comment": "medical or combined moop amount for individuals and families respectively", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "moops": {"$comment": "any key with dedSchema as the details of the moop", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "monthsSinceSmoked": {"type": "number", "$comment": "at how many months someone is considered non-tobacco user"}, "catsBlacklist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "memo": {"type": "string"}}}}}, "covered": {"type": "string", "enum": ["individual", "group"]}, "carrierLogo": {"$comment": "***imageSchema used here***"}, "documents": {"$comment": "***imageSchema used here***"}, "postTax": {"type": "boolean"}, "video": {"$comment": "***imageSchema used here***"}, "geo": {"$comment": "***geoJsonSchema used here***"}, "issuer": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "lastSync": {"type": "string"}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "template": {"type": "boolean"}, "fromTemplate": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "public": {"type": "boolean"}, "sim": {"type": "boolean"}, "group_sim_only": {"type": "boolean", "$comment": "For products only available to groups - even if its an individual contract. Won't show in individual shop experience not tied to a group"}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "listBillDiscount": {"type": "number"}, "av": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"value": {"type": "number"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "at": {}}}}}, "dpc": {"type": "boolean"}, "ichra": {"type": "boolean"}, "shop": {"type": "boolean"}, "adj": {"type": "object", "properties": {"autoMax": {"type": "number"}, "authProviders": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "networks": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"coins_discount": {"type": "number"}, "coins_discount_type": {"type": "string", "enum": ["percent", "flat"]}, "ded_discount": {"type": "number"}, "ded_discount_type": {"type": "string", "enum": ["percent", "flat"]}}}}}, "disability": {"type": "object", "properties": {"coverOverRequiredAge": {"type": "boolean"}, "incomeLimit": {"type": "number"}}}}}, "cross-sections": {"$id": "CrossSections", "type": "object", "additionalProperties": false, "required": ["_id", "hackId", "subject", "sections"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "hackId": {"type": "string"}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "sections": {"type": "object", "patternProperties": {"^.*$": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "doc-requests": {"$id": "DocRequests", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "types": {"type": "array", "items": {"type": "string"}}, "orgName": {"type": "string"}, "orgAvatar": {"type": "string"}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fingerprint": {"type": "string"}, "refName": {"type": "string"}, "doc": {"type": "string"}, "eeCount": {"type": "number"}, "fteCount": {"type": "number"}, "email": {"type": "string"}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "name": {"type": "string"}, "optIn": {"type": "boolean"}, "status": {"type": "string", "enum": ["complete", "partial"]}, "states": {"type": "array", "items": {"type": "string"}}, "docType": {"type": "string", "enum": ["smb"]}, "employees": {"type": "array", "items": {"type": "object", "properties": {"w9": {"type": "boolean"}, "role": {"type": "string"}, "errorAdding": {"type": "boolean"}, "addError": {"type": "string"}, "camsError": {"type": "boolean"}, "uid": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "lastName": {"type": "string"}, "firstName": {"type": "string"}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "gender": {"type": "string", "enum": ["male", "female"]}, "wage": {"type": "number"}, "income": {"type": "number"}, "hh_income": {"type": "number"}, "hourly": {"type": "boolean"}, "hours": {"type": "number"}, "married": {"type": "string"}, "deps": {"type": "number"}, "zip": {"type": "string"}, "state": {"type": "string"}, "smoker": {"type": "boolean"}, "spouseAge": {"type": "string"}, "spouseDob": {"type": "string"}, "age": {"type": "number"}, "dob": {"type": "string"}, "updatedAt": {}, "ptc": {"type": "number"}, "ptcUpdatedAt": {}, "countyfips": {"type": "string"}, "lastFacts": {"type": "object", "properties": {"gender": {"type": "string", "enum": ["male", "female"]}, "wage": {"type": "number"}, "income": {"type": "number"}, "hh_income": {"type": "number"}, "hourly": {"type": "boolean"}, "hours": {"type": "number"}, "married": {"type": "string"}, "deps": {"type": "number"}, "zip": {"type": "string"}, "state": {"type": "string"}, "smoker": {"type": "boolean"}, "spouseAge": {"type": "string"}, "spouseDob": {"type": "string"}, "age": {"type": "number"}, "dob": {"type": "string"}}}}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "doc-templates": {"$id": "DocTemplates", "type": "object", "additionalProperties": false, "required": ["_id", "name", "sections"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "description": {"type": "string"}, "class": {"type": "string", "enum": ["core", "125", "105", "misc", "spd"]}, "subClass": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}, "smb": {"type": "boolean"}}}, "drops": {"$id": "Drops", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "tags": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string", "enum": ["question", "answer"]}, "archives": {"type": "object", "properties": {"title": {"type": "array", "items": {"type": "string"}}, "body": {"type": "array", "items": {"type": "string"}}}}, "topAnswer": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "topScore": {"type": "number"}, "title": {"type": "string"}, "body": {"type": "string"}, "class": {"type": "string"}, "anonymous": {"type": "boolean"}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "voteCount": {"type": "number"}, "upVotes": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "downVotes": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "enrollments": {"$id": "Enrollments", "type": "object", "additionalProperties": false, "required": ["_id", "org", "group", "person", "plan", "version", "close", "idempotency_key"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "group": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "idempotency_key": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string"}, "planYear": {"type": "string"}, "address": {"$comment": "***addressSchema used here***"}, "county": {"type": "object", "properties": {"fips": {"type": "string"}, "name": {"type": "string"}, "stateCode": {"type": "string"}}}, "spec": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "string", "enum": ["not_started", "open", "review", "complete", "closed"]}, "statusNote": {"type": "string"}, "optOut": {}, "open": {"type": "string"}, "close": {}, "terminated": {"type": "boolean"}, "terminatedAt": {}, "terminatedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrolledAt": {}, "ichra": {"type": "boolean"}, "shop": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "type": {"type": "string", "enum": ["single", "family"]}, "householdIncome": {"type": "number"}, "enrolled": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"age": {"type": "number"}, "dob": {"type": "string"}, "ssn": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "gender": {"type": "string"}, "relation": {"type": "string", "enum": ["self", "spouse", "child", "father", "mother", "grandfather", "grandmother", "grandson", "granddaughter", "son_in_law", "daughter_in_law", "uncle", "aunt", "nephew", "niece", "cousin", "guardian", "stepparent", "stepson", "stepdaughter", "adopted_child", "foster_child", "sister", "brother", "brother_in_law", "sister_in_law", "mother_in_law", "father_in_law", "ward", "sponsored_dependent", "dependent_minor_dependent", "ex_spouse", "court_appointed_guardian", "collateral_dependent", "life_partner", "annultant", "trustee", "other_relationship", "other_relative"]}, "zip": {"type": "string"}, "point": {"type": "array", "items": {"type": "number"}}, "monthsSinceSmoked": {"type": "number"}, "dependent": {"type": "boolean"}, "disabled": {"type": "boolean"}, "annualIncome": {"type": "number"}, "incarcerated": {"type": "boolean"}}}}}, "cafe": {"type": "object", "properties": {"hsa": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}, "fsa": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}, "dcp": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}, "pop": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}, "def": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}, "cash": {"type": "object", "properties": {"optOut": {}, "amount": {"type": "number"}, "updateHistory": {"type": "array", "items": {"type": "object", "properties": {"amount": {"type": "number"}, "optOut": {}, "date": {}}}}, "acknowledgements": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "priority": {"type": "number"}, "key": {"type": "string"}, "date": {}, "ip": {"type": "string"}, "user_agent": {"type": "string"}}}}}}}}, "lastClaimCoverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "claimPayments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "patientClaims": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}}}}}}}, "coverageClaims": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}}}}}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"ichra": {"type": "boolean"}, "shop": {"type": "boolean"}, "recurs": {"type": "number"}, "card": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "confirmedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "confirmedAt": {}, "confirmData": {"type": "object", "properties": {"aptc": {"type": "number"}, "policy_id": {"type": "string"}, "premium": {"type": "number"}, "income": {"type": "number"}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "participants": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "premium": {"type": "number"}, "participants_last": {"type": "number"}, "coverageType": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"]}, "postTax": {"type": "boolean"}, "policy": {"type": "string"}, "fullPolicy": {}, "ptc": {"type": "number"}, "individual_coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fullCoverage": {}, "optOut": {}, "optOutDisclosure": {"type": "string"}, "providers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "status": {"type": "number", "enum": [1, 2, 3, 4]}, "type": {"type": "string", "enum": ["individual", "family"]}, "slcsp": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "premium": {"type": "number"}}}, "aptc": {"type": "object", "properties": {"attest": {"type": "string"}, "income": {"type": "number"}, "aptc": {"type": "number"}, "hardship_exemption": {"type": "boolean"}, "in_coverage_gap": {"type": "boolean"}, "is_medicaid_chip": {"type": "boolean"}}}, "annualSpend": {"type": "number"}, "rxSpend": {"type": "number"}, "issuerBlacklist": {"type": "array", "items": {"type": "string"}}, "typeBlacklist": {"type": "array", "items": {"type": "string"}}, "conditions": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "procedures": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "meds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}, "contributions": {"type": "object", "properties": {"lastAutoSet": {}, "lastManualSet": {}, "employer": {"type": "object", "properties": {"cafe": {"type": "number"}, "coverages": {"type": "number"}}}, "employee": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "needed": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "byPlan": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "byCoverage": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"employer": {"type": "number"}, "employee": {"type": "number"}}}}}}}}}, "errs": {"$id": "Errs", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "path": {"type": "string"}, "method": {"type": "string"}, "type": {"type": "string"}, "id": {"type": "string"}, "error": {}, "params": {}, "data": {}, "result": {}}}, "expenses": {"$id": "Expenses", "type": "object", "additionalProperties": false, "required": ["_id", "name", "owner", "budget", "limit_owner"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "amount": {"type": "number", "$comment": "spend limit"}, "max_transaction": {"type": "number", "$comment": "max transaction limit"}, "recurs": {"type": "number", "$comment": "monthly limit"}, "recur_start": {"type": "string"}, "next_reset": {"type": "string"}, "lock_date": {"type": "string"}, "spent": {"type": "number", "$comment": "amount spent"}, "spent_pending": {"type": "number", "$comment": "amount pending - moves to zero when spent confirmed"}, "singleUse": {"type": "boolean"}, "singleVendor": {"type": "boolean"}, "perTransactionLimit": {"type": "number"}, "approvers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "limit_owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "closedLimitIds": {"type": "array", "items": {"type": "string"}}, "closedLimits": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "closedAt": {}, "memo": {"type": "string"}}}}, "last4": {"type": "string"}, "lastSync": {}, "ramp_limit": {"type": "string"}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "ramp_whitelist": {"type": "array", "items": {"type": "number"}}, "ramp_blacklist": {"type": "array", "items": {"type": "number"}}, "vendor_whitelist": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "orgs that a limit is limited to - must have a ramp_vendor_id"}, "vendor_blacklist": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "orgs that a limit is restricted for - must have a ramp_vendor_id"}, "mcc_whitelist": {"type": "array", "items": {"type": "string"}}, "mcc_blacklist": {"type": "array", "items": {"type": "string"}}, "members": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "name": {"type": "string"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "preAuth": {"type": "number"}, "status": {"type": "string", "enum": ["active", "inactive", "canceled", "pending-verification"]}, "users": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "fb-res": {"$id": "FbRes", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "form": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "formData": {}, "lastField": {"type": "string"}}}, "fbs": {"$id": "Fbs", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "active": {"type": "boolean"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "live": {"type": "boolean"}, "parent": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "children": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "name": {"type": "string"}, "primaryColor": {"type": "string"}, "secondaryColor": {"type": "string"}, "description": {"type": "string"}, "avatar": {"$comment": "***imageSchema used here***"}, "welcomeTitle": {"type": "string"}, "welcomeMessage": {"type": "string"}, "welcomeImage": {"$comment": "***imageSchema used here***"}, "welcomeVideos": {"$comment": "***imageSchema used here***"}, "welcomeFiles": {"$comment": "***imageSchema used here***"}, "finishTitle": {"type": "string"}, "finishMessage": {"type": "string"}, "finishImage": {"$comment": "***imageSchema used here***"}, "finishVideos": {"$comment": "***imageSchema used here***"}, "finishFiles": {"$comment": "***imageSchema used here***"}, "dark": {"type": "boolean"}, "class": {"type": "string"}, "products": {"type": "object", "properties": {"ids": {"type": "array"}}}, "style": {"type": "object", "properties": {"background": {"type": "string"}, "color": {"type": "string"}, "padding": {"type": "string"}}}, "fields": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "color": {"type": "string"}, "icon": {"type": "string"}, "label": {"type": "string"}, "value": {}, "next": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "if": {"type": "object", "properties": {"check": {"type": "string"}, "arg": {}, "error": {"type": "string"}, "rule": {"type": "string"}}}}}}, "validators": {"type": "object", "properties": {"check": {"type": "string"}, "arg": {}, "error": {"type": "string"}, "rule": {"type": "string"}}}, "slots": {"type": "array"}, "rules": {"type": "array"}, "errs": {"type": "array"}, "fieldType": {"type": "string"}, "path": {"type": "string"}, "attrs": {}, "div-attrs": {}}}}, "responses": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "canEdit": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "fingerprints": {"$id": "Fingerprints", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "turnstile": {"type": "object", "additionalProperties": true, "properties": {"success": {"type": "boolean"}, "challenge_ts": {"type": "string"}, "hostname": {"type": "string"}, "error-codes": {"type": "array"}, "action": {"type": "string"}, "cdata": {"type": "string"}, "metadata": {"type": "object"}}}, "visitorId": {"type": "string"}, "visits": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"date": {}, "domain": {"type": "string"}, "ipInfo": {"type": "object", "additionalProperties": true, "properties": {"ip": {"type": "string"}, "range": {"type": "array", "items": {"type": "number"}}, "country": {"type": "string"}, "region": {"type": "string"}, "eu": {"type": "string"}, "timezone": {"type": "string"}, "city": {"type": "string"}, "lngLat": {"type": "array", "items": {"type": "number"}}, "ll": {"type": "array", "items": {"type": "number"}}, "metro": {"type": "number"}, "area": {"type": "number"}}}, "incognito": {"type": "boolean"}}}}, "name": {"type": "string"}, "manufacturer": {"type": "string"}, "product": {"type": "string"}, "osName": {"type": "string"}, "screenWidth": {}, "touch": {"type": "boolean"}, "incognito": {"type": "boolean"}, "type": {"type": "string"}, "ua": {"type": "string"}, "ipInfo": {"type": "object", "additionalProperties": true, "properties": {"ip": {"type": "string"}, "range": {"type": "array", "items": {"type": "number"}}, "country": {"type": "string"}, "region": {"type": "string"}, "eu": {"type": "string"}, "timezone": {"type": "string"}, "city": {"type": "string"}, "lngLat": {"type": "array", "items": {"type": "number"}}, "ll": {"type": "array", "items": {"type": "number"}}, "metro": {"type": "number"}, "area": {"type": "number"}}}}}, "flow-charts": {"$id": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "nodes": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"parent": {"type": "string"}, "label": {"type": "string"}, "children": {"type": "object"}, "q": {"type": "string"}, "text": {"type": "string"}}}}}}, "funds": {"$id": "Funds", "type": "object", "additionalProperties": false, "required": ["_id", "name", "state"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "state": {"type": "string"}, "board": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "nominees": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "number", "enum": [0, 1, 2, 3, 4, 5]}, "statusUpdates": {"type": "array", "items": {"type": "object", "properties": {"from": {"type": "number"}, "to": {"type": "number"}, "at": {}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}}}}, "funds-requests": {"$id": "FundsRequests", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "gps": {"$id": "Gps", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "runRequest": {}, "companyName": {"type": "string"}, "companyAvatar": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "eeCount": {"type": "number"}, "planName": {"type": "string"}, "employerContribution": {"type": "object", "properties": {"amount": {"type": "number"}, "family": {"type": "number"}, "match": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat"]}, "percentType": {"type": "string", "enum": ["cost", "income"]}, "postTax": {"type": "number"}, "includeExtras": {"type": "boolean"}}}, "ale": {"type": "boolean"}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "editors": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "vectorId": {"type": "string"}, "lastSim": {}, "groupCompare": {"type": "boolean"}, "simProgress": {"type": "number"}, "simStats": {"type": "object", "$comment": "Split the stats into enrolled employees (no prefix) and not enrolled \"inactive\"", "properties": {"count": {"type": "number", "$comment": "Total number of auto-selected plans"}, "spendPtc": {"type": "number", "$comment": "premium + oop for auto-selected plan factoring in PTC"}, "premiumPtc": {"type": "number", "$comment": "premium for auto-selected plan factoring in PTC"}, "spend": {"type": "number", "$comment": "premium + oop for auto-selected plan"}, "premium": {"type": "number", "$comment": "premium for auto-selected plan"}, "tax_savings": {"type": "number", "$comment": "Total tax savings for auto-selected plan if ICHRA"}, "tax_rate": {"type": "number", "$comment": "Average tax rate for auto-selected plan employees - useful for group calculations"}, "ptc": {"type": "number", "$comment": "Total PTC for auto-selected plan"}, "ptc_likely": {"type": "number", "$comment": "Likely actual PTC based on whether the best option was PTC eligible"}, "altSpend": {"type": "number", "$comment": "Best non insurance option"}, "altPremium": {"type": "number"}, "selected_spend": {"type": "number"}, "selected_spend_delta": {"type": "number"}, "selected_spendPtc_delta": {"type": "number"}, "selected_premium": {"type": "number"}, "selected_premium_delta": {"type": "number", "$comment": "The difference in premium between the auto-selected plan and the employee selected plan"}, "selected_premiumPtc_delta": {"type": "number", "$comment": "The difference in premium between the auto-selected plan and the employee selected plan"}, "selected_ptc": {"type": "number", "$comment": "PTC applying to selected plans"}, "selected_ptc_delta": {"type": "number", "$comment": "The difference in PTC between the auto-selected plan and the employee selected plan"}, "selected_count": {"type": "number"}, "selected_tax_savings": {"type": "number"}, "selected_tax_savings_delta": {"type": "number"}, "selected_tax_rate": {"type": "number"}, "inactive_count": {"type": "number", "$comment": "Total number of auto-selected plans"}, "inactive_spendPtc": {"type": "number", "$comment": "premium + oop for auto-selected plan factoring in PTC"}, "inactive_premiumPtc": {"type": "number", "$comment": "premium for auto-selected plan factoring in PTC"}, "inactive_spend": {"type": "number", "$comment": "premium + oop for auto-selected plan"}, "inactive_premium": {"type": "number", "$comment": "premium for auto-selected plan"}, "inactive_tax_savings": {"type": "number", "$comment": "Total tax savings for auto-selected plan if ICHRA"}, "inactive_tax_rate": {"type": "number", "$comment": "Average tax rate for auto-selected plan employees - useful for group calculations"}, "inactive_ptc": {"type": "number", "$comment": "Total PTC for auto-selected plan"}, "inactive_ptc_likely": {"type": "number", "$comment": "Likely actual PTC based on whether the best option was PTC eligible"}, "inactive_altSpend": {"type": "number", "$comment": "Best non insurance option"}, "inactive_altPremium": {"type": "number"}, "inactive_selected_spend": {"type": "number"}, "inactive_selected_spend_delta": {"type": "number"}, "inactive_selected_spendPtc_delta": {"type": "number"}, "inactive_selected_premium": {"type": "number"}, "inactive_selected_premium_delta": {"type": "number", "$comment": "The difference in premium between the auto-selected plan and the employee selected plan"}, "inactive_selected_premiumPtc_delta": {"type": "number", "$comment": "The difference in premium between the auto-selected plan and the employee selected plan"}, "inactive_selected_ptc": {"type": "number", "$comment": "PTC applying to selected plans"}, "inactive_selected_ptc_delta": {"type": "number", "$comment": "The difference in PTC between the auto-selected plan and the employee selected plan"}, "inactive_selected_count": {"type": "number"}, "inactive_selected_tax_savings": {"type": "number"}, "inactive_selected_tax_savings_delta": {"type": "number"}, "inactive_selected_tax_rate": {"type": "number"}}}, "currentStats": {"type": "object", "properties": {"spend": {"type": "number"}, "spendCount": {"type": "number"}, "spendPremium": {"type": "number"}, "count": {"type": "number"}, "premium": {"type": "number"}, "premiumByKey": {"type": "object", "properties": {"single": {"type": "number"}, "plus_spouse": {"type": "number"}, "plus_child": {"type": "number"}, "plus_child__2": {"type": "number"}, "plus_child__3": {"type": "number"}, "family": {"type": "number"}}}, "countByKey": {"type": "object", "properties": {"single": {"type": "number"}, "plus_spouse": {"type": "number"}, "plus_child": {"type": "number"}, "plus_child__2": {"type": "number"}, "plus_child__3": {"type": "number"}, "family": {"type": "number"}}}}}, "employees": {"type": "array", "items": {"type": "object", "properties": {"shop": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"type": "string"}, "sim": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "simError": {"type": "string"}, "bestPlan": {"type": "string"}, "bestPlanPtc": {"type": "string"}, "bestAlt": {"type": "string"}, "w9": {"type": "boolean"}, "role": {"type": "string"}, "errorAdding": {"type": "boolean"}, "addError": {"type": "string"}, "camsError": {"type": "boolean"}, "uid": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "lastName": {"type": "string"}, "firstName": {"type": "string"}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "gender": {"type": "string", "enum": ["male", "female"]}, "wage": {"type": "number"}, "income": {"type": "number"}, "hh_income": {"type": "number"}, "hourly": {"type": "boolean"}, "hours": {"type": "number"}, "married": {"type": "string"}, "deps": {"type": "number"}, "zip": {"type": "string"}, "state": {"type": "string"}, "smoker": {"type": "boolean"}, "spouseAge": {"type": "string"}, "spouseDob": {"type": "string"}, "age": {"type": "number"}, "dob": {"type": "string"}, "updatedAt": {}, "ptc": {"type": "number"}, "ptcUpdatedAt": {}, "countyfips": {"type": "string"}, "lastFacts": {"type": "object", "properties": {"gender": {"type": "string", "enum": ["male", "female"]}, "wage": {"type": "number"}, "income": {"type": "number"}, "hh_income": {"type": "number"}, "hourly": {"type": "boolean"}, "hours": {"type": "number"}, "married": {"type": "string"}, "deps": {"type": "number"}, "zip": {"type": "string"}, "state": {"type": "string"}, "smoker": {"type": "boolean"}, "spouseAge": {"type": "string"}, "spouseDob": {"type": "string"}, "age": {"type": "number"}, "dob": {"type": "string"}}}}}}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "compare_id": {"type": "string"}, "similar": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}}, "mostSimilar": {"type": "string"}, "carrierName": {"type": "string", "$comment": "name of the insurance company"}, "webpage": {"type": "string", "$comment": "link to details webpage if available"}, "name": {"type": "string", "$comment": "name of the coverage"}, "openNetwork": {"type": "boolean"}, "plan_type": {"type": "string", "$comment": "network type such as HMO, PPO, EPO, POS"}, "type": {"type": "string", "enum": ["mm", "mec", "hs", "dc", "eb", "hra"], "$comment": "major medical, health share, direct care, excepted benefit"}, "description": {"type": "string", "$comment": "brief coverage description"}, "hsaQualified": {"type": "boolean", "$comment": "high deductible health plan - eligible for HSA contributions"}, "productDetailRef": {"type": "string", "$comment": "For health shares - this is the health share `_id:product_id`"}, "fortyPremium": {"type": "number"}, "maxAge": {"type": "number"}, "preventive": {"type": "boolean"}, "coinsurance": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}, "coins": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "deductible": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "deductibles": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "cap": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "caps": {"$comment": "any key with dedSchema as the details of the deductible", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "copays": {"$comment": "any key with coinsSchema as the details of the coinsurance", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "coinsurance name (ie: emergency room)"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "amount": {"type": "number"}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}}}}}, "premium": {"type": "object", "$comment": "Choose one of the premium structures flatPremium, rateByAge, fixedRates. The remaining properties are utilities for adjusting rates based on age, household count, etc.", "properties": {"flatPremium": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}, "$comment": "if the premium is a flat number per person, no age banding, no multi-person discount"}, "rateByAge": {"type": "object", "$comment": "flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "fixedRates": {"type": "object", "$comment": "this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "rateType": {"type": "string", "enum": ["flatPremium", "rateByAge", "fixedRates"], "$comment": "which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees"}, "multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc."}, "weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "A complex option for weighting rates by age such that multiDiscount doesn't apply equally to all ages"}, "baseDefault": {"type": "boolean", "$comment": "To be used with flatPremium - if true, this is the default rate if no rates are found"}, "breakpointAges": {"type": "array", "items": {"type": "number"}, "$comment": "This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates"}, "rateBreak": {"type": "string", "enum": ["graduated", "breakpoint"], "$comment": "Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property"}, "smokerFactor": {"type": "number", "$comment": "increase percentage for smoking status ie: 1.5 would be 50% more for smokers"}}}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "rates": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "special rate areas - to be added by a user later"}, "deductibleType": {"type": "string", "enum": ["annual", "event"], "$comment": "deprecated - now see deductible.type"}, "moop": {"type": "object", "$comment": "medical or combined moop amount for individuals and families respectively", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}, "moops": {"$comment": "any key with dedSchema as the details of the moop", "type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string", "$comment": "deductible name (ie: family major medical)"}, "waivable": {"type": "boolean", "$comment": "waive-able for a preferred network/behavior"}, "detail": {"type": "string", "$comment": "descriptive detail"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "$comment": "to be added later by a user"}, "single": {"type": "number", "$comment": "single amount"}, "family": {"type": "number", "$comment": "family amount - do not add 0, do not include if not applicable"}, "type": {"type": "string", "enum": ["event", "annual"], "$comment": "whether this is per event or per year"}}}}}, "monthsSinceSmoked": {"type": "number", "$comment": "at how many months someone is considered non-tobacco user"}, "catsBlacklist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "memo": {"type": "string"}}}}}, "covered": {"type": "string", "enum": ["individual", "group"]}, "knownKeys": {"type": "array", "items": {"type": "string"}}, "files": {"$comment": "***imageSchema used here***"}, "fromFile": {"type": "boolean"}}}}}, "employerContributionReports": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "gps": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "updatedAt": {}, "data": {"type": "object", "properties": {"amount": {"type": "number"}, "family": {"type": "number"}, "match": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat"]}, "percentType": {"type": "string", "enum": ["cost", "income"]}, "postTax": {"type": "number"}, "includeExtras": {"type": "boolean"}}}}}}}}}, "groups": {"$id": "Groups", "type": "object", "additionalProperties": false, "required": ["_id", "org", "name", "key"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "key": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "applyUcan": {"type": "string"}, "healthPlans": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "planClass": {"type": "boolean"}, "memberCount": {"type": "number"}, "memberCountAt": {}}}, "grp-mbrs": {"$id": "GrpMbrs", "type": "object", "additionalProperties": false, "required": ["_id", "group", "person", "org", "mbrId"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "group": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "mbrId": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}}, "health-shares": {"$id": "HealthShares", "type": "object", "additionalProperties": false, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "description": {"type": "string"}, "aka": {"type": "array", "items": {"type": "string"}}, "logo": {"$comment": "***imageSchema used here***"}, "cc_video": {"$comment": "***imageSchema used here***"}, "video": {"$comment": "***imageSchema used here***"}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "products": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "guidelines": {"$comment": "***imageSchema used here***"}, "vectorStore": {"type": "object", "properties": {"id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}, "resetId": {"type": "string"}}}}}}}, "financials": {"type": "object", "patternProperties": {"^d{4}$": {"$comment": "Key is the year", "type": "object", "properties": {"total_revenue": {"type": "number"}, "sharing_expense": {"type": "number"}, "admin_expense": {"type": "number"}, "net_assets_start": {"type": "number"}, "net_assets": {"type": "number"}, "cash_on_hand": {"type": "number"}, "highest_paid_executive": {"type": "number"}}}}}}}, "hosts": {"$id": "Hosts", "type": "object", "additionalProperties": false, "required": ["_id", "org", "dba"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "appDefault": {"type": "boolean"}, "dba": {"type": "string"}, "description": {"type": "string"}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "subdomain": {"type": "string"}, "allVideos": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "emails": {"type": "array", "items": {"type": "string"}}, "locations": {"$comment": "***addressSchema used here***"}, "refs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "teams": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "npn": {"type": "string"}, "shopStatuses": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"label": {"type": "string"}, "color": {"type": "string"}}}}}, "publicSupport": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plans": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"team": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "payContract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "videos": {"type": "object", "properties": {"intro": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "states": {"type": "object", "patternProperties": {"[A-Z]{2}": {"type": "object", "properties": {"state": {"type": "string"}, "counties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"all": {"type": "boolean"}, "cities": {"type": "array", "items": {"type": "string"}}}}}}, "all": {"type": "boolean"}}}}}, "roles": {"type": "array", "items": {"type": "string", "enum": ["care_director", "plan_guide", "compliance", "finance", "physician"]}}, "broker": {"type": "object", "properties": {"active": {"type": "boolean"}, "ichra": {"type": "boolean"}}}}}, "households": {"$id": "Households", "type": "object", "additionalProperties": false, "required": ["_id", "person"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "filingAs": {"type": "string", "enum": ["s", "ms", "hh", "mj"]}, "providers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "magi": {"type": "number"}, "qual_events": {"type": "object", "patternProperties": {"^.*$": {"type": "string"}}}, "incomes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "amount": {"type": "number"}, "off": {"type": "boolean"}, "interval": {"type": "string", "enum": ["hour", "day", "week", "month", "quarter", "year", "once"]}, "class": {"type": "string", "enum": ["ee", "ic"]}, "estHours": {"type": "number"}}}}}, "deductions": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"off": {"type": "boolean"}, "amount": {"type": "number"}, "atl": {"type": "boolean"}}}}}, "monthsSinceSmoked": {"type": "number"}, "disabled": {"type": "boolean"}, "incarcerated": {"type": "boolean"}, "latino": {"type": "boolean"}, "native": {"type": "boolean"}, "pregnant": {"type": "boolean"}, "us_citizen": {"type": "boolean"}, "adl_assist": {"type": "boolean"}, "medicaid": {"type": "boolean"}, "medicaid_ineligible": {"type": "boolean"}, "outside_coverage": {"type": "boolean"}, "outside_coverage_end": {"type": "string"}, "job_coverage": {"type": "boolean"}, "members": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"relation": {"type": "string", "enum": ["self", "spouse", "child", "father", "mother", "grandfather", "grandmother", "grandson", "granddaughter", "son_in_law", "daughter_in_law", "uncle", "aunt", "nephew", "niece", "cousin", "guardian", "stepparent", "stepson", "stepdaughter", "adopted_child", "foster_child", "sister", "brother", "brother_in_law", "sister_in_law", "mother_in_law", "father_in_law", "ward", "sponsored_dependent", "dependent_minor_dependent", "ex_spouse", "court_appointed_guardian", "collateral_dependent", "life_partner", "annultant", "trustee", "other_relationship", "other_relative"]}, "dependent": {"type": "boolean"}, "annualIncome": {"type": "number"}, "address": {"$comment": "***addressSchema used here***"}, "monthsSinceSmoked": {"type": "number"}, "disabled": {"type": "boolean"}, "incarcerated": {"type": "boolean"}, "latino": {"type": "boolean"}, "native": {"type": "boolean"}, "pregnant": {"type": "boolean"}, "us_citizen": {"type": "boolean"}, "adl_assist": {"type": "boolean"}, "medicaid": {"type": "boolean"}, "medicaid_ineligible": {"type": "boolean"}, "outside_coverage": {"type": "boolean"}, "outside_coverage_end": {"type": "string"}, "job_coverage": {"type": "boolean"}}}}}}}, "ims": {"$id": "Ims", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subjectService": {"type": "string"}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "team": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "participant": {"type": "object", "properties": {"login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fp": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "sendTo": {"type": "string", "enum": ["in-app", "phone", "email"]}, "lastAt": {}, "status": {"type": "number", "enum": [0, 1, 2]}, "offline": {"type": "boolean"}}}, "views": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "at": {}}}}, "orphan": {"type": "boolean"}, "texting": {"type": "boolean"}, "calling": {"type": "boolean"}, "typing": {"type": "array", "items": {"type": "string"}}, "support": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fp": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "sendTo": {"type": "string", "enum": ["in-app", "phone", "email"]}, "lastAt": {}, "status": {"type": "number", "enum": [0, 1, 2]}, "offline": {"type": "boolean"}}}}}, "messages": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "sentAt": {}, "body": {"type": "string"}, "pid": {"type": "string"}, "source": {"type": "string", "enum": ["in-app", "phone", "email"]}, "openedBy": {"type": "array", "items": {"type": "string"}}, "errs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"code": {"type": "number"}, "message": {"type": "string"}, "pid": {"type": "string"}}}}}}}}}}, "issues": {"$id": "Issues", "type": "object", "additionalProperties": false, "required": ["_id", "by"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "service": {"type": "string"}, "record": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "type": {"type": "string", "enum": ["content", "complaint", "dispute"]}, "category": {"type": "string"}, "message": {"type": "string"}, "by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "status": {"type": "string", "enum": ["unopened", "assigned", "resolved", "escalated", "dropped"]}, "transaction": {"type": "string"}, "resolvedAt": {}, "channel": {"type": "string", "enum": ["app", "email", "phone", "mail"]}, "assigned": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "assignedAt": {}, "assignedHistory": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "at": {}, "notes": {"type": "string"}}}}, "treasuryComplaint": {"type": "boolean"}, "treasuryData": {"type": "object", "properties": {"date_input": {"type": "string"}, "date_received": {"type": "string"}, "user_name": {"type": "string"}, "received_by": {"type": "string"}, "account_ID": {"type": "string"}, "complaint_classification": {"type": "string", "enum": ["operational", "executive"]}, "complaint_category": {"type": "string", "enum": ["Privacy or Security", "Legal or Regulatory", "Product or Service"]}, "complaint_sub_category": {"type": "string"}, "complaint_description": {"type": "string"}, "alleges_UDAP_or_discrimination": {"type": "boolean"}, "user_stage": {"type": "string", "enum": ["prospect", "onboarding", "active"]}, "systemic_issue_identified": {"type": "boolean"}, "date_resolved": {"type": "string"}, "redress_reqd": {"type": "boolean"}, "description_of_corrective_action": {"type": "string"}, "internal_links": {"type": "array", "items": {"type": "string"}}, "user_correspondence_links": {"type": "array", "items": {"type": "string"}}, "reason_exceeded_15_days": {"type": "string"}}}}}, "junk-drawers": {"$id": "JunkDrawers", "type": "object", "additionalProperties": false, "required": ["_id", "drawer", "itemName", "itemId"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "drawer": {"type": "string"}, "itemName": {"type": "string"}, "itemId": {"type": "string"}, "data": {}}}, "leads": {"$id": "Leads", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "ledgers": {"$id": "Ledgers", "type": "object", "additionalProperties": false, "required": ["_id", "plan", "org", "person", "planYear"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "planYear": {"type": "number"}, "type": {"type": "string", "enum": ["fsa", "hra", "ichra", "dcap"]}, "account": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "transactions": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "logins": {"$id": "<PERSON><PERSON>", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "password": {"type": "string"}, "pendingPassword": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "did": {"type": "string"}, "ucan": {"type": "string"}, "fingerprints": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "keyPair": {"type": "object", "properties": {"publicKey": {"type": "string"}, "privateKey": {"type": "string"}, "alg": {"type": "string"}}}, "googleId": {"type": "string"}, "facebookId": {"type": "string"}, "twitterId": {"type": "string"}, "linkedinId": {"type": "string"}, "microsoftId": {"type": "string"}, "githubId": {"type": "string"}, "appleId": {"type": "string"}, "isVerified": {"type": "boolean"}, "verifyToken": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "verifyExpires": {}, "lastLogin": {}, "loginAttempts": {"type": "array", "items": {}}, "locked": {}, "lastLoginMethod": {"type": "string"}, "resetToken": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "resetExpires": {"anyOf": [{"type": "string"}, {"type": "object"}, {"type": "null"}]}}}, "markets": {"$id": "Markets", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "owners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "geo": {"$comment": "***geoJsonSchema used here***"}, "locations": {"type": "object", "patternProperties": {"[A-Z]{2}": {"type": "object", "properties": {"cities": {"type": "array", "items": {"type": "string"}}, "zips": {"type": "array", "items": {"type": "string"}}, "counties": {"type": "array", "items": {"type": "string"}}}}}}}}, "mbrs": {"$id": "Mbrs", "type": "object", "additionalProperties": false, "required": ["_id", "person", "coverage", "itemId"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "pm": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "inactive": {"type": "boolean"}, "itemId": {"type": "string"}}}, "meds": {"$id": "Meds", "type": "object", "additionalProperties": false, "required": ["_id", "rxcui"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "standard": {"type": "string"}, "rxcuis": {"type": "array", "items": {"type": "string"}}, "s_f": {"type": "array", "items": {"type": "string"}}, "variants": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "rxcui": {"type": "string"}, "medical_name": {"type": "string"}, "consumer_name": {"type": "string"}, "description": {"type": "string"}, "activeIngredient": {"type": "string"}, "synonyms": {"type": "string"}, "sbdOf": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ndcs": {"type": "array", "items": {"type": "string"}}, "info": {"type": "object", "properties": {"IN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "PIN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "MIN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SCD": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SCDF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SCDG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SCDC": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "GPCK": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "BN": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "BPCK": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "DF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "DFG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SBD": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SBDG": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SBDC": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SBDF": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}, "SBDFP": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "additionalProperties": true, "properties": {"rxcui": {"type": "string"}, "name": {"type": "string"}, "synonym": {"type": "string"}, "language": {"type": "string"}, "suppress": {"type": "string"}, "umlscui": {"type": "string"}}}}}}}}}, "networks": {"$id": "Networks", "type": "object", "additionalProperties": false, "required": ["_id", "name", "description"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "access": {"type": "string", "enum": ["public", "private", "searchable"]}, "subs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "managers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "writers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "images": {"$comment": "***imageSchema used here***"}, "name": {"type": "string"}, "description": {"type": "string"}, "lastSync": {"type": "string"}, "bundle_changes": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "plans": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "bundles": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "bundle_reqs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "plan_reqs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "bundle_invites": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "plans_invites": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "offers": {"$id": "Offers", "type": "object", "additionalProperties": false, "required": ["_id", "plan", "host", "fee", "feeType"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "role": {"type": "string", "enum": ["care_director", "plan_guide", "compliance", "finance", "physician"]}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "status": {"type": "string", "enum": ["pending", "rejected", "active"]}}}, "orgs": {"$id": "Orgs", "type": "object", "additionalProperties": true, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "bankAccounts": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "default": {"type": "boolean"}}}}}, "address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}, "affiliatedOrgs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "asg": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["A", "B"]}, "orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string", "enum": ["A", "B", "FSO", "M"]}}}}}}}}}, "avatar": {"$comment": "***imageSchema used here***"}, "budgets": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "controls": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"identical": {"type": "number"}, "common": {"type": "number"}, "control": {"type": "boolean"}, "orgs": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"identical": {"type": "number"}, "owners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "parent": {}, "total": {"type": "number"}}}}}, "brotherSister": {"type": "boolean"}, "parentSub": {"type": "boolean"}}}}}, "customers": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "coverImage": {"$comment": "***imageSchema used here***"}, "did": {"type": "string"}, "ein": {"type": "string"}, "email": {"type": "string"}, "emails": {"type": "array", "items": {"type": "string"}}, "hostAccounts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "industries": {"type": "array", "items": {"type": "string"}}, "images": {"$comment": "***imageSchema used here***"}, "groups": {"type": "object", "patternProperties": {"^.*$": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "groupIds": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "legalName": {"type": "string"}, "managementOrgs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "name": {"type": "string"}, "plans": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "providerAccounts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "owners": {"type": "array", "items": {"type": "object", "properties": {"did": {"type": "string"}, "percent": {"type": "number"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "attribute": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "executive": {"type": "boolean"}, "director": {"type": "boolean"}, "position": {"type": "string"}, "idService": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "address": {"$comment": "***addressSchema used here***"}}}}, "ownerSync": {"type": "boolean"}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "payroll_settings": {"type": "object", "properties": {"frequency": {"type": "string", "enum": ["daily", "weekly", "bi-weekly", "semi-monthly", "monthly", "quarterly", "annually", "int"]}}}, "public": {"type": "boolean"}, "ramp_vendor_id": {"type": "string"}, "refs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "singleMember": {"type": "boolean"}, "memberCount": {"type": "number"}, "memberCountAt": {}, "structure": {"type": "string", "enum": ["PARTNERSHIP", "SOLE_PROPRIETOR", "NONPROFIT", "CORPORATION", "S-CORP", "LLC", "LLP", "OTHER"]}, "taxes": {"type": "object", "properties": {"incomeTaxForm": {"type": "string"}}}, "taxStructure": {"type": "string"}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "careAccounts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "treasury": {"type": "object", "properties": {"customerId": {"type": "string"}, "id": {"type": "string"}, "business_profile": {"type": "object", "properties": {"support_email": {"type": "string"}, "annual_revenue": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}, "fiscal_year_end": {"type": "string"}}}, "estimated_worker_count": {"type": "number"}, "mcc": {"type": "string"}, "mcc_name": {"type": "string"}, "naics": {"type": "string"}, "sic": {"type": "string"}, "product_description": {"type": "string"}, "support_address": {"$comment": "***addressSchema used here***"}, "support_url": {"type": "string"}, "url": {"type": "string"}, "representatives": {"type": "array", "items": {"type": "object", "properties": {"isController": {"type": "boolean"}}}}}}}}, "website": {"type": "string"}}}, "passkeys": {"$id": "Passkeys", "type": "object", "additionalProperties": false, "required": ["_id", "login", "rpID", "credentialId", "public<PERSON>ey", "signCount"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "rpID": {"type": "string", "description": "Relying Party ID, e.g. example.com"}, "credentialId": {"type": "string", "description": "WebAuthnCredential.id (base64url)", "pattern": "^[A-Za-z0-9_-]+$"}, "publicKey": {"type": "string", "description": "WebAuthnCredential.publicKey encoded to base64url", "pattern": "^[A-Za-z0-9_-]+$"}, "signCount": {"type": "number", "description": "Verification counter (may not always increase)"}, "transports": {"type": "array", "items": {"type": "string", "enum": ["usb", "nfc", "ble", "internal", "hybrid", "cable"]}, "description": "Authenticator transport hints"}, "aaguid": {"type": "string", "description": "Authenticator AAGUID (UUID string if provided)"}, "backupEligible": {"type": "boolean", "description": "Derived from credentialDeviceType === \"multiDevice\""}, "backupState": {"enum": ["enabled", "disabled"], "description": "Derived from credentialBackedUp"}, "displayName": {"type": "string", "description": "User-visible label for the device"}}}, "pings": {"$id": "Pings", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subjectService": {"type": "string"}, "recipient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "recipientService": {"type": "string"}, "nouns": {"type": "array", "items": {"type": "string"}}, "verbs": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "subjectAvatarPath": {"type": "string"}, "subjectNamePath": {"type": "string"}, "link": {"type": "string"}, "tries": {"type": "number"}, "action": {"type": "string"}, "priority": {"type": "number"}, "category": {"type": "string"}, "methods": {"type": "object", "properties": {"internal": {"type": "object", "properties": {"send": {"type": "boolean"}, "sent": {"type": "string"}, "unsubbed": {"type": "boolean"}, "error": {"type": "string"}, "errorMessage": {"type": "string"}, "tries": {"type": "number"}, "opened": {"type": "string"}}}, "email": {"type": "object", "properties": {"send": {"type": "boolean"}, "sent": {"type": "string"}, "unsubbed": {"type": "boolean"}, "error": {"type": "string"}, "errorMessage": {"type": "string"}, "tries": {"type": "number"}, "opened": {"type": "string"}}}, "sms": {"type": "object", "properties": {"send": {"type": "boolean"}, "sent": {"type": "string"}, "unsubbed": {"type": "boolean"}, "error": {"type": "string"}, "errorMessage": {"type": "string"}, "tries": {"type": "number"}, "opened": {"type": "string"}}}}}}}, "plan-docs": {"$id": "PlanDocs", "type": "object", "additionalProperties": false, "required": ["_id", "plan", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "smb": {"type": "boolean"}, "public": {"type": "boolean"}, "printCount": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "class": {"type": "string", "enum": ["core", "125", "105", "misc", "spd"]}, "subClass": {"type": "string"}, "path": {"type": "string"}, "sectionsUpdatedAt": {}, "template": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "sections": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"key": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}}}}}}}}}}}, "plans": {"$id": "Plans", "type": "object", "additionalProperties": false, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "parent": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "spd": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ale": {"type": "boolean"}, "estFte": {"type": "number"}, "groups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "orgs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "vectorStoreIds": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}}}}}, "rfp": {"type": "object", "properties": {"care_director": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "public": {"type": "boolean"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}}}, "plan_guide": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "public": {"type": "boolean"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}}}, "compliance": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "public": {"type": "boolean"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}}}, "finance": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "public": {"type": "boolean"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}}}, "physician": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "public": {"type": "boolean"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}}}}}, "dependents": {"type": "object", "properties": {"excludeSpouse": {"type": "boolean"}}}, "info": {"type": "object", "additionalProperties": true, "properties": {"sponsor": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "ein": {"type": "string"}}}, "planAdmin": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}}, "fiduciary": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}}, "legalAgent": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}}, "number": {"type": "string"}, "numberEin": {"type": "string"}}}, "team": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "string", "enum": ["pending", "canceled", "active"]}, "role": {"type": "string", "enum": ["care_director", "plan_guide", "compliance", "finance", "physician"]}, "roleDescription": {"type": "string"}, "fee": {"type": "number"}, "feeType": {"type": "string", "enum": ["alg", "pepm", "pmpm", "flat"]}, "feeDescription": {"type": "string"}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "conflicts": {"type": "string"}, "approvedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "approvedAt": {}}}}}, "template": {"type": "boolean"}, "name": {"type": "string"}, "aka": {"type": "array", "items": {"type": "string"}}, "active": {"type": "boolean"}, "description": {"type": "string"}, "eligibility": {"type": "object", "properties": {"hours": {"type": "number"}, "term": {"type": "number"}}}, "enrollments": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"active": {"type": "boolean"}, "description": {"type": "string"}, "open": {"type": "string"}, "close": {"type": "string"}, "enrolled": {"type": "number"}, "lastUpdate": {}, "lastEnrolled": {"type": "string"}, "contributions": {"type": "object", "properties": {"lastAutoSet": {}, "lastManualSet": {}, "employer": {"type": "object", "properties": {"cafe": {"type": "number"}, "coverages": {"type": "number"}}}, "employee": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "needed": {"type": "object", "properties": {"preTax": {"type": "number"}, "postTax": {"type": "number"}, "total": {"type": "number"}, "def": {"type": "number"}}}, "byPlan": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}, "byCoverage": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"employer": {"type": "number"}, "employee": {"type": "number"}}}}}}}, "open_enroll": {"type": "boolean"}, "ppls": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "groups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "sentThrough": {"type": "number"}}}}}, "billEraser": {"type": "object", "properties": {"max": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "planYearStart": {}, "cafe": {"type": "object", "properties": {"hsa": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "fsa": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "dcp": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "pop": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "def": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "cash": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "taxStatus": {"type": "number", "enum": [0, 1, 2]}, "hsaEligible": {"type": "boolean"}, "gracePeriod": {"type": "number"}, "carryover": {"type": "number"}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "monthlySpend": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}}}, "hra": {"type": "object", "properties": {"ichra": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "recurs": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "ebhra": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "recurs": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}, "gchra": {"type": "object", "properties": {"active": {"type": "boolean"}, "doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "manual_doc": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "recurs": {"type": "number"}, "limits": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}}}}}}}, "coverages": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"type": {"type": "string"}, "groups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "budget": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "card": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "employeeBudget": {"type": "boolean"}, "employerContribution": {"type": "object", "properties": {"single": {"type": "number"}, "family": {"type": "number"}, "type": {"type": "string", "enum": ["flat", "percent"]}}}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "employerContribution": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"amount": {"type": "number"}, "family": {"type": "number"}, "match": {"type": "boolean"}, "type": {"type": "string", "enum": ["percent", "flat"]}, "percentType": {"type": "string", "enum": ["cost", "income"]}, "postTax": {"type": "number"}, "includeExtras": {"type": "boolean"}}}}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "networks": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "ppls": {"$id": "Ppls", "type": "object", "additionalProperties": false, "required": [], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "lastName": {"type": "string"}, "firstName": {"type": "string"}, "did": {"type": "string"}, "fid": {"type": "string"}, "wallet": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "emails": {"type": "array", "items": {"type": "string"}}, "phone": {"anyOf": [{"type": "null"}, {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}]}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "household": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "online": {"type": "boolean"}, "onlineAt": {}, "ramp_user_id": {"type": "string"}, "moovCardholderId": {"type": "string"}, "ims": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "moovAccounts": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "isController": {"type": "boolean"}}}}}, "cams": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "inGroups": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "inOrgs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "lastGroupSync": {}, "refs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "badges": {"type": "array", "items": {"type": "string"}}, "dob": {"type": "string"}, "ssn": {"type": "string"}, "itin": {"type": "string"}, "last4Itin": {"type": "string"}, "last4Ssn": {"type": "string"}, "gender": {"type": "string"}, "cleanupFlag": {"type": "boolean"}, "enrollments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "invites": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"by": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "at": {}, "reminded": {"type": "array", "items": {}}, "caps": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "required": ["id", "path"], "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "path": {"type": "string", "$comment": "This is the path of the caps.cap permission set for this person"}}}}}}}}}, "preferences": {"type": "object", "properties": {"notifications": {"type": "object", "properties": {"unsub": {"type": "array", "items": {"type": "string"}}, "emailUnsub": {"type": "array", "items": {"type": "string"}}, "smsUnsub": {"type": "array", "items": {"type": "string"}}}}}}, "card_user": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "budget_user": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "practitioners": {"$id": "Practitioners", "type": "object", "additionalProperties": false, "required": ["_id", "firstName", "lastName"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "name": {"type": "string"}, "name_prefix": {"type": "string"}, "gender": {"type": "string"}, "credential": {"type": "string"}, "auto_created": {"type": "boolean"}, "credentials": {"type": "array", "items": {"type": "string"}}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "soleProp": {"type": "string"}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "email": {"type": "string"}, "npi_date": {}, "npi_update": {}, "npi_status": {"type": "string"}, "npi": {"type": "string"}, "license": {"type": "string"}, "licenses": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"state": {"type": "string"}}}}, "cities": {"type": "array", "items": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}}}}, "license_states": {"type": "array", "items": {"type": "string"}}, "taxonomy1": {"type": "string"}, "taxonomy2": {"type": "string"}, "taxonomy3": {"type": "string"}, "providers": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}, "price-estimates": {"$id": "PriceEstimates", "type": "object", "additionalProperties": false, "required": ["_id", "code", "locationCode"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "medicare": {"type": "number"}, "cash": {"type": "number"}, "medicare_low": {"type": "number"}, "medicare_high": {"type": "number"}, "description": {"type": "string"}, "cash_low": {"type": "number"}, "cash_high": {"type": "number"}, "source": {"type": "string"}, "estimatedAt": {}, "alts": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"medicare": {"type": "number"}, "cash": {"type": "number"}, "medicare_low": {"type": "number"}, "medicare_high": {"type": "number"}, "description": {"type": "string"}, "cash_low": {"type": "number"}, "cash_high": {"type": "number"}, "source": {"type": "string"}, "estimatedAt": {}}}}, "code": {"type": "string"}, "rxcui": {"type": "string"}, "locationCode": {"type": "string"}, "listPrice": {"type": "number"}, "carrier": {"type": "string"}, "files": {"$comment": "***imageSchema used here***"}, "session": {"type": "string"}, "zip_code": {"type": "string"}, "state": {"type": "string"}}}, "prices": {"$id": "Prices", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "bundle": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "source": {"type": "string", "enum": ["vision", "bill", "provider", "dataset", "va", "upload", "ptf"]}, "description": {"type": "string"}, "notes": {"type": "string"}, "session": {"type": "string"}, "state": {"type": "string"}, "eraser": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "providerName": {"type": "string"}, "price": {"type": "number"}, "uom": {"type": "string"}, "batch": {"type": "string"}, "uid": {"type": "string"}, "subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "type": {"type": "string", "enum": ["procedures", "meds", "other"]}, "code": {"type": "string"}, "billing_code": {"type": "string"}, "carrier": {"type": "string"}, "ndcs": {"type": "array", "items": {"type": "string"}}, "relatedCheckedAt": {}, "ndc": {"type": "string"}, "ndc10": {"type": "string"}, "ndc11": {"type": "string"}, "labeler": {"type": "string"}, "product": {"type": "string"}, "package": {"type": "string"}, "rxcui": {"type": "string"}, "s_f": {"type": "string"}}}, "procedures": {"$id": "Procedures", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "standard": {"type": "string"}, "level": {"type": "string", "enum": ["1", "2"]}, "subLevel": {"type": "string"}, "category": {"type": "string"}, "pcc": {"type": "string"}, "parent": {"type": "string"}, "code": {"type": "string"}, "codes": {"type": "array", "items": {"type": "object", "properties": {"standard": {"type": "string"}, "code": {"type": "string"}}}}, "name": {"type": "string"}, "names": {"type": "array", "items": {"type": "string"}}, "layName": {"type": "string"}, "layDescription": {"type": "string"}, "descriptions": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}, "synonyms": {"type": "string"}}}, "providers": {"$id": "Providers", "type": "object", "additionalProperties": false, "required": ["_id", "name"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***"}, "name": {"type": "string"}, "legalName": {"type": "string"}, "address": {"$comment": "***addressSchema used here***"}, "addresses": {"$comment": "***addressSchema used here***"}, "email": {"type": "string"}, "emails": {"type": "array", "items": {"type": "string"}}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "images": {"$comment": "***imageSchema used here***"}, "npi": {"type": "string"}, "specialties": {"type": "array", "items": {"type": "string"}}, "patientUpdate": {"type": "string"}, "primaryType": {"type": "string"}, "otherTypes": {"type": "array", "items": {"type": "string"}}, "priority": {"type": "number"}, "tags": {"type": "array", "items": {"type": "string"}}, "licenses": {"type": "array", "items": {"type": "object", "properties": {"state": {"type": "string"}, "license": {"type": "string"}, "desc": {"type": "string"}, "code": {"type": "string"}}}}, "allVideos": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "videos": {"type": "object", "properties": {"general": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "memberships": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}, "bundles": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "bundles": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "bundle_length": {"type": "number"}, "auto_created": {"type": "boolean"}, "npi_status": {"type": "string"}, "cities": {"type": "array", "items": {"type": "object", "properties": {"city": {"type": "string"}, "state": {"type": "string"}}}}, "typeDescription": {"type": "string"}, "autoGenerated": {"type": "boolean"}, "googlePlacesId": {"type": "string"}, "customerId": {"type": "string"}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "geo": {"type": "object", "additionalProperties": true, "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["Feature"]}, "geometry": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string"}, "coordinates": {"type": "array", "items": {}}}}, "properties": {"type": "object"}, "addresses": {"type": "array", "items": {}}}}, "locations": {"$comment": "***serviceAddressSchema used here***"}, "cats": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "googleRating": {"type": "number"}, "googleRatingCount": {"type": "number"}, "googleMapsUri": {"type": "string"}, "regularHours": {"type": "object", "patternProperties": {"^[0-6]$": {"type": "object", "properties": {"open": {"type": "object", "properties": {"hour": {"type": "number"}, "minute": {"type": "number"}}}, "close": {"type": "object", "properties": {"hour": {"type": "number"}, "minute": {"type": "number"}}}}}}}, "websiteUri": {"type": "string"}, "payment_settings": {"type": "object", "properties": {"card_payments": {"type": "string"}, "ach_payments": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "default_ach": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "rates": {"$id": "Rates", "type": "object", "additionalProperties": false, "required": ["_id", "coverage", "state"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "state": {"type": "string"}, "stateKey": {"type": "string"}, "areas": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "zips": {"type": "array", "items": {"type": "string"}}, "premium": {"type": "object", "$comment": "Choose one of the premium structures flatPremium, rateByAge, fixedRates. The remaining properties are utilities for adjusting rates based on age, household count, etc.", "properties": {"flatPremium": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}, "$comment": "if the premium is a flat number per person, no age banding, no multi-person discount"}, "rateByAge": {"type": "object", "$comment": "flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "fixedRates": {"type": "object", "$comment": "this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "rateType": {"type": "string", "enum": ["flatPremium", "rateByAge", "fixedRates"], "$comment": "which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees"}, "multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc."}, "weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "A complex option for weighting rates by age such that multiDiscount doesn't apply equally to all ages"}, "baseDefault": {"type": "boolean", "$comment": "To be used with flatPremium - if true, this is the default rate if no rates are found"}, "breakpointAges": {"type": "array", "items": {"type": "number"}, "$comment": "This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates"}, "rateBreak": {"type": "string", "enum": ["graduated", "breakpoint"], "$comment": "Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property"}, "smokerFactor": {"type": "number", "$comment": "increase percentage for smoking status ie: 1.5 would be 50% more for smokers"}}}}}}, "premium": {"type": "object", "$comment": "Choose one of the premium structures flatPremium, rateByAge, fixedRates. The remaining properties are utilities for adjusting rates based on age, household count, etc.", "properties": {"flatPremium": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}, "$comment": "if the premium is a flat number per person, no age banding, no multi-person discount"}, "rateByAge": {"type": "object", "$comment": "flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}, "fixedRates": {"type": "object", "$comment": "this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "object", "properties": {"single": {"type": "number", "$comment": "rate for a single person"}, "plus_spouse": {"type": "number", "$comment": "rate for a single person plus a spouse"}, "plus_child": {"type": "number", "$comment": "rate for a single person plus a child"}, "plus_child__2": {"type": "number", "$comment": "rate for a single person plus 2 children"}, "plus_child__3": {"type": "number", "$comment": "rate for a single person plus 3 children"}, "family": {"type": "number", "$comment": "rate for a family"}}}}}, "rateType": {"type": "string", "enum": ["flatPremium", "rateByAge", "fixedRates"], "$comment": "which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees"}, "multiDiscount": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc."}, "weights": {"type": "object", "patternProperties": {"^(1?[0-9]|20)$": {"type": "number"}}, "$comment": "A complex option for weighting rates by age such that multiDiscount doesn't apply equally to all ages"}, "baseDefault": {"type": "boolean", "$comment": "To be used with flatPremium - if true, this is the default rate if no rates are found"}, "breakpointAges": {"type": "array", "items": {"type": "number"}, "$comment": "This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates"}, "rateBreak": {"type": "string", "enum": ["graduated", "breakpoint"], "$comment": "Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property"}, "smokerFactor": {"type": "number", "$comment": "increase percentage for smoking status ie: 1.5 would be 50% more for smokers"}}}}}, "refs": {"$id": "Refs", "type": "object", "additionalProperties": true, "required": ["_id", "name", "person"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "avatar": {"$comment": "***imageSchema used here***"}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "disabled": {"type": "string"}, "disabledBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "disabledAt": {}, "isHost": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "showBy": {"type": "boolean"}, "npn": {"type": "string"}, "sendTo": {"type": "string", "enum": ["in-app", "phone", "email"]}, "approved": {"type": "boolean"}, "approvedAt": {}, "approvedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "leads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "tags": {"type": "array", "items": {"type": "string"}}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "email": {"type": "string"}, "calendar": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "calendars": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "status": {"type": "string", "enum": ["invited", "req", "canceled", "active"]}, "teams": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"calendar": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}}}, "reqs": {"$id": "Reqs", "type": "object", "additionalProperties": true, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "fingerprint": {"type": "string"}, "refName": {"type": "string"}}}, "sales-taxes": {"$id": "SalesTaxes", "type": "object", "additionalProperties": false, "required": ["_id", "postal_code", "taxes", "state"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "country": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "postal_code": {"type": "string"}, "total_tax": {"type": "number"}, "taxes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "rate": {"type": "number"}, "type": {"type": "string", "enum": ["percent", "flat"]}}}}, "expires": {"type": "string"}}}, "se-plans": {"$id": "SePlans", "type": "object", "additionalProperties": false, "required": ["_id", "name", "plan_id", "state_code"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "public": {"type": "boolean"}, "template": {"type": "boolean"}, "fromTemplate": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "sim": {"type": "boolean"}, "rating_areas": {"type": "object", "patternProperties": {"^d+$": {"type": "object", "properties": {"name": {"type": "string"}, "zips": {"type": "array", "items": {"type": "string"}}, "fips": {"type": "array", "items": {"type": "string"}}, "cities": {"type": "array", "items": {"type": "string"}}, "county": {"type": "string"}, "rates": {"type": "object", "patternProperties": {"^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {"type": "number"}}}}}}}, "rateIncrease": {"type": "object", "properties": {"amount": {"type": "number"}, "date": {}}}, "all_fips": {"type": "array", "items": {"type": "string"}}, "first_3_zips": {"type": "array", "items": {"type": "string"}}, "all_zips": {"type": "array", "items": {"type": "string"}}, "fortyPremium": {"type": "number"}, "design": {"type": "string"}, "acaPlan": {"type": "boolean"}, "plan_id": {"type": "string"}, "state_code": {"type": "string"}, "business_year": {"type": "number"}, "import_date": {}, "rate_expiration": {}, "rate_effective_date": {}, "state_plan_id": {"type": "string"}, "issuer_id": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "premium": {"type": "number"}, "aptc_eligible_premium": {"type": "number"}, "eligible_dependents": {"type": "array", "items": {"type": "string"}}, "hsa_eligible": {"type": "boolean"}, "carrierName": {"type": "string"}, "carrierLogo": {"type": "string"}, "plan_type": {"type": "string"}, "benefits_url": {"type": "string"}, "formulary_url": {"type": "string"}, "network_url": {"type": "string"}, "brochure_url": {"type": "string"}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "exclusions": {"type": "string"}, "on_exchange": {"type": "boolean"}, "off_exchange": {"type": "boolean"}, "tobacco_lookback": {"type": "number"}, "tobacco": {"type": "string"}, "moop": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "deductible": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "metal": {"type": "string"}, "coins": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}, "copay": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}, "csr": {"type": "object", "patternProperties": {"^(02|03|04)$": {"type": "object", "properties": {"acaPlan": {"type": "boolean"}, "plan_id": {"type": "string"}, "state_code": {"type": "string"}, "business_year": {"type": "number"}, "import_date": {}, "rate_expiration": {}, "rate_effective_date": {}, "state_plan_id": {"type": "string"}, "issuer_id": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "premium": {"type": "number"}, "aptc_eligible_premium": {"type": "number"}, "eligible_dependents": {"type": "array", "items": {"type": "string"}}, "hsa_eligible": {"type": "boolean"}, "carrierName": {"type": "string"}, "carrierLogo": {"type": "string"}, "plan_type": {"type": "string"}, "benefits_url": {"type": "string"}, "formulary_url": {"type": "string"}, "network_url": {"type": "string"}, "brochure_url": {"type": "string"}, "benefits": {"type": "object", "patternProperties": {"^.*$": {"$comment": "benefits are just areas of coverage and aren't necessarily exhaustive. Label is the benefit name, covered is whether the plan covers it, detail is copays, limits, or other facts", "type": "object", "properties": {"label": {"type": "string"}, "covered": {"type": "boolean"}, "detail": {"type": "string"}, "cat": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "exclusions": {"type": "string"}, "on_exchange": {"type": "boolean"}, "off_exchange": {"type": "boolean"}, "tobacco_lookback": {"type": "number"}, "tobacco": {"type": "string"}, "moop": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "deductible": {"type": "object", "$comment": "For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits", "properties": {"medical": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}, "drug": {"type": "object", "properties": {"single": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}, "family": {"type": "object", "properties": {"in_network": {"type": "number"}, "in_network2": {"type": "number"}, "combined": {"type": "number"}, "oop": {"type": "number"}}}}}}}, "metal": {"type": "string"}, "coins": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}, "copay": {"type": "object", "$comment": "for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users", "properties": {"in_network": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "in_network2": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "combined": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}, "oon": {"type": "object", "properties": {"avg": {"type": "number"}, "display": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "object", "properties": {"emergency_room": {"type": "number"}, "primary_care": {"type": "number"}, "urgent_care": {"type": "number"}, "specialist": {"type": "number"}, "dental": {"type": "number"}, "drug": {"type": "number"}}}}}}}}}}}}}, "shops": {"$id": "Shops", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan_coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "aiChatCount": {"type": "number"}, "vectorStore": {"type": "object", "properties": {"id": {"type": "string"}, "fileIds": {"type": "array", "items": {"type": "string"}}, "updatedAt": {}, "resetId": {"type": "string"}}}, "resetId": {"type": "string"}, "limit_remaining": {"type": "number"}, "attest": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}}}, "tax_rate": {"type": "number"}, "planYear": {"type": "string"}, "version": {"type": "string"}, "consent_to_changes": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}, "decline_changes": {"type": "object", "properties": {"acceptedAt": {}, "ip": {"type": "string"}, "ua": {"type": "string"}, "copy": {"type": "string"}, "fingerprint": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "login": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "email": {"type": "string"}, "phone": {"type": "string"}, "signature": {"type": "string"}}}, "stats": {"type": "object", "properties": {"peopleEdited": {}, "inactive": {"type": "boolean"}, "people": {"type": "array", "items": {"type": "object", "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "inactive": {"type": "boolean"}, "age": {"type": "number"}, "gender": {"type": "string", "enum": ["male", "female"]}, "child": {"type": "boolean"}, "smoker": {"type": "boolean"}, "relation": {"type": "string"}}}}, "gender": {"type": "string", "enum": ["male", "female"]}, "preEx": {"type": "boolean"}, "income": {"type": "number"}, "age": {"type": "number"}, "spend": {"type": "number"}, "ded": {"type": "number"}, "spouse": {"type": "boolean"}, "plus": {"type": "number"}, "household_size": {"type": "number"}, "smoker": {"type": "boolean"}, "risk": {"type": "number"}, "city": {"type": "string"}, "place": {"type": "object", "properties": {"countyfips": {"type": "string"}, "zipcode": {"type": "string"}, "state": {"type": "string"}}}}}, "mult": {"type": "number"}, "useAptc": {"type": "boolean"}, "skipAptc": {"type": "boolean"}, "compare_ids": {"type": "array", "items": {"type": "string"}}, "spend_dist": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"spend": {"type": "number"}, "count": {"type": "number"}}}}}, "allSpend": {"type": "number"}, "byYear": {"type": "array", "items": {"type": "number"}}, "issuers": {"type": "array", "items": {"type": "string"}}, "aca_issuers": {"type": "array", "items": {"type": "string"}}, "lastRun": {}, "worst10": {"type": "array", "items": {"type": "number"}}, "distribution": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}}}, "distribution_ptc": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "patternProperties": {"^.*$": {"type": "number"}}}}}, "aptc": {"type": "number"}, "slcsp": {"type": "number"}, "coverage_scores": {"type": "object", "properties": {"top1": {"type": "number"}, "top3": {"type": "number"}, "last": {"type": "number"}, "pr": {"type": "number"}, "pr_spend": {"type": "number"}, "pw": {"type": "number"}, "pw_spend": {"type": "number"}, "premium": {"type": "number"}, "average": {"type": "number"}, "median": {"type": "number"}, "aptc": {"type": "number"}}}, "coverage_scores_ptc": {"type": "object", "properties": {"top1": {"type": "number"}, "top3": {"type": "number"}, "last": {"type": "number"}, "pr": {"type": "number"}, "pr_spend": {"type": "number"}, "pw": {"type": "number"}, "pw_spend": {"type": "number"}, "premium": {"type": "number"}, "average": {"type": "number"}, "median": {"type": "number"}, "aptc": {"type": "number"}}}, "spend": {"type": "number"}, "simsCount": {"type": "number"}, "coverages": {"type": "array", "items": {}}, "stage": {"type": "string", "enum": ["shopping", "review", "sign", "complete", "closed"]}, "status": {"type": "string"}, "policy": {"type": "string"}, "coverage": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "team": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "gps": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "majorMedical": {"type": "string"}, "keepOld": {"type": "boolean"}, "cashInLieu": {"type": "boolean"}, "deduction": {"type": "number"}, "interval": {"type": "string"}, "choices": {"type": "object", "patternProperties": {"^.*$": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}, "lastPremium": {"type": "number"}, "comments": {"type": "string"}, "fid": {"type": "string"}, "form": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "anon": {"type": "boolean"}, "jobTitle": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}}, "specs": {"$id": "Specs", "type": "object", "additionalProperties": false, "required": ["_id", "org", "plan", "planYear"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "planYear": {"type": "string"}, "enrollment": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "approvedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "approvedAt": {}, "event": {"type": "string", "enum": ["household", "employment", "plan", "hsa"]}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["approved", "rejected", "pending", "archived"]}, "message": {"type": "string"}, "thread": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "changedAt": {}, "changes": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"newVal": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}]}, "oldVal": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}]}}}}}, "specialEnrollment": {"type": "boolean"}}}, "teams": {"$id": "Teams", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "types": {"type": "array", "items": {"type": "string", "enum": ["sales", "support"]}}, "avatar": {"$comment": "***imageSchema used here***"}, "refs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "invited": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "req": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "contract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "calendar": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "phone": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "sms": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}, "email": {"type": "string"}, "priority": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "online": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "threads": {"$id": "Threads", "type": "object", "additionalProperties": true, "required": ["_id", "parent"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "upVotes": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "downVotes": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "voteCount": {"type": "number"}, "body": {"type": "string"}, "name": {"type": "string"}, "did": {"type": "string"}, "archives": {"type": "object", "properties": {"body": {"type": "array", "items": {"type": "string"}}}}, "owners": {"type": "array", "items": {"type": "object", "properties": {"did": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}}}}, "tags": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "service": {"type": "string"}}}}, "parent": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "service": {"type": "string"}}, "required": ["id", "service"]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "uploads": {"$id": "Uploads", "type": "object", "additionalProperties": true, "required": ["_id", "url", "fileId", "storage"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "url": {"type": "string"}, "cid": {"type": "string"}, "originalName": {"type": "string"}, "originalname": {"type": "string"}, "expires": {"type": "number"}, "usageVerified": {"type": "string"}, "usage": {"type": "array", "items": {"type": "object", "properties": {"subject": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "subjectModel": {"type": "string"}, "subjectPath": {"type": "string"}, "subjectArray": {"type": "boolean"}}}}, "info": {"type": "object", "properties": {"name": {"type": "string"}, "size": {"type": "number"}, "type": {"type": "string"}, "lastModifiedDate": {}}}, "session": {"type": "string"}, "temp": {"type": "boolean"}, "video": {"type": "boolean"}, "fileId": {"type": "string"}, "status": {"type": "string"}, "bucket": {"anyOf": [{"type": "string"}, {"type": "number"}]}, "storage": {"type": "string"}, "uploadChannel": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}}}, "visits": {"$id": "Visits", "type": "object", "additionalProperties": false, "required": ["_id", "provider", "patient", "care", "plan", "date"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "care": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "preventive": {"type": "boolean"}, "status": {"type": "string", "enum": ["appointment", "record", "cancelled"]}, "date": {}, "endDate": {}, "category": {"type": "string", "enum": ["emergency_room", "primary_care", "urgent_care", "dental", "specialist", "mental", "drug"]}, "er": {"type": "boolean"}, "conditions": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "name": {"type": "string"}, "medical_name": {"type": "string"}, "code": {"type": "string"}, "standard": {"type": "string"}, "notes": {"type": "string"}, "loggedAt": {}, "loggedBy": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}, "claims": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "claimReqs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "total": {"type": "number"}, "subtotal": {"type": "number"}, "pending": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "request": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "offer": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "paid": {"type": "object", "properties": {"amount": {"type": "number"}, "ded": {"type": "number"}, "coins": {"type": "number"}}}, "balance": {"type": "number"}, "balanceSyncedAt": {}, "enteredBy": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "auto": {"type": "boolean"}}}, "practitioners": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***"}}}}}, "wallets": {"$id": "Wallets", "type": "object", "additionalProperties": false, "required": ["_id", "owner"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "owner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "ownerService": {"type": "string", "enum": ["ppls", "orgs"]}, "methods": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}}}}}