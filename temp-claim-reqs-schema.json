    "$id": "ClaimReqs",
    "type": "object",
    "additionalProperties": false,
    "required": [
      "_id"
    ],
    "properties": {
      "_id": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "plan": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "org": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "patient": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "person": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "care": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "visit": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "claim": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "provider": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "practitioner": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "providerOrg": {
        "anyOf": [
          {
            "type": "string",
            "objectid": true
          },
          {
            "type": "object",
            "properties": {},
            "additionalProperties": true
          }
        ]
      },
      "threads": {
        "type": "array",
        "items": {
          "anyOf": [
            {
              "type": "string",
              "objectid": true
            },
            {
              "type": "object",
              "properties": {},
              "additionalProperties": true
            }
          ]
        }
      },
      "files": {
        "type": "object",
        "patternProperties": {
          "^.*$": {
            "$comment": "***imageSchema used here***"
          }
        }
      },
      "status": {
        "type": "string",
        "enum": [
          "unopened",
          "pending",
          "approved",
          "rejected"
        ]
      },
      "removeRequest": {
        "type": "boolean"
      },
      "claimData": {
        "type": "object",
        "properties": {
          "_id": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "visit": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "plan": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "patient": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "person": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "practitioner": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "provider": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "procedure": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "med": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "coverage": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "date": {},
          "misc": {
            "type": "string"
          },
          "enteredBy": {
            "type": "object",
            "properties": {
              "id": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "org": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "auto": {
                "type": "boolean"
              }
            }
          },
          "log": {
            "type": "object",
            "properties": {
              "code": {
                "type": "string"
              },
              "standard": {
                "type": "string"
              }
            }
          },
          "category": {
            "type": "string",
            "enum": [
              "emergency_room",
              "primary_care",
              "urgent_care",
              "dental",
              "specialist",
              "mental",
              "drug"
            ]
          },
          "description": {
            "type": "string"
          },
          "notes": {
            "type": "string"
          },
          "preventive": {
            "type": "boolean"
          },
          "adj": {
            "type": "object",
            "required": [
              "adjBy",
              "adjAt",
              "fp"
            ],
            "properties": {
              "adjBy": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "adjAt": {},
              "fp": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "enrollment": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "ded": {
                "type": "number"
              },
              "coins": {
                "type": "number"
              },
              "copay": {
                "type": "number"
              },
              "coverage": {
                "anyOf": [
                  {
                    "type": "string",
                    "objectid": true
                  },
                  {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": true
                  }
                ]
              },
              "waived_ded": {
                "type": "number"
              },
              "waived_coins": {
                "type": "number"
              },
              "waived_copay": {
                "type": "number"
              },
              "preventive": {
                "type": "boolean"
              },
              "amount": {
                "type": "number"
              },
              "qty": {
                "type": "number"
              },
              "total": {
                "type": "number"
              },
              "notes": {
                "type": "string"
              }
            }
          },
          "pending": {
            "type": "object",
            "properties": {
              "amount": {
                "type": "number"
              },
              "ded": {
                "type": "number"
              },
              "coins": {
                "type": "number"
              }
            }
          },
          "request": {
            "type": "object",
            "properties": {
              "amount": {
                "type": "number"
              },
              "ded": {
                "type": "number"
              },
              "coins": {
                "type": "number"
              }
            }
          },
          "offer": {
            "type": "object",
            "properties": {
              "amount": {
                "type": "number"
              },
              "ded": {
                "type": "number"
              },
              "coins": {
                "type": "number"
              }
            }
          },
          "paid": {
            "type": "object",
            "properties": {
              "amount": {
                "type": "number"
              },
              "ded": {
                "type": "number"
              },
              "coins": {
                "type": "number"
              }
            }
          },
          "amount": {
            "type": "number"
          },
          "subtotal": {
            "type": "number"
          },
          "total": {
            "type": "number"
          },
          "qty": {
            "type": "number"
          },
          "balanceSyncedAt": {},
          "balance": {
            "type": "number"
          },
          "adjHistory": {
            "type": "array",
            "items": {
              "type": "object",
              "required": [
                "adjBy",
                "adjAt",
                "fp"
              ],
              "properties": {
                "adjBy": {
                  "anyOf": [
                    {
                      "type": "string",
                      "objectid": true
                    },
                    {
                      "type": "object",
                      "properties": {},
                      "additionalProperties": true
                    }
                  ]
                },
                "adjAt": {},
                "fp": {
                  "anyOf": [
                    {
                      "type": "string",
                      "objectid": true
                    },
                    {
                      "type": "object",
                      "properties": {},
                      "additionalProperties": true
                    }
                  ]
                },
                "enrollment": {
                  "anyOf": [
                    {
                      "type": "string",
                      "objectid": true
                    },
                    {
                      "type": "object",
                      "properties": {},
                      "additionalProperties": true
                    }
                  ]
                },
                "ded": {
                  "type": "number"
                },
                "coins": {
                  "type": "number"
                },
                "copay": {
                  "type": "number"
                },
                "coverage": {
                  "anyOf": [
                    {
                      "type": "string",
                      "objectid": true
                    },
                    {
                      "type": "object",
                      "properties": {},
                      "additionalProperties": true
                    }
                  ]
                },
                "waived_ded": {
                  "type": "number"
                },
                "waived_coins": {
                  "type": "number"
                },
                "waived_copay": {
                  "type": "number"
                },
                "preventive": {
                  "type": "boolean"
                },
                "amount": {
                  "type": "number"
                },
                "qty": {
                  "type": "number"
                },
                "total": {
                  "type": "number"
                },
                "notes": {
                  "type": "string"
                }
              }
            }
          },
          "taxes": {
            "type": "object",
            "patternProperties": {
              "^.*$": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string"
                  },
                  "amount": {
                    "type": "number"
                  }
                }
              }
            }
          },
          "fees": {
            "type": "object",
            "patternProperties": {
              "^.*$": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string"
                  },
                  "amount": {
                    "type": "number"
                  }
                }
              }
            }
          },
          "payments": {
            "type": "array",
            "items": {
              "anyOf": [
                {
                  "type": "string",
                  "objectid": true
                },
                {
                  "type": "object",
                  "properties": {},
                  "additionalProperties": true
                }
              ]
            }
          },
          "reduced": {
            "type": "object",
            "properties": {
              "from": {
                "type": "number"
              },
              "to": {
                "type": "number"
              },
              "on": {
                "type": "string"
              }
            }
          },
          "status": {
            "type": "number",
            "enum": [
              0,
              1,
              2,
              3,
              4,
              5
            ]
          },
          "settings": {
            "type": "object",
            "properties": {
              "tax": {
                "$comment": "***taxSchema used here***"
              }
            }
          },
          "files": {
            "type": "object",
            "patternProperties": {
              "^.*$": {
                "$comment": "***imageSchema used here***"
              }
            }
          },
          "threads": {
            "type": "array",
            "items": {
              "anyOf": [
                {
                  "type": "string",
                  "objectid": true
                },
                {
                  "type": "object",
                  "properties": {},
                  "additionalProperties": true
                }
              ]
            }
          },
          "env": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "host": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "ref": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "changeLog": {
            "anyOf": [
              {
                "type": "string",
                "objectid": true
              },
              {
                "type": "object",
                "properties": {},
                "additionalProperties": true
              }
            ]
          },
          "editMap": {
            "type": "object"
          },
          "deleted": {
            "type": "boolean"
          },
          "session_fp": {
            "type": "string"
          },
          "deletedAt": {},
          "updatedAt": {},
          "createdAt": {},
          "createdBy": {
            "$comment": "***updatesSchema used here***"
          },
          "updatedBy": {
            "$comment": "***updatesSchema used here***"
          },
          "updatedByHistory": {
            "$comment": "***updatesSchema used here***"
          }
        }
      }
    }
