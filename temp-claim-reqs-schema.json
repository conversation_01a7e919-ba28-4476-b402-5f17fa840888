{"$id": "ClaimReqs", "type": "object", "additionalProperties": false, "required": ["_id"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plan": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "patient": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "person": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "care": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "visit": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "claim": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "provider": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "practitioner": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "providerOrg": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "threads": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "files": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***", "type": "object"}}}, "status": {"type": "string", "enum": ["unopened", "pending", "approved", "rejected"]}, "removeRequest": {"type": "boolean"}}}