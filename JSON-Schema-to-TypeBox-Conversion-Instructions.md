# Detailed Instructions for JSON Schema to TypeBox Conversion Process

## 🚨 CRITICAL RULE 🚨
**ALWAYS USE `server/schema-diff/service-schemas-with-common-indicators.json` AS THE SINGLE SOURCE OF TRUTH**
**NEVER LOOK AT EXISTING TYPEBOX SCHEMAS - THEY ARE INCORRECT**

## Step-by-Step Process:

### Step 1: Extract the Exact JSON Schema
1. Open `server/schema-diff/service-schemas-with-common-indicators.json`
2. Find the service name (e.g., "cares", "bills", etc.)
3. Copy the ENTIRE JSON schema for that service from `"service-name": {` to the closing `}`
4. Save it to a temporary file `temp-[service-name]-schema.json`
5. **CRITICAL**: Include ALL properties exactly as they appear - do not modify anything

### Step 2: Handle $comment Fields
Before running the converter, find any fields with ONLY `$comment` properties and add `"type": "object"`:
```json
// BEFORE:
"fieldName": {
  "$comment": "***schemaName used here***"
}

// AFTER:
"fieldName": {
  "$comment": "***schemaName used here***",
  "type": "object"
}
```

### Step 3: Run TypeBox Conversion
Execute: `cd server && npx schema2typebox -i ../temp-[service-name]-schema.json --output-stdout`

### Step 4: Replace $comment References with Common Schemas
In the converted TypeBox code, replace `Type.Any()` fields that had `$comment` references:
- `***imageSchema used here***` → use `imageSchema`
- `***addressSchema used here***` → use `addressSchema`  
- `***taxSchema used here***` → use `taxSchema`
- Add the schema to imports if needed

### Step 5: Check Pattern Properties
1. Search `server/schema-diff/pattern-properties-usage-clean.json` for the service name
2. If pattern properties exist, convert `Type.Unknown()` to `Type.Record(Type.String(), [proper object structure])`

### Step 6: Convert Union Types to ObjectIdSchema
Replace all:
```typescript
Type.Union([
  Type.String({ objectid: true }),
  Type.Object({}, { additionalProperties: true }),
])
```
With: `ObjectIdSchema()`

### Step 7: Replace Type.Unknown() with Type.Any()
Change all `Type.Unknown()` to `Type.Any()` for consistency

### Step 8: Update Main Schema
1. Add `commonPatch` to imports if not present
2. Replace the entire main schema export with converted code
3. Ensure it includes `{ $id: 'ServiceName', additionalProperties: false }`
4. Keep `...commonFields` at the end

### Step 9: Update Patch Schema Pattern
Replace the patch schema section with:
```typescript
// Pick ObjectId fields and nested ObjectId fields for query properties
const [serviceName]QueryProperties = Type.Pick([serviceName]Schema, ['_id', 'field1', 'field2', ...])

export const [serviceName]PatchSchema = commonPatch([serviceName]Schema, [pushPullArray], [serviceName]QueryProperties)
```

Where:
- `pushPullArray` is `[]` for no operators, `['$addToSet', '$pull']` for array operations, `['$inc']` for increment, etc.
- Include ALL ObjectId fields from the schema in the Pick list

### Step 10: Clean Up
1. Remove temporary JSON file
2. Verify the schema compiles without errors

## Common Patterns:

### Push/Pull Arrays:
- Empty array `[]` - no special operators
- `['$addToSet', '$pull']` - for services with array operations
- `['$inc']` - for services with increment operations

### ObjectId Fields to Include in Pick:
- Always include `_id`
- Include any field that uses `ObjectIdSchema()`
- Include nested ObjectId fields (like `mandate.fingerprint`, `prices.threads`)

### Import Updates:
Only add imports that are actually used:
- `commonPatch` - always needed
- `imageSchema` - if replacing `***imageSchema used here***`
- `addressSchema` - if replacing `***addressSchema used here***`
- `taxSchema` - if replacing `***taxSchema used here***`

## Validation Checklist:
- [ ] Used exact JSON schema from source of truth file
- [ ] All properties from JSON schema are present in TypeBox schema
- [ ] $comment fields replaced with proper common schemas
- [ ] Pattern properties converted to Type.Record()
- [ ] Union types converted to ObjectIdSchema()
- [ ] Type.Unknown() changed to Type.Any()
- [ ] $id property matches service name
- [ ] commonFields included
- [ ] Patch schema uses commonPatch pattern
- [ ] All ObjectId fields included in Pick list
- [ ] Temporary file cleaned up

## Example Complete Conversion:
```typescript
export const serviceSchema = Type.Object({
  _id: ObjectIdSchema(),
  field1: Type.String(),
  field2: Type.Optional(ObjectIdSchema()),
  // ... all fields from JSON schema
  ...commonFields
}, { $id: 'ServiceName', additionalProperties: false })

// Pick ObjectId fields and nested ObjectId fields for query properties
const serviceQueryProperties = Type.Pick(serviceSchema, ['_id', 'field2', 'nestedField'])

export const servicePatchSchema = commonPatch(serviceSchema, [], serviceQueryProperties)
```

This process ensures 100% accuracy and consistency across all schema conversions.
