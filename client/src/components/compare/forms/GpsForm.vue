<template>
  <div class="_fw">
    <div class="__title">Your Household</div>
    <age-zip-family @change="changeHousehold" :place="shop.stats?.place" @place="addPlace" @zip-data="setZipData" :shop="shop"></age-zip-family>

    <q-separator class="q-my-sm"></q-separator>

    <div class="__title row items-center">
      <div>
        <div>Your Health Plan Details</div>
        <q-chip clickable @click="setCoverageId()" dense color="transparent" class="font-7-8r text-accent">
          <span>{{ $possiblyPlural('Option', Object.keys(gpsForm.coverages || {})) }} Added</span>
          <q-icon class="q-ml-sm" name="mdi-pencil-box"></q-icon>
        </q-chip>
      </div>
      <q-space></q-space>
     <ai-upload-chip :save="gpsSave" v-model:gps="gpsForm" v-if="canEdit"></ai-upload-chip>
    </div>

    <div class="row q-py-sm">
      <q-checkbox class="text-ir-mid tw-six font-3-4r" size="sm" label="Employer has 50+ full time employees"
                  :model-value="!!gpsForm.ale"
                  @update:modelValue="setGps('ale', $event); emit('ale', $event)"></q-checkbox>
    </div>

    <q-tab-panels class="_panel" animated :model-value="!!coverageId || !Object.keys(gpsForm.coverages || {}).length">
      <q-tab-panel class="_panel" :name="true">

        <div class="_frm">
          <div class="_lbl">
            <div>Your premium</div>
            <span>Check your paycheck</span>
          </div>
          <div class="_bod">
            <div class="flex items-center">
              <money-input
                  class="w150 mw60"
                  dense prefix="$"
                  v-model="shopForm.deduction"
                  @update:model-value="autoSaveShop('deduction'); autoSaveC('premium')"
              ></money-input>

              <plan-interval-chip v-model="shopForm.interval" @update:modelValue="autoSaveShop('interval'); autoSaveC('premium')" picker></plan-interval-chip>
            </div>
          </div>
        </div>

        <q-slide-transition>
          <div class="_fw" v-if="shopForm.deduction || shopForm.deduction === 0">
            <div class="_frm">
              <div class="_lbl">
                <div>Employer Pays</div>
                <span>({{ shopForm.interval }})</span>
              </div>
              <div class="_bod">
                <q-radio size="xs" val="percent" :model-value="ec.type" @update:modelValue="setEc('type', $event)"
                         label="Percent"></q-radio>
                <q-radio size="xs" val="flat" :model-value="ec.type" @update:modelValue="setEc('type', $event)"
                         label="Dollar Amount"></q-radio>
                <div class="row items-center">
                  <money-input
                      label="Individual"
                      :prefix="ec.type === 'percent' ? '' : '$'"
                      :suffix="ec.type === 'percent' ? '%' : ''"
                      class="w150 mw60 q-px-xs"
                      dense
                      :model-value="ec.amount * (intervals[shopForm.interval]?.factor || 1)"
                      @update:modelValue="setEc('amount', $event);autoSaveC('premium')"
                  ></money-input>
                  <money-input
                      label="Family"
                      :prefix="ec.type === 'percent' ? '' : '$'"
                      :suffix="ec.type === 'percent' ? '%' : ''"
                      class="w150 mw60 q-px-xs"
                      dense
                      :model-value="ec.family * (intervals[shopForm.interval]?.factor || 1)"
                      @update:modelValue="setEc('family', $event);autoSaveC('premium')"
                  ></money-input>
                </div>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">Deductible</div>
              <div class="_bod flex items-center">
                <money-input
                    label="Individual"
                    class="w150 mw60 q-px-xs"
                    dense prefix="$"
                    :model-value="cForm.deductible?.single"
                    @update:modelValue="setDed('single', $event)"
                ></money-input>
                <money-input
                    label="Family"
                    class="w150 mw60 q-px-xs"
                    dense
                    prefix="$"
                    :model-value="cForm.deductible?.family"
                    @update:modelValue="setDed('family', $event)"
                ></money-input>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">Max OOP</div>
              <div class="_bod flex items-center">
                <money-input label="Individual" class="w150 mw60 q-px-xs" dense prefix="$"
                             :model-value="cForm.moop?.single"
                             @update:modelValue="setMoop('single', $event)"></money-input>
                <money-input label="Family" class="w150 mw60 q-px-xs" dense prefix="$" :model-value="cForm.moop?.family"
                             @update:modelValue="setMoop('family', $event)"></money-input>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">Network Type</div>
              <div class="_bod">
                <q-radio v-for="tp in ['hmo', 'ppo', 'pos', 'epo', 'open']" size="sm" :key="tp" :label="tp.toUpperCase()" :model-value="cForm.plan_type" @update:modelValue="setNetworkType" :val="tp"></q-radio>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">
                <div>Coinsurance %</div>
                <span>% you pay before max oop (common is 20-40%)</span>

              </div>
              <div class="_bod">
                <money-input class="w150 mw60" dense prefix="" :decimal="0" suffix="%" :model-value="cForm.coinsurance?.amount"
                             @update:model-value="autoSaveC('coinsurance', { amount: $event})"></money-input>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">Insurance Carrier</div>
              <div class="_bod">
                <q-input @focus="setCarrierBlur(false)" @blur="setCarrierBlur(true)"
                         :placeholder="cForm.carrierName || ''" dense
                         class="_fw" v-model="carrierSearch"></q-input>
                <q-slide-transition>
                  <div v-if="!carrierBlur" class="flex items-center">
                    <q-chip color="ir-bg2" dense square v-for="(c, i) in carriers" :key="`c-${i}`" clickable
                            @click="setCarrier(c)">
                      <span>{{ c }}</span>
                    </q-chip>
                  </div>
                </q-slide-transition>

              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">
                <div>Plan or Company</div>
                <span>If you want to share with others so they can see/contribute</span>
              </div>

              <div class="_bod">
                <q-input dense class="_fw" v-model="cForm.name" @update:model-value="autoSaveC('name')"></q-input>
              </div>
            </div>

            <div class="_frm">
              <div class="_lbl">
                <div>Rate Type</div>
                <span>If you know it</span>
              </div>
              <div class="_bod">
                <q-radio size="xs" :model-value="cForm.premium?.rateType" @update:modelValue="setRateType"
                         val="flatPremium"
                         label="Same rates for all ages"></q-radio>
                <q-radio size="xs" :model-value="cForm.premium?.rateType" @update:modelValue="setRateType"
                         val="fixedRates"
                         label="Age-based w/ family discounts"></q-radio>
                <q-radio size="xs" :model-value="cForm.premium?.rateType" @update:modelValue="setRateType"
                         val="fixedRates"
                         label="Rate by age"></q-radio>
              </div>
            </div>
          </div>
        </q-slide-transition>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">

        <div class="font-7-8r tw-five text-ir-deep q-py-md">
          <div class="font-1-1-8r">Select Coverage Option</div>
          <div>Multiple coverage options have been added for your plan. Choose one to compare</div>
        </div>
        <gps-coverages editing :gps="gpsForm" @update:modelValue="setCoverageId($event.id)" @add="coverageId = cForm.id"></gps-coverages>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import PlanIntervalChip from 'components/plans/utils/PlanIntervalChip.vue';
  import GpsCoverages from 'components/compare/cards/GpsCoverages.vue';
  import AiUploadChip from 'components/compare/utils/AiUploadChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useGps} from 'stores/gps';
  import {HForm, HSave} from 'src/utils/hForm';
  import {ref, computed, watch, nextTick} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useRoute, useRouter} from 'vue-router';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {$infoNotify, $possiblyPlural, fakeId} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import {useMarketplace} from 'stores/marketplace';
  import {costLimits} from 'components/plans/utils';
  import {useEnvStore} from 'stores/env';
  import {sessionFamily} from 'components/households/utils/session-family';
  import {getCoverageRate, getFixedRateKey} from 'components/coverages/utils/display';
  import {intervals} from 'components/plans/utils/intervals';
  import {otherKeys, slcspMultiples} from 'components/market/utils/rates';
  import {_get} from 'symbol-syntax-utils';
  import {householdToShopHousehold, shopHouseholdToHousehold} from 'components/compare/utils/compare-shop';
  import {shopGet} from 'components/market/utils/shop-get';
  import {useShops} from 'stores/shops';
  import {getStats} from 'components/market/shop/utils';

  const gpStore = useGps();
  const marketStore = useMarketplace();
  const route = useRoute();
  const router = useRouter();
  const envStore = useEnvStore()
  const shopStore = useShops();


  const { person } = loginPerson();

  const emit = defineEmits(['change',  'update:gps', 'update:coverage', 'update:shop', 'ale']);
  const props = defineProps({
    gps: { required: false },
    shop: { required: false },
    coverage: { required: false },
    planId: String
  })

  const { item: dbGps } = idGet({
    store: gpStore,
    value: computed(() => props.gps || route.params.gpsId)
  ,
    useAtcStore
  })

  const gpsAfterFn = (val) => {
    emit('update:gps', val);
    router.push({ ...route, params: { gpsId: val._id } });
  }
  const { form: gpsForm, save: gpsSave } = HForm({
    store: gpStore,
    value: dbGps,
    notify: false,
    beforeFn: (val) => {
      if (!val.owner) val.owner = person.value._id;
      if (!val.org) val.org = LocalStorage.getItem('org_id')
      if (!val.plan) val.plan = props.planId
      return val;
    },
    afterFn: gpsAfterFn
  })

  const { autoSave: autoSaveGps, setForm: setGps } = HSave({
    form: gpsForm,
    store: gpStore,
    save: gpsSave,
    afterFn: gpsAfterFn,
    pause: computed(() => !dbGps.value?._id)
  })

  const canEdit = computed(() => !gpsForm.value.owner || gpsForm.value.owner === person.value._id || (gpsForm.value.editors || []).includes(person.value._id))

  const ec = computed(() => gpsForm.value.employerContribution || {})
  const setEc = (path, v) => {
    const val = ['amount', 'family'].includes(path) ? v / (intervals[shopForm.value.interval]?.factor || 1) : v
    gpsForm.value.employerContribution = { ...gpsForm.value.employerContribution, [path]: val }
    autoSaveGps('employerContribution')
    autoSaveC('premium')
  }

  const { h$: s$ } = HFind({
    store: shopStore,
    params: computed(() => {
      const query = {};
      if (dbGps.value._id) query.gps = dbGps.value._id;
      if (person.value._id) query.person = person.value._id;
      else query.fingerprint = LocalStorage.getItem('fpId') || fakeId;
      return {
        query
      }
    })
  })
  const setShop = ref({});
  const shopId = computed(() => {
    if (setShop.value._id) return setShop.value._id;
    const local = LocalStorage.getItem('shop_id')
    if (local) return local
    if (s$.data[0]) return s$.data[0]._id;
    return undefined;
  })

  const { shop:dbShop, setStat } = shopGet({ shop: shopId })

  const { household, age } = sessionFamily(envStore, { shop: computed(() => props.shop) })


  const shopFn = (defs) => {
    return {
      interval: 'bi-weekly',
      stats: getStats(),
      ...defs
    }
  }

  const shopAfterFn = (val) => {
    if (val._id) {
      emit('update:shop', val);
      LocalStorage.setItem('shop_id', val._id);
    }
  }

  const { form: shopForm, save: saveShop } = HForm({
    store: shopStore,
    value: dbShop,
    formFn: shopFn,
    notify: false,
    beforeFn: (val) => {
      if (!val.gps) val.gps = gpsForm.value._id;
      if (!val.person) val.person = person.value._id;
      if (!val.fingerprint) val.fingerprint = LocalStorage.getItem('fpId')
      return val;
    },
    afterFn: shopAfterFn
  })

  const { autoSave: autoSaveShop } = HSave({
    form: shopForm,
    store: shopStore,
    save: saveShop,
    afterFn: shopAfterFn,
    pause: computed(() => !dbShop.value?._id),
    onChange: (val) => emit('update:shop', val)
  })

  const coverageId = ref()
  const cFn = (defs) => {
    return {
      type: 'mm',
      covered: 'group',
      name: 'My Employer Health Plan',
      moop: {
        ...costLimits.moop
      },
      id: coverageId.value || new Date().getTime().toString() + '-' + 0,
      ...defs
    }
  }
  const cForm = ref(cFn())
  const cTo = ref();
  const cPatch = ref({})


  const estimatePremiumSplit = (totalPremium, aGes) => {
    // Replace child age multiple with assumed constant (0.765 is used in ACA for under 21)
    const ages = aGes.sort((a, b) => b - a)
    const getMultiple = (age) => {
      return slcspMultiples[age] ?? 1.0; // Fallback to 1.0 if not found
    };
    // Get the list of age-based multipliers
    const multipliers = ages.map(age => getMultiple(age));
    // Sum total multiplier value
    const totalMultiplier = multipliers.reduce((sum, m) => sum + m, 0);
    // Divide the total premium proportionally
    const perPerson = multipliers.map(m => +(totalPremium * (m / totalMultiplier)).toFixed(2));

    const byAge = {};
    ages.map((a, i) => {
      byAge[a] = perPerson[i]
    })
    return { perPerson, byAge, ages };
  }
  const rateByAge = ref({})
  const fixedRates = ref({})
  const flatPremium = ref({})


  /** calculate the premium based on employee/employer contributions and set premium rates accordingly */
  const setPremium = async () => {

    // if((gpsForm.value.coverages || {})[coverageId.value]?.fromFile) return;
    const key = getFixedRateKey({ enrolled: household.value.people }).key

    const factor = intervals[shopForm.value.interval]?.factor || 1
    const fromMyPaycheck = (shopForm.value.deduction || 0) / factor;
    // console.log('from my paycheck', key, fromMyPaycheck)
    let blendedPremium = fromMyPaycheck;

    const { amount: ecAmount, family: ecFamily } = ec.value || {}

    const { premium: coveragePremium } = cForm.value;

    const setRates = (aGe, val, k) => {
      if (!val) return;
      if (k) {
        const cAge = (coveragePremium?.fixedRates || {})[aGe]
        fixedRates.value[aGe] = { ...cAge, ...fixedRates.value[aGe], [k]: val }
        for (const k2 in otherKeys[k]) {
          fixedRates.value[aGe] = {
            ...fixedRates.value[aGe],
            ...cAge,
            [k2]: val * otherKeys[k][k2],
          }
        }
      }
      if (!k || k === 'single')
        rateByAge.value[aGe] = val
    }

    const setFlatPremium = (p, k) => {
      if (coveragePremium?.rateType === 'flatPremium') {
        flatPremium.value[k] = p;
        // console.log('loop other keys', k, p);
        for (const k2 in otherKeys[k]) {
          // console.log('k2', k2, p * otherKeys[k][k2]);
          flatPremium.value[k2] = p * otherKeys[k][k2]
        }
      }
    }
    /** if there is an employer contribution, reverse-engineer the premium from the deduction and set the age based rates as well */

    if (ec.value.type === 'flat') {
      if (key === 'single') {
        blendedPremium = fromMyPaycheck + (ecAmount || 0)
        setRates(household.value.people[0].age, blendedPremium, 'single')
        setFlatPremium(blendedPremium, key)

      } else {
        // console.log('else family', ecAmount, ecFamily, fromMyPaycheck, factor)
        blendedPremium = fromMyPaycheck + ((ecAmount || 0) + (ecFamily || 0))
        const { byAge, ages } = estimatePremiumSplit(blendedPremium, household.value.people.map(a => a.age))
        for (const k in byAge) setRates(k, byAge[k], 'single')
        setRates(ages[0], blendedPremium, key)
        setFlatPremium(blendedPremium, key)
      }
    } else {
      if (key === 'single') {
        blendedPremium = fromMyPaycheck / (1 - (ecAmount || 0))
        setRates(household.value.people[0].age, blendedPremium, 'single')
        setFlatPremium(blendedPremium, key)

      } else {
        const {
          byAge,
          perPerson,
          ages
        } = estimatePremiumSplit(blendedPremium, household.value.people.map(a => a.age))

        const ee = byAge[age.value || household.value.people[0].age] || perPerson[0];
        const family = (fromMyPaycheck) - ee;
        const eeTotal = ee / (1 - (ecAmount || 0))
        const familyTotal = family / (1 - (ecFamily || 0))

        blendedPremium = eeTotal + familyTotal

        const updatedEstimate = estimatePremiumSplit(blendedPremium, household.value.people.map(a => a.age)).byAge
        for (const k in updatedEstimate) setRates(k, updatedEstimate[k], 'single')
        setRates(ages[0], blendedPremium, key)
        setFlatPremium(blendedPremium, key)

      }
    }

    let premium;
    // console.log('structure new premium', cForm.value.premium, flatPremium.value);
    const newPremium = {
      ...cForm.value.premium,
      flatPremium: { ...cForm.value.premium?.flatPremium, ...flatPremium.value },
      fixedRates: { ...cForm.value.premium?.fixedRates, ...fixedRates.value },
      rateByAge: { ...cForm.value.premium?.rateByAge, ...rateByAge.value }
    }
    // console.log('new premium', newPremium)
    try {
      premium = getCoverageRate({
        coverage: { ...cForm.value, premium: newPremium },
        enrolled: household.value.people,
        def_key: key
      })
    } catch (e) {
      console.error(`Could not calculate premium: ${e?.message ?? e}`)
    } finally {

      if (!premium || isNaN(premium) || premium !== shopForm.value.lastPremium) {
        if (canEdit.value) {
          // console.log('set as new premium')
          cForm.value.premium = newPremium
          cPatch.value.$set = { ...cPatch.value.$set, [`coverages.${coverageId.value}.premium`]: newPremium }

          emit('update:coverage', cForm.value)
        } else if (blendedPremium !== premium) $infoNotify('Your premium entry doesn\'t match the rates entered on this coverage option. Since someone else already provided that data, we will not change it.')
      }
      if (blendedPremium && Math.round(blendedPremium) !== shopForm.value.lastPremium) {
        if (!gpsForm.value._id) await gpsSave();
        shopForm.value.lastPremium = Math.round(blendedPremium);
        if (!shopForm.value._id) await saveShop();
        else autoSaveShop('lastPremium')
      }

    }
  }

    const autoSaveC = (path, val) => {
      if (!coverageId.value) coverageId.value = cForm.value.id
      clearTimeout(cTo.value);

      gpsForm.value.coverages = { ...gpsForm.value.coverages, [coverageId.value]: cForm.value }
      if (gpsForm.value._id) gpStore.patchInStore(gpsForm.value._id, { coverages: gpsForm.value.coverages })
      const v = val || val === 0 || val === false ? val : _get(cForm.value, [path]);
      emit('update:coverage', gpsForm.value.coverages)
      cPatch.value.$set = { ...cPatch.value.$set, [`coverages.${coverageId.value}.${path}`]: v }
      cTo.value = setTimeout(async () => {
        if (path === 'premium') {
          setPremium(coverageId.value)
          cPatch.value.$set = { ...cPatch.value.$set, [`coverages.${coverageId.value}.premium`]: cForm.value.premium }

        }
        const patchObj = { ...cPatch.value }
        cPatch.value = {}
        if(gpsForm.value._id) await gpStore.patch(gpsForm.value._id, patchObj)
            .catch(err => {
              console.error(`Error saving coverage for gps ${err.message}`)
              cPatch.value = patchObj;
            })
        else gpsSave()
      }, 3000)

    }

    const setCoverageId = (id) => {
      if (!id || id !== coverageId.value) {
        if (id) {
          shopForm.value.majorMedical = id;
          autoSaveShop('majorMedical')
          coverageId.value = id;
          cForm.value = cFn(gpsForm.value.coverages[id])
          emit('update:coverage', cForm.value)
        } else {
          cForm.value = cFn()
          coverageId.value = undefined
        }
      }
    }

    const gpsId = computed(() => gpsForm.value._id);
    watch(gpsId, (nv, ov) => {
      if (nv && nv !== ov) {
        setTimeout(() => {
          const id = shopForm.value.majorMedical
          setCoverageId(id)
        }, 1000)
      }
    }, { immediate: true })


    const setRateType = (val) => {
      cForm.value.premium = { ...cForm.value.premium, rateType: val }
      autoSaveC('premium')
    }

    const setDed = (path, v) => {
      cForm.value.deductible = { ...cForm.value.deductible, [path]: v }
      autoSaveC('deductible')
    }
    const setMoop = (path, v) => {
      cForm.value.moop = { ...cForm.value.moop, [path]: v }
      autoSaveC('moop')
    }
    const setNetworkType = (t) => {
      cForm.value.plan_type = t
      autoSaveC('plan_type')
      if(t === 'open') {
        cForm.value.openNetwork = true;
        autoSaveC('openNetwork')
      }
    }


    const carrierBlur = ref(true);
    const market = ref({});
    const searchMarket = async () => {
      if (carrierBlur.value || market.value.data?.length || !shopForm.value.household?.place?.countyfips) return
      await marketStore.find({
        runJoin: { quick_quote: {} },
        query: {
          household: { people: [{ age: 30, gender: 'Male' }], income: 500000 },
          place: shopForm.value.household?.place
        }
      })
          .then(res => market.value = res)
          .catch(err => {
            console.error(`Error finding slcsp: ${err.message}`);
            return { data: [], silver: { data: [] } }
          })
    }
    const carrierSearch = ref('')
    const setCarrierBlur = (v) => {
      carrierSearch.value = ''
      if (v !== carrierBlur.value) {
        carrierBlur.value = v;
        if (!v) searchMarket();
      }
    }

    const carriers = computed(() => (market.value.facet_groups?.find(a => a.name === 'issuers')?.facets || []).map(a => a.value).filter(a => a.toLowerCase().includes((carrierSearch.value || '').toLowerCase())))

    const setCarrier = (c) => {
      cForm.value.carrierName = c
      autoSaveC('carrierName')
    }

    const zipData = ref({})
    const setZipData = (val, passive) => {
      zipData.value = val;
      console.log('set zip data', val, passive, shopForm.value.household?.place);
      if (val.fips && (!passive || !shopForm.value.household?.place?.countyfips)) {
        const place = { zipcode: val.zip, state: val.state, countyfips: val.fips }
        shopForm.value.household = { ...shopForm.value.household, place };
        autoSaveShop('household');
      }
    }

    const changeHousehold = () => {
      nextTick(() => {
        const hh = householdToShopHousehold(household.value, setStat)
        setStat('people', hh.people)
        setStat('place', hh.place)
        emit('household', household.value)
      })
    }
    const addPlace = (val) => {
      if (val.countyfips) {
        if (!shopForm.value.stats?.place?.countyfips) {
          setStat('place', val)
          shopForm.value.stats.place = val;
          emit('household', shopHouseholdToHousehold(shopForm.value.stats))
          envStore.setPlace(val)

        }
      }
    }




</script>

<style lang="scss" scoped>



  .__title {
    padding: 0 5px 7px 5px;
    font-size: 1rem;
    font-weight: 500;
    color: var(--ir-mid);
  }
</style>
