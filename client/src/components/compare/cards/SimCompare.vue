<template>
  <div class="_fw bg-ir-bg1">
    <div class="row justify-center">
      <div class="_cent pd8 pw2">


        <div class="row">
          <div class="w1000 mw100 q-pa-md">
            <org-chip size="lg" :model-value="gps.org" v-if="gps.org"></org-chip>
            <div class="font-1-3-4r tw-five text-primary alt-font">See if your employer plan stacks up</div>
            <div class="font-1-1-8r text-ir-deep">Your employer thinks you're demanding this plan of
              them (maybe you are - but look closer), they think government is demanding it of them, you may think the
              government is demanding it of you. The ACA does not mean "offer one insurance plan to your employees -
              which if they don't choose they forfeit that part of their pay."
            </div>
            <div class="font-1-1-8r q-pt-md tw-five text-primary">
              Light is the best disinfectant - enter your plan details and find out how well your money is being spent.
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-6 q-py-md pw1 __left">
            <div></div>
            <div class="__az">
              <gps-form
                  @ale="toggleAle"
                  @update:shop="setShop = $event"
                  @update:coverage="setCoverage"
                  :shop="shop"
                  @household="checkShopHousehold"
              ></gps-form>
            </div>

            <div class="__az">
              <div class="_frm">
                <div class="_lbl">
                  <div>Household Income</div>
                  <span>For calculating tax incentives</span>
                </div>
                <div class="_bod">
                  <money-input class="w300 mw100" prefix="$" :decimal="0" input-class="font-1r tw-six alt-font"
                               :model-value="stats.income"
                               @update:modelValue="setStat('income', $event, resetMarket)"></money-input>
                </div>
              </div>

            </div>

            <div class="__az" v-if="!useAle">


              <q-slide-transition>
                <div class="_fw">
                  <ptc-display
                      sm="xs"
                      lg="md"
                      xs="xxs"
                      :loading="loading"
                      :mult="12"
                      :ptc="(shop?.aptc || 0) * 12"
                      :model-value="shop.useAptc"
                      @update:model-value="toggleAptc"
                  ></ptc-display>
                </div>
              </q-slide-transition>


            </div>

            <div class="__az" v-if="coverage.files?.length">

              <div class="__title">
                Documents
              </div>
              <div class="__files">
                <div v-for="(file, i) in Object.keys(fileById.byUpload).map(a => fileById.byUpload[a])" :key="`file-${i}`">
                  <div class="_fw _oh _hov cursor-pointer" @click="openFile(file)">
                    <file-type-handler :file="file"></file-type-handler>
                    <div class="row justify-center q-pt-xs">
                      <div class="tw-five font-3-4r __filename">
                        {{ file.info?.name || file.originalname }}
                        <q-tooltip>{{ file.info.name || file.originalname }}</q-tooltip>

                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>

          <div class="col-12 col-md-6 q-py-md pw1">
            <div class="__az">
              <div class="font-1r tw-five alt-font text-ir-deep">
                <div>We use your plan details to run simulations against millions of actusl medical bills.</div>
                <div class="q-pt-sm">This gives you a super-intelligent look at how each plan performs in real scenarios
                  vs
                  every other plan available in your zip code.
                </div>
                <div class="text-primary tw-six q-pt-sm">That will tell you how your plan stacks up.</div>

              </div>
            </div>

            <div class="__az" v-if="coverage?.id">
              <div class="__title q-pa-sm">
                <div>Plan Premiums</div>
                <div class="font-3-4r">(Based on the data we have here)</div>
                <div class="font-3-4r flex items-center tw-five text-accent">
                  <div>Enter full detail</div>
                  <q-btn dense flat icon="mdi-pencil-box" @click="premiumDialog = true"></q-btn>

                  <common-dialog v-model="premiumDialog" setting="right">
                    <div class="_fw bg-white q-pa-md">
                      <premium-form :model-value="coverage.premium" @update:modelValue="setPremium"></premium-form>
                    </div>
                  </common-dialog>
                </div>
                <div class="q-py-sm tw-five text-ir-deep">{{ coverage.name || '' }}</div>
              </div>
              <rate-table
                  :model-value="coverage"
                  :age="def_age"
              ></rate-table>
            </div>

            <div class="__az" v-if="coverage?.id">
              <div class="q-pa-sm __title">
                <span class="font-1r tw-five text-ir-mid">Find matching plans</span>
                <div class="_fw">
                  <span v-if="!similar.done" class="font-7-8r">Use AI to filter for the plans that most closely match your current plan</span>
                  <span v-else-if="!comparing" class="font-7-8r">
                    Results don't seem accurate? <span class="text-accent tw-five _hov cursor-pointer"
                                                       @click="clearSimilar">Run it again</span>
                  </span>
                </div>
              </div>

              <q-chip v-if="!similar.done || comparing" clickable :color="comparing ? 'p3' : 'p1'" @click="runCompare"
                      :disable="comparing" no-caps>
                <span class="tw-five q-mr-md font-1r text-p7">Search Now</span>
                <ai-logo dark opaque size="20px"></ai-logo>
              </q-chip>

              <similar-list v-model:selected="selected" v-else :model-value="similar" :stats="stats"
                            :gps="gps"></similar-list>

            </div>


            <div class="__az">
              <type-manager
                  :model-value="activeTypes"
                  @toggle="toggleType"
              ></type-manager>
            </div>

            <div class="__az">
              <risk-profile
                  :model-value="stats.risk"
                  @update:modelValue="setStat('risk', $event, resetMarket)"
                  :people="household.people"
                  :age="stats.age || envStore.age || (household.people || [])[0]?.age"

              ></risk-profile>

            </div>


          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center __result">
      <div class="_cent">
        <div class="row justify-center">
          <div class="__rc">
            <div class="row items-end q-pa-sm">
              <div class="col-12">
                <div class="text-center tw-five font-2r">Your Plan Results</div>
                <div class="text-center q-pb-md font-1-1-4r" v-if="covRank">@
                  {{ dollarString(covRank._fastjoin?.spend || 0, '$', 0) }} /yr in medical bills on average
                </div>
                <div class="row justify-center">
                  <div class="__az __mine w600 mw100">
                    <div class="_fw">
                      <template v-if="covRank.rank">
                        <div class="t-r">
                          <div class="font-7-8r tw-five text-ir-deep alt-font q-px-sm">{{
                              ordinalSuffixOf(covRank.rank)
                            }}
                            of
                            {{ allLength }}
                          </div>
                        </div>
                        <spend-card
                            :scores="(shop.coverage_scores || {})[covRank.id]"
                            :tax_rate=".06"
                            :mult="12"
                            :model-value="covRank"
                            :def_age="def_age"
                            :def_key="def_key"
                        ></spend-card>
                      </template>

                      <div v-else class="_fw h200 flex flex-center">
                        <div>
                          <div class="q-pa-lg text-center font-1-1-4r tw-five text-ir-deep _l1-3">
                            <div>Add your plan details to compare
                              outcomes
                            </div>
                            <q-separator class="q-my-md" color="primary"></q-separator>
                            <div class="font-1r">We'll compare your plan to all others in your zip code to give you a
                              deep
                              look
                              at what you're getting
                            </div>
                          </div>
                          <div v-if="!loading" class="row justify-center">
                            <ai-logo opaque></ai-logo>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent pd4 pw2">

        <div class="q-py-md pw1 _fw">

          <div class="__shop">

            <div class="__load flex flex-center" v-if="loading">
              <ai-logo opaque></ai-logo>
            </div>

            <div class="q-pa-sm tw-five text-ir-deep">
              <div class="font-1-1-2r">
                See the ranking details
              </div>
              <div class="font-1r">Your plan ranked <span
                  class="tw-six text-accent">{{ ordinalSuffixOf(covRank.rank || 0) }} of {{ allLength }}</span> in
                {{ stats.place?.zipcode || 'your zip' }} based on total premium + out of pocket costs.
              </div>
              <span class="font-1r text-italic">Having a problem? <q-chip @click="resetMarket('', false, true)" color="transparent" clickable class="text-accent" label="Reset"></q-chip></span>
            </div>

            <ideal-structure
                :shop="shop"
                @update:selected="selected = $event"
                :compare="gps.coverages || {}"
                :tax_rate=".06"
                :def_key="def_key"
                :all="byId.all"
            ></ideal-structure>

            <div class="row justify-center q-pt-md">
              <q-btn @click="shareDialog = true" push color="accent" no-caps class="tw-six font-1-1-4r">Share These
                Results
              </q-btn>
              <common-dialog v-model="shareDialog" setting="smmd">
                <div class="_fw q-pa-md bg-white">
                  <share-card save-btn :shop="shop" :gps="gps"></share-card>
                </div>
              </common-dialog>
            </div>
          </div>

          <div class="col-12 pw1 q-py-xl">
            <div class="_fw q-pt-lg">
              <div class="text-center tw-five font-1-1-4r">Here's a 50 year sample of the bills we used to simulate your
                outcomes
              </div>
              <div class="text-center q-pb-md font-1r">To be realistic, the bills come from separate medical events for
                each
                member of your household
              </div>
              <div class="_fw __grid">
                <bill-chart :shop="shop"></bill-chart>
              </div>
            </div>
          </div>

        </div>
        <turnstile-popup
            v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile" v-model="notabot"
            @update:model-value="resetMarket()"
        ></turnstile-popup>
        <count-down @close="countdown = 0" :countdown="countdown" @reset="resetMarket('', true)"></count-down>
      </div>
    </div>

    <popup-sleeve :model-value="!!selected" @update:model-value="togglePopup">
      <div class="__popup_wrap bg-ir-bg text-ir-text _fh">
        <coverage-viewer
            :shop="shop"
            :dummy-ids="covRank.id"
            :age="stats.age"
            :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
            :by-id="byId"
            :model-value="selected"
        ></coverage-viewer>
      </div>
    </popup-sleeve>


  </div>
</template>

<script setup>
  import GpsForm from 'components/compare/forms/GpsForm.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import SpendCard from 'components/market/shop/cards/SpendCard.vue';
  import RiskProfile from 'components/market/shop/utils/RiskProfile.vue';
  import CountDown from 'components/market/shop/utils/CountDown.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import PtcDisplay from 'components/market/shop/cards/PtcDisplay.vue';
  import RateTable from 'components/coverages/cards/RateTable.vue';
  import TypeManager from 'components/market/shop/utils/TypeManager.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PremiumForm from 'components/coverages/forms/PremiumForm.vue';
  import BillChart from 'components/market/shop/cards/BillChart.vue';
  import IdealStructure from 'components/market/shop/cards/IdealStructure.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import ShareCard from 'components/compare/cards/ShareCard.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import CoverageViewer from 'components/market/shop/cards/CoverageViewer.vue';
  import PopupSleeve from 'components/market/utils/PopupSleeve.vue';
  import SimilarList from 'components/compare/cards/SimilarList.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, nextTick, reactive, ref} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {ordinalSuffixOf} from 'components/market/utils';
  import {getCoverageRate, getFixedRateKey} from 'components/coverages/utils/display';
  import {idGet} from 'src/utils/id-get';
  import {useGps} from 'stores/gps';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import {useRouter} from 'vue-router';

  import {compareShop} from 'components/compare/utils/compare-shop';
  import {dollarString} from 'symbol-syntax-utils';
  import {$infoNotify} from 'src/utils/global-methods';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';

  const router = useRouter();

  const envStore = useEnvStore()
  const gpsStore = useGps()

  const props = defineProps({
    coverages: Array,
    shopIn: { required: false }
  })

  const { item: gps } = idGet({
    store: gpsStore,
    routeParamsPath: 'gpsId'
  ,
    useAtcStore
  })

  const shareDialog = ref(false);

  const selected = ref();
  const togglePopup = (val) => {
    if (!val) {
      selected.value = undefined;
    }
  }

  const premiumDialog = ref(false);
  const coverageId = ref()
  const coverage = computed(() => {
    const id = coverageId.value || Object.keys(gps.value.coverages || {})[0];
    if (!id) return {}
    return gps.value.coverages[id]
  })

  const { byId:fileById } = manageFindUploads({
    sources: reactive({ data: coverage.value.files || [] }),
    paths: ['']
  })

  const shopEmit = (evt, ...args) => {
    const evts = {}
    if (evts[evt]) evts[evt](...args)
  }

  const shopId = ref()
  const {
    notabot,
    showTurnstile,
    isAuthenticated,
    household,
    activeTypes,
    toggleType,
    allLength,
    covRank,
    byId,
    useAle,
    toggleAptc,
    toggleAle,
    shop,
    stats,
    setStat,
    def_key,
    def_age,
    loading,
    resetMarket,
    countdown,
    householdToShopHousehold,
      similar
  } = compareShop({ gps, coverage, emit: shopEmit, shopId })

  const setShop = (v) => {
    shopId.value = v._id
  }
  const checkShopHousehold = () => {
    nextTick(() => {
      householdToShopHousehold(household.value, setStat);
    })
  }

  const lastPremium = ref();

  const checkPremium = (c) => {
    const key = getFixedRateKey({ enrolled: household.value.people }).key
    return getCoverageRate({ coverage: c, enrolled: household.value.people, def_key: key })

  }
  const setCoverage = (val) => {
    coverageId.value = val.id;
    const premium = checkPremium(val);
    if (premium && premium !== lastPremium.value) resetMarket('', true, false)
    lastPremium.value = premium;
  }

  const premiumTo = ref()
  const setPremium = (val) => {
    clearTimeout(premiumTo.value);
    premiumTo.value = setTimeout(() => {
      const p = checkPremium(coverage.value)
      if (p && p !== lastPremium.value) {
        resetMarket('', false, true)
        gpsStore.patch(gps.value._id, { $set: { [`coverages.${coverage.value.id}.premium`]: val } })
      }
      lastPremium.value = p;
    }, 1000)
  }

  const openFile = (val) => {
    const { href } = router.resolve({ name: 'file-preview', params: { id: val.uploadId || val._id } });
    window.open(href, '_blank')
  }


  const comparing = ref(false);
  const runCompare = async () => {
    if (!shop.value?.stats?.place) return $infoNotify('Add a location to compare')
    const ids = Object.keys(gps.value.coverages || {}).map(a => gps.value.coverages[a].id)
    if (ids.length) {
      const HH = {
        place: stats.value.place,
        people: [{ age: stats.value.age, gender: stats.value.gender }, ...stats.value.people || []],
        income: stats.value.income
      };
      comparing.value = true;
      const res = await gpsStore.get(gps.value._id, {
        runJoin: {
          employer_plan: {
            compare_ids: ids,
            query: { $limit: 25, household: HH, place: HH.place }
          }
        }
      })
          .catch(err => {
            console.error(`Error running compare: ${err.message}`)
          })
      comparing.value = false;

      const { plan_compare } = res._fastjoin;
      const patchObj = {};
      for (const k in plan_compare) {
        patchObj[`coverages.${k}.similar`] = plan_compare[k]
      }
      resetMarket('', false, true)
      await gpsStore.patch(gps.value._id, { $set: patchObj })
      console.log('got res', res);
    } else $infoNotify('Add at least one plan to compare')
  }

  const clearSimilar = async () => {
    const cov = { ...gps.value.coverages }
    for (const k in cov) {
      for (const sub in cov[k].similar || {}) {
        delete cov[k].similar[sub]
      }
    }
    comparing.value = true;
    gpsStore.patchInStore(gps.value._id, { coverages: cov })
    await gpsStore.patch(gps.value._id, { coverages: cov })
    runCompare()
  }



</script>

<style lang="scss" scoped>
  .__left {
    position: relative;

    > div {
      &:first-child {
        position: absolute;
        top: 0;
        right: 0;
        height: 90%;
        width: 60px;
        border-radius: 0 20px 20px 0;
        background: repeating-linear-gradient(
                -45deg,
                var(--q-p4) 0px,
                var(--q-p4) 2px,
                transparent 2px,
                transparent 9px
        )
      }
    }
  }

  .__result {
    padding: max(70px, 4vh) min(2vw, 50px) max(100px, 6vh) min(2vw, 50px);
    background: linear-gradient(115deg, var(--ir-bg), var(--q-p0), var(--q-a1), var(--ir-bg));

    .__rc {
      width: 100%;
      //background: rgba(255, 255, 255, .7);
    }
  }

  .__az {
    width: 100%;
    max-width: 600px;
    padding: 25px 15px;
    border-radius: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 8px var(--ir-light);
    position: relative;
    background: white;
  }

  .__mine {
    width: 575px;
    max-width: 100%;
    margin-top: 20px;
    border: solid 3px var(--q-accent) !important;
    padding: 35px max(1.5vw, 14px) !important;
    box-shadow: 0 0 40px var(--q-a1);
  }

  .__shop {
    width: 100%;
    padding: 30px 10px;
    position: relative;

    .__c {
      position: relative;
      border-radius: 15px 15px 0 0;
      padding: 25px max(2vw, 14px);
      background: var(--ir-bg);
      box-shadow: 0 2px 12px -2px var(--ir-light);
    }

    .__load {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      background: rgba(255, 255, 255, .5);
      backdrop-filter: blur(5px);
      z-index: 4;
    }
  }

  .__f {
    border-radius: 8px;
    //box-shadow: -2px -2px 12px var(--ir-light);
    transition: all .2s;
    background: var(--ir-bg);
    position: relative;
    display: grid;
    grid-template-columns: min(30%, 150px) 1fr;
    align-items: center;

    .__bod {
      padding: 6px;
      border-radius: 0 8px 8px 0;
    }

    .__l {
      font-size: .8rem;
      font-weight: 600;
      color: var(--ir-mid);
      font-family: var(--alt-font);
      border-radius: 8px 0 0 8px;
      padding: 0 6px;
      border-right: solid 2px var(--ir-light);
      height: 100%;
      align-content: center;


      > span {
        font-weight: 500;
        font-size: .75rem;
      }
    }

  }

  @media screen and (max-width: 600px) {
    .__f {
      grid-template-columns: 100%;
    }
  }

  .__files {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, 80px);
    grid-template-rows: repeat(auto-fit, 80px);

    > div {
      width: 100%;
      height: 100%;
      display: grid;
      align-items: center;
      justify-content: center;
      border-radius: 7px;
      border: solid 1px var(--ir-light);
      background: white;
      padding: 5px;
    }

    .__filename {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .__grid {
    padding: 15px 0;
    background: repeating-linear-gradient(
            -90deg,
            white 0px,
            white 5px,
            transparent 5px,
            transparent 9px
    ), repeating-linear-gradient(
            180deg,
            white 0px,
            white 5px,
            transparent 5px,
            transparent 9px
    )
  }

  .__title {
    padding: 0 5px 7px 5px;
    font-size: 1rem;
    font-weight: 500;
    color: var(--ir-mid);
  }
</style>
