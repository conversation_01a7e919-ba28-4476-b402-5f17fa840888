<template>
  <q-page class="bg-a0">
    <easy-add-plan
        :smb="gps.employees?.length <= 50"
        :model-value="gps"
        :store="gpsStore"
        local-path="gps"
        service-path="gps"
    ></easy-add-plan>
  </q-page>
</template>

<script setup>

  import EasyAddPlan from 'components/plans/forms/easy-add/EasyAddPlan.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useGps} from 'stores/gps';
  import {useRoute} from 'vue-router';


  const gpsStore = useGps();

  const route = useRoute()

  const drId = ref()
  const { item:gps } = idGet({
    store: gpsStore,
    value: computed(() => route.params.gpsId || LocalStorage.getItem('gps'))
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

  .__c, .__gs {
    margin: 20px 0;
    padding: max(2vw, 30px) max(2vw, 8px);
    border-radius: 20px;
    background: var(--ir-bg);
    box-shadow: 0 4px 6px var(--ir-light);
    width: 100%;
  }

  .__c {
    margin: 10px 0;
  }

  .__logo {
    height: 60px;
    width: 60px;
    overflow: hidden;
    position: relative;

    .__lform {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      opacity: 0;
    }
  }

  .__gs {
    display: grid;
    grid-template-columns: 200px 1fr;

    .__left {
      padding: 20px 15px;
      border-right: solid .3px var(--ir-light);
      text-align: right;
      font-weight: 600;
      color: var(--ir-mid);
      font-size: 1rem;
    }

    .__right {
      padding: 20px 15px;
    }
  }

  .__in {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto auto;
    width: 100%;
    max-width: 500px;

    > div {
      &:first-child {
        font-size: .8rem;
        font-weight: 400;
        padding: 0 10px 3px 10px;
        color: var(--ir-mid);
      }

      &:nth-child(2) {
        padding-bottom: 10px;
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__gs {
      display: grid;
      grid-template-columns: 100%;
      grid-template-rows: auto auto;

      .__left {
        padding: 10px 8px;
        border-right: none;
        border-bottom: solid .3px var(--ir-light);
        text-align: left;
        font-weight: 600;
        color: var(--ir-mid);
        font-size: 1rem;
      }

      .__right {
        padding: 10px 8px;
      }
    }
  }

  .__next {
    width: 100%;
    padding: 20px 15px;
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(135deg, var(--q-p0), white, var(--q-a0));
    //box-shadow: 0 4px 6px var(--ir-light);
  }
</style>
