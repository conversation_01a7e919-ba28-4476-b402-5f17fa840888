<template>
  <q-page class="bg-ir-bg1">
    <div class="row justify-center __top">
      <div class="_cent pd15 pw2">
        <div class="row">
          <div class="col-12 col-md-10">
            <org-chip v-if="org._id" :model-value="org" :size="$q.screen.gt.sm ? 'lg' : 'md'"></org-chip>
            <q-chip v-else-if="gps.companyName">
              <q-avatar v-if="gps.companyAvatar">
                <img :src="gps.companyAvatar">
              </q-avatar>
              <span>{{ gps.companyName }}</span>
            </q-chip>
            <div class="text-xxl tw-five alt-font">Health Plan Comparison</div>
            <div class="_fw">
              <div class="text-sm _fw tw-five q-py-sm">
                <span v-if="!gps.org && !gps.companyName">That's your paycheck being spent - whether you pay or your employer pays - a dollar spent is a dollar you don't take home.
                That's why more employers are looking at employee-directed health plans: to maximize transparency
                and choice - and boost take-home pay.
                  </span>
                <span v-else>
                  Help {{org.dba || org.name || gps.companyName}} make your health plan more effective - <i>for you.</i>
                </span>
              </div>
            </div>
            <div class="_fw row items-center q-pt-sm font-1r tw-six text-a3" v-if="sentBy._id">
              <div>Shared by:</div>
              <default-chip class="tw-five" :hide-avatar="!sentBy.avatar" :model-value="sentBy" :use-atc-store="useAtcStore"></default-chip>
            </div>
          </div>
        </div>

      </div>
    </div>


    <compare-participant
        :gpsId="gps"
        :shop-id="shop"
    ></compare-participant>


    <div class="row justify-center">
      <div class="_cent pd10 pw2">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-py-md pw1">
            <div class="__wi">
              <div class="text-lg tw-five alt-font text-primary">What is employee-directed?</div>
              <div class="_fw text-xs q-py-sm alt-font">
                <div>Who's money is being spent? The answer is simple: <b>yours</b>. Always. Even if 100%
                  of the premium is employer-paid.
                </div>

                <div class="q-pt-md">Employee-directed makes just two simple changes - that change everything:
                  <ol>
                    <li>Free the money up - no use-it-or-lose-it</li>
                    <li>Provide open-architecture with rich options</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-py-md pw2">
            <div class="__ps">
              <div></div>
              <div></div>
              <div class="z2 relative-position _fw">
                <plan-structure></plan-structure>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-center">
      <div class="_sent __why">
        <div class="row justify-center">
          <div class="w800 mw100">
            <div class="text-lg tw-five alt-font">Why wouldn't this already be the norm?</div>
            <div class="q-pt-sm text-xs text-white">Employers have no natural reason to choose your healthcare for
              you. They think
              you are pressuring them to. You might be. The government is pressuring them to. It traps them behind a
              wall of complexity.
              <div class="q-pt-md">CommonCare blasts that wall down with automation, AI analysis, and open-source
                pricing
                data. Health plans where an insurance carrier doesn't pool costs and control care. You do - or your
                doctor does.
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <new-way :employer="org" :employee="mergePerson"></new-way>

    <div class="row justify-center pd10 bg-a0">
      <div class="_cent">
        <div class="row __trans">
          <div class="col-12 col-md-6 q-pb-md q-pt-xl pw3">

            <div class="text-sm tw-five">A great employer values transparency 👑
            </div>
            <div class="text-xl tw-five q-px-xs">Wages and benefits -<br> both are <span
                class="text-primary">your</span> money.
              <q-icon v-if="$q.screen.gt.sm" name="mdi-chevron-right" size="45px" color="primary"></q-icon>
              <q-icon v-else name="mdi-chevron-down" size="45px" color="primary"></q-icon>
            </div>

            <div class="text-xs q-py-md">For any job, there is a <span class="text-primary tw-six">total</span> budget.
              Payroll tax, insurance, benefits all cut into that <span class="text-primary tw-six">total</span> -
              meaning they reduce your take-home pay dollar for dollar. Adjust the numbers here and see for yourself.
            </div>
            <div class="text-xs">That's why the best employers offer a clear allowance and let you choose where it goes
              - premiums, expenses, savings - or just take it home.
            </div>
          </div>

          <div class="col-12 col-md-6 q-py-md pw2">
            <de-mystify></de-mystify>
          </div>
        </div>
      </div>
    </div>


  </q-page>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import PlanStructure from 'components/plans/preview/cards/PlanStructure.vue';
  import NewWay from 'components/plans/preview/sections/NewWay.vue';
  import DeMystify from 'components/plans/preview/cards/DeMystify.vue';
  import CompareParticipant from 'components/compare/pages/CompareParticipant.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useGps} from 'stores/gps';
  import {usePpls} from 'stores/ppls';
  import {useOrgs} from 'stores/orgs';
  import {loginPerson} from 'stores/utils/login';
  import {useShops} from 'stores/shops';

  const gpsStore = useGps();
  const pplStore = usePpls();
  const shopStore = useShops();
  const orgStore = useOrgs();

  const { person } = loginPerson()

  const { item: gps } = idGet({
    store: gpsStore,
    routeParamsPath: 'gpsId'
  ,
    useAtcStore
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => gps.value.org)
  ,
    useAtcStore
  })
  const { item:fromShop } = idGet({
    store: shopStore,
    routeQueryPath: 'sentBy'
  ,
    useAtcStore
  })
  const { item: sbPerson } = idGet({
    store: pplStore,
    value: computed(() => fromShop.value?.person)
  ,
    useAtcStore
  })

  const { item: shop } = idGet({
    store: shopStore,
    routeParamsPath: 'shopId'
  ,
    useAtcStore
  })

  const sentBy = computed(() => {
    if (!fromShop.value._id || fromShop.value.anon) return {}
    return {
      ...sbPerson.value,
      ...fromShop.value
    }
  })

  const mergePerson = computed(() => {
    return {
      ...shop.value,
      ...person.value
    }
  })
</script>

<style lang="scss" scoped>
  .__top {
    background: linear-gradient(110deg, var(--q-a12) 20%, var(--q-a6));
    color: var(--q-a1);
  }

  .__why {
    background: radial-gradient(at 40% 40%, var(--q-a10), var(--q-a12));
    padding: max(80px, 12vh) max(20px, 2vw);
    color: var(--q-a2);
    border-radius: 2vw;
    border: solid 4px var(--q-p2)
  }

  .__ps {

    position: relative;
    border-radius: 15px;
    overflow: hidden;
    padding: 40px min(2vw, 25px);

    > div {
      &:first-child, &:nth-child(2) {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        z-index: 0;
      }

      &:first-child {
        z-index: 1;
        background: repeating-linear-gradient(
                -90deg,
                white 0px,
                white 2px,
                transparent 2px,
                transparent 29px
        ), repeating-linear-gradient(
                180deg,
                white 0px,
                white 2px,
                transparent 2px,
                transparent 29px
        );
      }

      &:nth-child(2) {
        z-index: 0;
        background: radial-gradient(at 30% 40%, var(--q-p2), transparent 50%);
      }
    }
  }


</style>
