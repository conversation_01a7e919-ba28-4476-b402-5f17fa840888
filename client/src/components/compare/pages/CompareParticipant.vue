<template>
  <q-tab-panels v-model="tab" animated class="_panel" transition-next="jump-up" transition-prev="jump-down">
    <q-tab-panel class="_panel" name="info">
      <div class="row justify-center bg-ir-bg1">
        <div class="_cent pd10">
          <div class="text-lg tw-five alt-font text-ir-deep pw2">See how your plan compares - and vote for improvements
          </div>
          <div class="text-sm text-ir-deep pw2">We take your household profile, your employer health plan details, and
            use
            AI to deeply analyze how
            well your plan performs against all available plans for your zip code.
          </div>
          <div class="row q-pt-md">
            <div class="col-12 col-md-6 q-py-md pw2 __left">
              <div></div>
              <div class="__az">
                <div class="__azt">Your Household</div>
                <age-zip-family
                    :shop="shop"
                    @change="setHousehold"
                    :place="shop.stats?.place"
                    @place="addPlace"
                    @zip-data="setZipData"
                ></age-zip-family>
              </div>

              <div class="__az">
                <div class="_frm">
                  <div class="_lbl">
                    <div>Household Income</div>
                    <span>For calculating tax incentives</span>
                  </div>
                  <div class="_bod">
                    <money-input
                        filled
                        dense
                        class="w300 mw100"
                        prefix="$"
                        :decimal="0"
                        input-class="font-1r tw-six alt-font"
                        :model-value="stats.income"
                        @update:modelValue="setIncome"
                    ></money-input>
                  </div>
                </div>
                <div class="_frm"
                     v-if="gps?.employerContribution?.type === 'percent' && gps.employerContribution.percentType === 'income'">
                  <div class="_lbl">{{ org.dba || org.name || 'This job' }} Income<br><span>Needed b/c benefits are a percent of income</span>
                  </div>
                  <div class="_bod">
                    <money-input filled dense class="w300 mw100" prefix="$" :decimal="0"
                                 input-class="font-1r tw-six text-ir-deep alt-font" v-model="stats.income"
                                 @update:modelValue="setStat('income')"></money-input>
                  </div>
                </div>
                <div class="_frm">
                  <div class="_lbl">Your Name</div>
                  <div class="_bod">
                    <q-input class="w300 mw100" filled dense v-model="shopForm.name" @blur="setName"></q-input>
                  </div>
                </div>
                <div class="_frm">
                  <div class="_lbl">Email</div>
                  <div class="_bod">
                    <email-field class="w300 mw100" hide-bottom-space filled dense v-model="shopForm.email"
                                 @update:modelValue="setEmail"></email-field>
                  </div>
                </div>
              </div>
              <div class="__az">
                <risk-profile
                    :model-value="stats.risk"
                    @update:modelValue="setStat('risk', $event)"
                    :people="household.people"
                    :age="stats.age"

                ></risk-profile>

              </div>

            </div>
            <div class="col-12 col-md-6 q-py-md pw2">

              <div class="__az">
                <div class="__azt">
                  <div>Your Coverage</div>
                  <div class="font-7-8r">
                    <span v-if="!coverageId">Tell us which coverage option are you enrolled in</span>
                  </div>
                </div>

                <q-tab-panels animated class="_panel" :model-value="!coverageId">
                  <q-tab-panel class="_panel" :name="true">
                    <gps-coverages @update:modelValue="setCoverageId($event.id)" :gps="gps"></gps-coverages>
                  </q-tab-panel>
                  <q-tab-panel class="_panel" :name="false">
                    <div class="row justify-end">
                      <q-chip color="ir-bg2" class="tw-five q-py-sm text-ir-deep" v-if="ec.amount">
                        {{ org.dba || org.name || 'Employer' }}
                        pays: <span class="tw-six text-primary">{{ ec.display }}</span>
                      </q-chip>
                    </div>
                    <div class="font-7-8r tw-six text-ir-deep">{{ coverage.carrierName || '' }}</div>
                    <div class="font-1r tw-five text-ir-deep q-pb-sm">{{ coverage.title || coverage.name || '' }}</div>
                    <rate-table :age="def_age" :model-value="coverage"></rate-table>
                  </q-tab-panel>
                </q-tab-panels>

              </div>
              <div class="__az">
                <div class="__azt">
                  <div>Result Types</div>
                  <div class="font-7-8r q-pb-md">
                    <span v-if="!coverageId">Filter which types of plans you want to compare against</span>
                  </div>
                </div>
                <type-manager
                    :model-value="activeTypes"
                    @toggle="setType"
                ></type-manager>
              </div>

            </div>
          </div>

          <div class="q-pt-xl _fw">
            <div class="q-py-lg"></div>
            <div class="row justify-center">
              <q-btn-group rounded push no-caps>
                <q-btn @click="tab='shop'" :disable="!coverageId" class="tw-six text-md" color="white">
                  <ai-logo size="35px"></ai-logo>
                </q-btn>
                <q-btn @click="tab='shop'" :disable="!coverageId" no-caps class="tw-six text-md" color="primary">
                  <span v-if="coverageId" class="q-ml-md">See the results</span>
                  <span v-else class="q-ml-md">Select current plan to see results</span>
                </q-btn>
              </q-btn-group>
            </div>
          </div>

        </div>
      </div>
    </q-tab-panel>
    <q-tab-panel class="_panel" name="shop">
      <div class="row justify-center __result">
        <div class="_cent">

          <div class="row">
            <div class="col-12 col-md-6 q-py-md pw2 __left">
              <div></div>
              <div class="__az">
                <div class="__azt">Your Household
                  <q-chip dense color="transparent" clickable @click="tab='info'">
                    <q-icon name="mdi-pencil-box" color="accent" size="18px"></q-icon>
                  </q-chip>
                </div>
                <div class="_frm">
                  <div class="_lbl">Location</div>
                  <div class="_bod">
                    <q-chip color="ir-grey-2">
                      <q-icon size="16px" color="red" name="mdi-map-marker"></q-icon>
                      <span class="q-ml-sm tw-six font-1r text-ir-deep alt-font">{{ stats.place?.zipcode }}</span>
                    </q-chip>
                  </div>
                </div>
                <div class="_frm">
                  <div class="_lbl">Members</div>
                  <div class="_bod">
                    <family-member-icon :model-value="{ age: stats.age, gender: stats.gender }">
                      <q-tooltip class="tw-six font-1r">{{ stats.gender || 'Unknown Gender' }} - age
                        {{ stats.age || 'Unknown Age' }}
                      </q-tooltip>
                    </family-member-icon>
                    <family-member-icon
                        v-for="(prsn, i) in stats.people || []"
                        :key="`prsn-${i}`"
                        :model-value="prsn">
                      <q-tooltip class="tw-six font-1r">{{ prsn.gender || 'Unknown Gender' }} - age
                        {{ prsn.age || 'Unknown Age' }}
                      </q-tooltip>
                    </family-member-icon>
                  </div>
                </div>
                <div class="_frm">
                  <div class="_lbl">Income</div>
                  <div class="_bod">
                    <div class="font-1-1-4r tw-six q-pa-sm alt-font text-ir-deep">{{
                        dollarString(stats.income, '$', 0)
                      }}
                    </div>
                  </div>
                </div>
                <div class="_frm">
                  <div class="_lbl">Risk Profile</div>
                  <div class="_bod">
                    <risk-profile
                        :model-value="stats.risk"
                        @update:modelValue="setStat('risk', $event)"
                        :people="household.people"
                        :age="stats.age || envStore.age || (household.people || [])[0]?.age"

                    ></risk-profile>
                  </div>
                </div>
              </div>
              <div class="__az" v-if="!useAle || ec.amount">
                <div class="__azt">
                  Your Allowances
                </div>
                <div class="_fw">
                  <template v-if="!useAle">
                    <ptc-display
                        sm="xs"
                        lg="sm"
                        xs="xxs"
                        :loading="loading"
                        :mult="12"
                        :ptc="(shop?.aptc || 0) * 12"
                        :model-value="shop.useAptc"
                        @update:model-value="toggleAptc"
                    ></ptc-display>
                  </template>
                  <div v-if="ec.amount" class="q-px-md">
                    <q-separator class="q-my-md"></q-separator>
                    <div class="text-xs tw-five flex items-center">
                      <q-chip color="a3" class="tw-five text-xs text-white">Employer Contribution:</q-chip>
                      <div class="text-accent tw-six text-sm q-ml-sm alt-font">{{ ec.display }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6 q-py-md pw2">
              <div class="__az">
                <div class="__azt">Results</div>
                <div class="font-1r q-px-sm alt-font">
                  We simulated <span class="tw-six text-accent">{{ dollarString(shop.spend || 0, '$', 0) }}</span> in
                  average annual spend using your household and risk profile to match against millions of actual U.S.
                  household medical bills.
                </div>
              </div>
              <div class="__az">
                <div class="__azt">
                  <div>How your plan compares</div>
                  <div class="text-ir-deep tw-five font-1r" v-if="covRank">@
                    {{ dollarString(shop?.spend || 0, '$', 0) }} /yr in medical bills on average
                  </div>
                  <div class="tw-six text-a4 alt-font font-1r">{{
                      ordinalSuffixOf(covRank.rank)
                    }} of
                    {{ allLength }}
                  </div>

                  <div v-if="similar.done" class="_fw _hov cursor-pointer" @click="similarDialog = true">
                    <span class="font-1r text-a3 tw-five">See the most similar options</span>

                    <common-dialog setting="smmd" v-model="similarDialog">
                      <div class="_fw bg-white q-pa-sm">
                        <similar-list :shop="shop" :gps="gps" :similar="similar" @update:selected="selected = $event"></similar-list>
                      </div>
                    </common-dialog>
                  </div>
                  <div v-else class="row justify-end">
                    <q-chip color="ir-bg2" clickable @click="runCompare" :disable="comparing">
                      <span class="q-mr-sm">Find Similar Plans</span>
                      <q-icon v-if="!comparing" color="primary" name="mdi-magnify"></q-icon>
                      <q-spinner v-else color="primary"></q-spinner>
                    </q-chip>
                  </div>
                </div>

                <q-separator class="q-mb-sm"></q-separator>

                <template v-if="covRank.rank">
                  <spend-card
                      :tax_rate="tax_rate"
                      :mult="12"
                      :scores="(shop.coverage_scores || {})[covRank.id || covRank._id]"
                      :model-value="covRank"
                      :def_age="def_age"
                      :def_key="def_key"
                  ></spend-card>
                </template>

                <div v-else>
                  <div class="q-pa-lg text-center text-sm tw-five text-ir-deep _l1-3">
                    <div>Running Simulation</div>
                    <q-separator class="q-my-md" color="primary"></q-separator>
                    <div class="font-1r">We'll compare your plan to all others in your zip code to give you a
                      deep
                      look
                      at what you're getting
                    </div>
                  </div>
                  <div v-if="!loading" class="row justify-center">
                    <ai-logo opaque></ai-logo>
                  </div>
                </div>
              </div>
              <div class="__az">
                <div class="__azt">Your Election</div>
                <template v-if="shopForm.coverage || shopForm.policy || shopForm.keepOld || shopForm.cashInLieu">

                  <div class="__opt">

                    <div class="text-left" v-if="shopForm.coverage || shopForm.policy">
                      <spend-card
                          :tax_rate="tax_rate"
                          :mult="12"
                          :model-value="byId.all[selectedPlan._id || shop.coverage || shop.policy]"
                          :scores="(shop.coverage_scores || {})[selectedPlan._id || shop.coverage || shop.policy]"
                          :def_age="def_age"
                          :def_key="def_key"
                      ></spend-card>
                    </div>
                    <div v-if="shopForm.keepOld">
                      <div class="pw2 pd2 text-sm tw-five text-primary">
                        <div>Keep your existing plan</div>
                      </div>
                    </div>

                    <div v-if="shopForm.cashInLieu">
                      <div>Cash out for <br><span
                          class="text-primary tw-six alt-font">{{ dollarString(ec.amount * .9, '$', 0) }}<span
                          class="font-1-1-8r tw-five">/mo</span></span>
                      </div>
                    </div>
                  </div>
                </template>
                <div v-else class="__opt text-center">
                  Select a plan or cash out
                </div>
              </div>
            </div>
          </div>

          <div class="_fw row justify-center __ai_chip" v-if="shop._id">
            <div :class="`AiChip ${chatOn ? '__on' : ''}`">
              <shop-ai-chip
                  :current_coverage="(gps.coverages || {})[shop.majorMedical]"
                  :compare_coverages="gps.coverages"
                  v-model="chatOn"
                  dark
                  :prompts="['Which option is the most similar to my current plan?', 'How does my plan compare to the other options?', 'How does the premium tax credit affect things?', 'When would I want to choose a health share?']"
                  color="a10"
                  class="tw-five text-xs alt-font text-white"
                  :shop="shop"
              ></shop-ai-chip>
            </div>
          </div>

          <div class="_fw pd4">


            <div class="q-py-md pw1 _fw">

              <div class="__shop">

                <div class="__load flex flex-center" v-if="loading">
                  <ai-logo opaque></ai-logo>
                </div>

                <div class="q-pa-sm tw-five text-ir-deep q-pt-lg">
                  <div class="row">
                    <div>
                      <div class="text-lg tw-five text-accent">
                        See the rankings
                      </div>
                      <q-separator class="q-my-sm"></q-separator>
                      <div class="text-sm">Your plan is ranked <span
                          class="tw-six text-accent">{{ ordinalSuffixOf(covRank.rank || 0) }} of {{ allLength }}</span>
                        in your
                        zip code.
                      </div>
                      <div class="text-xs">That's based on total premium + out of pocket costs on-average across all
                        simulated scenarios.
                      </div>
                      <div class="text-xs tw-five text-accent">
                        Select the plan you prefer below <span v-if="ec.amount"> - or <span
                          class="tw-six _hov cursor-pointer" @click="cashDialog = true">cash out</span></span>
                      </div>
                    </div>
                  </div>


                </div>

                <ideal-structure
                    :shop="shop"
                    @update:selected="selected = $event"
                    :tax_rate="tax_rate"
                    :def_key="def_key"
                    :all="byId.all"
                ></ideal-structure>

              </div>

              <div class="col-12 pw1 pd8">
                <div class="text-md tw-five">Insurance is for "what-if?"</div>
                <div class="text-xs q-pb-lg">We ranked each plan for how they performed with medical bills between each
                  of
                  <q-chip dark dense class="tw-six" color="accent" square>these</q-chip>
                  breakpoints. It shows you the best plan if things go well - or if they don't. <span
                      class="alt-font tw-six text-a3">Total spend</span>
                  (Premium + OOP)
                </div>

                <spend-distribution
                    :shop="shop"
                    @update:selected="selected = $event"
                    :lists="lists"
                    :all="byId.all"
                    :ranks="ranks"
                    :dist="dist"
                ></spend-distribution>
              </div>


              <div class="col-12 pw1 q-py-xl">
                <div class="_fw q-pt-lg">
                  <div class="text-center tw-five text-sm">Here's a 50 year sample of the bills we used to simulate your
                    outcomes
                  </div>
                  <div class="text-center q-pb-md font-1r">To be realistic, the bills come from separate medical events
                    for
                    each
                    member of your household
                  </div>
                  <div class="_fw __grid">
                    <bill-chart :shop="shop"></bill-chart>
                  </div>
                </div>
              </div>

            </div>
          </div>

          <div class="row justify-center relative-position _oh bg-white">
            <div class="__blob"></div>
            <div class="__blob1"></div>
            <div class="pd8 _sent relative-position z1">
              <div class="__wn">
                <div class="__l"></div>
                <div class="__r"></div>
                <div class="text-lg tw-five alt-font text-accent">What next?</div>
                <div class="text-sm">Participants at {{ org.dba || org.name || 'Your employer' }} are analyzing these
                  results. Provide your feedback to help drive your health plan toward employee-directed options - and
                  read on
                  about what that means.
                </div>

                <q-checkbox :model-value="!!shopForm.anon"
                            @update:model-value="shopForm.anon = $event; autoSaveShop('anon')"
                            label="Keep my comments anonymous"></q-checkbox>
                <div class="_fw q-py-md">
                  <q-editor
                      @update:modelValue="shopForm.comment = $event;autoSaveShop('comment')"
                      :modelValue="shopForm.comment || ''"
                      min-height="30px"
                      flat
                      placeholder="Let us know what you want for your health plan"
                      :content-style="{'border-radius': '10px'}"
                      content-class="bg-ir-grey-2 q-px-md"
                      :toolbar="[['bold', 'italic', 'strike', 'underline', 'code', 'link']]"
                  ></q-editor>
                  <div class="q-px-md">
                    <emoji-picker @update:model-value="pickEmoji"></emoji-picker>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <popup-sleeve :model-value="!!selected" @update:model-value="togglePopup">
            <div class="__popup_wrap bg-ir-bg text-ir-text _fh">
              <coverage-viewer
                  :shop="shop"
                  :current_coverage="(gps.coverages || {})[shop.majorMedical]"
                  :compare_coverages="gps.coverages"
                  :dummy-ids="covRank.id"
                  :open="selectPlan"
                  :age="stats.age"
                  :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
                  :by-id="byId"
                  :model-value="selected"
              ></coverage-viewer>
            </div>
          </popup-sleeve>

          <common-dialog v-model="cashDialog" setting="smmd">
            <div class="_fw q-pa-xl bg-white">
              <template v-if="gps.employerContribution?.percentType === 'income'">
                <div class="_frm">
                  <div class="_lbl">
                    <div>{{ org.dba || org.name || 'This job' }} income</div>
                    <span v-if="!stats.income">Needed b/c benefits are a percent of income</span>
                  </div>
                  <div class="_bod">
                    <money-input class="w300 mw100" prefix="$" :decimal="0"
                                 input-class="font-1r tw-six alt-font"
                                 :model-value="stats.income"
                                 @update:modelValue="setStat('income', $event)"></money-input>
                  </div>
                </div>
              </template>

              <div class="_fw text-xs alt-font">
                <div>{{ org.dba || org.name || 'Your employer' }} is willing to spend <b>{{ ec.display }}</b><span
                    v-if="gps.employerContribution?.type === 'percent'"> - which is <b>{{
                    dollarString(ec.amount, '$', 0)
                  }}/mo</b> </span>
                  on healthcare as part of your pay.
                </div>
                <div class="q-pt-sm">Here you can let them know you'd rather have the cash (if that's the case).</div>
                <div class="q-pt-sm">If you took it as regular pay, your employer pays ~10% in extra payroll taxes, so
                  you
                  get 90% - or <span class="tw-six text-primary">{{ dollarString(ec.amount * .9, '$', 0) }}/mo</span> -
                  as cash before your taxes.
                </div>
              </div>

              <div class="row q-pt-md justify-center">
                <q-chip clickable @click="toggleCash" color="ir-bg2">
                  <span class="tw-five text-xs q-mr-sm">I'd rather have the cash</span>
                  <q-icon size="25px" v-if="shopForm.cashInLieu" color="green"
                          name="mdi-checkbox-marked-outline"></q-icon>
                  <q-icon size="25px" v-else color="ir-mid" name="mdi-checkbox-blank-outline"></q-icon>
                </q-chip>
              </div>
            </div>
          </common-dialog>
          <common-dialog v-model="namePopup" setting="smmd">
            <div class="_fw q-pa-lg bg-white">
              <div class="row justify-center q-pb-md">
                <q-chip color="ir-bg2">
                  <span class="tw-five q-mr-sm">Feedback Saved</span>
                  <q-icon name="mdi-check-circle" color="green"></q-icon>
                </q-chip>
              </div>

              <div class="font-1r tw-five">Add your name & email to your feedback</div>
              <div class="font-7-8r">(Optional + we don't share it with anyone but your employer)</div>

              <div class="_frm">
                <div class="_lbl">Your Name</div>
                <div class="_bod">
                  <q-input class="w300 mw100" filled dense v-model="shopForm.name" @blur="setName"></q-input>
                </div>
              </div>
              <div class="_frm">
                <div class="_lbl">Email</div>
                <div class="_bod">
                  <email-field class="w300 mw100" hide-bottom-space filled dense v-model="shopForm.email"
                               @update:modelValue="setEmail"></email-field>
                </div>
              </div>
            </div>
          </common-dialog>
          <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile" v-model="notabot"
                           @update:model-value="resetMarket()"></turnstile-popup>
          <count-down @close="countdown = 0" :countdown="countdown" @reset="resetMarket('', false, true)"></count-down>

        </div>
      </div>

    </q-tab-panel>
  </q-tab-panels>
</template>

<script setup>
  // MUST HAVE coverageID
  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import GpsCoverages from 'components/compare/cards/GpsCoverages.vue';
  import RateTable from 'components/coverages/cards/RateTable.vue';
  import RiskProfile from 'components/market/shop/utils/RiskProfile.vue';
  import TypeManager from 'components/market/shop/utils/TypeManager.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import FamilyMemberIcon from 'components/households/utils/FamilyMemberIcon.vue';
  import SpendCard from 'components/market/shop/cards/SpendCard.vue';
  import EmojiPicker from 'components/common/input/EmojiPicker.vue';
  import PopupSleeve from 'components/market/utils/PopupSleeve.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CountDown from 'components/market/shop/utils/CountDown.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import BillChart from 'components/market/shop/cards/BillChart.vue';
  import IdealStructure from 'components/market/shop/cards/IdealStructure.vue';
  import CoverageViewer from 'components/market/shop/cards/CoverageViewer.vue';
  import SpendDistribution from 'components/market/shop/cards/result-charts/SpendDistribution.vue';
  import PtcDisplay from 'components/market/shop/cards/PtcDisplay.vue';
  import ShopAiChip from 'components/market/shop/ai/ShopAiChip.vue';
  import SimilarList from 'components/compare/cards/SimilarList.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useOrgs} from 'stores/orgs';
  import {useGps} from 'stores/gps';
  import {idGet} from 'src/utils/id-get';
  import {computed, nextTick, onMounted, ref, watch} from 'vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useEnvStore} from 'stores/env';
  import {compareShop} from 'components/compare/utils/compare-shop';
  import {$infoNotify, dollarString} from 'src/utils/global-methods';

  import {LocalStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';
  import {getCoverageRate} from 'components/coverages/utils/display';
  import {ordinalSuffixOf, pt} from 'components/market/utils';
  import {quickTaxTotal} from 'components/households/utils/tax-tables';
  import {getStats, shopToHousehold} from 'components/market/shop/utils';
  import {useShops} from 'stores/shops';

  const envStore = useEnvStore();
  const router = useRouter();
  const route = useRoute();

  const orgStore = useOrgs();
  const gpsStore = useGps();
  const shopStore = useShops();

  const props = defineProps({
    gpsId: { required: true },
    shopId: { required: true }
  })
  const chatOn = ref(false)
  const similarDialog = ref(false)

  const { item: gps } = idGet({
    store: gpsStore,
    value: computed(() => props.gpsId)
  ,
    useAtcStore
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => gps.value.org)
  ,
    useAtcStore
  })

  const { item: shop } = idGet({
    store: shopStore,
    value: computed(() => props.shopId)
  ,
    useAtcStore
  })

  const afterFn = (val) => {
    if (val._id) {
      LocalStorage.setItem('shop_id', val._id);
      router.push({ ...route, params: { ...route.params, shopId: val._id } })
    }
  }

  const { form: shopForm, save: saveShop } = HForm({
    store: shopStore,
    value: shop,
    notify: false,
    beforeFn: (val) => {
      if (!val.gps) val.gps = gps.value._id;
      if (!val.person) val.person = person.value._id;
      if (!val.name && person.value.name) val.name = person.value.name
      if (!val.email && person.value.email) val.email = person.value.email
      return val;
    },
    afterFn,
  })
  const { autoSave: autoSaveShop, patchObj } = HSave({
    form: shopForm,
    store: shopStore,
    save: saveShop,
    pause: computed(() => !shop.value?._id)
  })

  const tab = ref('info');

  const pickEmoji = (val) => {
    if (val) form.value.body += val;
  }

  const cashDialog = ref();
  const namePopup = ref();
  const selected = ref();
  const togglePopup = (val) => {
    if (!val) {
      selected.value = undefined;
    }
  }

  const setName = () => {
    if (shopForm.value._id) autoSaveShop('name')
    else if (shopForm.value.name) saveShop()
  }

  const setEmail = () => {
    if (shopForm.value._id) autoSaveShop('email')
    else if (shopForm.value.email) saveShop()
  }

  const toggleCash = () => {
    const val = !shopForm.value.cashInLieu;
    shopForm.value.cashInLieu = val
    if (val) {
      delete shopForm.value.coverage
      delete shopForm.value.policy
      shopForm.value.keepOld = false;
      patchObj.value.$unset = { ...patchObj.$unset, coverage: '', policy: '' }
      patchObj.value.keepOld = false
    }
    autoSaveShop('cashInLieu', val);
    cashDialog.value = false;
  }

  const coverageId = computed(() => shopForm.value.majorMedical)
  const coverage = computed(() => (gps.value.coverages || {})[coverageId.value] || {})

  const {
    selectedPlan,
    similar,
    person,
    household,
    activeTypes,
    toggleType,
    stats,
    loading,
    covRank,
    toggleAptc,
    resetMarket,
    setById,
    setStat,
    def_key,
    def_age,
    useAle,
    householdToShopHousehold,
    notabot,
    showTurnstile,
    isAuthenticated,
    allLength,
    byId,
    countdown,
    hh
  } = compareShop({ shopId: shop, gps, coverage })

  const setType = (v) => {
    toggleType(v)
  }
  const ec = computed(() => {
    const { amount = 0, family = 0, type, percentType } = gps.value.employerContribution || {};
    if (!amount && !family) return { amount: 0, display: '' };
    const pct = type === 'percent'
    if (!pct) {
      if (def_key.value === 'single') return { amount, display: dollarString(amount, '$', 0) + '/mo' };
      return { amount: (family || 0) + amount, display: dollarString(family + amount, '$', 0) + '/mo' }
    }
    if (percentType === 'income') {
      const amt = (amount / 100) * stats.value.income || 0
      return { amount: amt, display: `${dollarString(amount, '', 0)}% of your income` }
    } else {
      const single = getCoverageRate({
        coverage: coverage.value,
        enrolled: [{ age: stats.value.age, relation: 'self' }]
      })
      if (def_key.value === 'single') return {
        amount: single * (amount / 100),
        display: `${dollarString(amount, '', 0)}% of plan premium`
      }
      const fam = getCoverageRate({
        coverage: coverage.value,
        def_key: 'family',
        enrolled: [{ age: stats.value.age, relation: 'self' }, ...stats.value.people || []]
      })
      return {
        amount: (fam * ((family || 0) / 100)) + (single * (amount / 100)),
        display: `${dollarString(amount, '', 0)}% of individuals - ${dollarString(family, '', 0)}% of families`
      }
    }
  })

  const dist = computed(() => {
    const { distribution, distribution_ptc } = shop.value || {};
    if (shop.value.useAptc) return distribution_ptc || {};
    return distribution || {}
  })
  const keys = ['1000', '5000', '10000', '20000', '50000', '100000', '*']

  const ranks = computed(() => {
    const obj = {};
    for (let i = 0; i < keys.length; i++) {
      const bench = (dist.value || {})[keys[i]] || {};
      obj[keys[i]] = Object.keys(bench).sort((a, b) => bench[a] - bench[b])
    }
    return obj;
  })
  const lists = computed(() => {
    const obj = {};
    if (!byId.value.all) return obj;
    const arr = Object.keys(ranks.value || {});
    for (let i = 0; i < arr.length; i++) {
      obj[arr[i]] = ranks.value[arr[i]].filter(a => (activeTypes.value || {})[pt(byId.value.all[a])])
    }
    return obj;
  })

  const setHousehold = () => {
    nextTick(() => {
      householdToShopHousehold(household.value, setStat);
    })
  }

  const setIncome = (v) => {
    setStat('income', v)
  }

  const setCoverageId = (id) => {
    shopForm.value.majorMedical = id;
    if (!shopForm.value._id) saveShop()
    else autoSaveShop('majorMedical')
  }

  watch(gps, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const keys = Object.keys(nv.coverages || {})
      if (keys.length === 1) setCoverageId(keys[0])
      if(route.params.shopId){
        const ee = nv.employees?.filter(a => a.sim === route.params.shopId)[0];
        if(ee) {
          setCoverageId(ee.coverage)
          setTimeout(() => {
          if(ee.income && !stats.value.income || stats.value.income === getStats().income){
            setIncome(ee.income)
          }
          }, 1000)
        }
      }
    }
  }, { immediate: true, deep: true })

  const zipData = ref({})
  const setZipData = (val, passive) => {
    zipData.value = val;
    if (val.fips && !passive) {
      const place = { zipcode: val.zip, state: val.state, countyfips: val.fips }
      setStat('place', place);
    }
  }
  const addPlace = (val) => {
    if ((!shop.value._id || !shop.value?.place?.countyfips) && val.countyfips) {
      setStat('place', val);
      envStore.setPlace(val)
    }
  }

  const tax_rate = ref(.06);
  const income = computed(() => shop.value.stats?.income || stats.value.income)
  watch(income, (nv, ov) => {
    if (nv && nv !== ov) {
      const shopHH = shopToHousehold(shop.value)
      let hh_members = shopHH.people
      tax_rate.value = quickTaxTotal({
        income: nv.income,
        hh_members,
        filing_as: hh_members.some(a => a.relation === 'spouse') ? 'mj' : hh_members.length > 1 ? 'hh' : 's'
      }).rate;
    }
  }, { immediate: true })

  const selectPlan = async (v) => {
    if (!shopForm.value._id) await saveShop()
    const unset = (path) => {
      if (shopForm.value[path]) {
        delete shopForm.value[path];
        patchObj.value.$unset = { ...patchObj.value.$unset, [path]: '' }
      }
    }
    if (v.plan_id) {
      shopForm.value.policy = v._id;
      shopForm.value.keepOld = false;
      shopForm.value.cashInLieu = false;
      unset('coverage');
      patchObj.value.keepOld = false;
      patchObj.value.cashInLieu = false;
      autoSaveShop('policy', v.plan_id)

    } else if (!v.id || !gps.value.coverages[v.id]) {
      shopForm.value.coverage = v._id;
      shopForm.value.keepOld = false;
      shopForm.value.cashInLieu = false;
      unset('policy')
      patchObj.value.keepOld = false;
      patchObj.value.cashInLieu = false;
      autoSaveShop('coverage', v._id)

    } else if (gps.value.coverages[v.id]) {
      unset('coverage')
      unset('policy')
      shopForm.value.keepOld = true;
      patchObj.value.cashInLieu = false;
      autoSaveShop('keepOld', true)
    }
    selected.value = undefined
    if (!shopForm.value.name) namePopup.value = true;
  }

  watch(tab, (nv, ov) => {
    if (nv === 'shop' && nv !== ov) {
      setById()
      resetMarket()
    }
  }, { immediate: true })

  const tryInit = (tries = 0) => {
    if (shop.value._id) {
      if (shop.value.majorMedical) tab.value = 'shop'
    } else if (tries < 10) {
      setTimeout(() => tryInit(tries + 1), 500)
    }
  }

  const comparing = ref(false);
  const runCompare = async () => {
    if (!stats.value?.place?.countyfips) return $infoNotify('Add a location to compare')
    const ids = Object.keys(gps.value.coverages || {}).map(a => gps.value.coverages[a].id)
    if (ids.length) {
      const HH = {
        place: stats.value.place,
        people: [{ age: stats.value.age, gender: stats.value.gender }, ...stats.value.people || []],
        income: stats.value.income
      };
      comparing.value = true;
      const res = await gpsStore.get(gps.value._id, {
        runJoin: {
          employer_plan: {
            compare_ids: ids,
            query: { $limit: 25, household: HH, place: HH.place }
          }
        }
      })
          .catch(err => {
            console.error(`Error running compare: ${err.message}`)
          })
      comparing.value = false;

      const { plan_compare } = res._fastjoin;
      const patchIt = {};
      for (const k in plan_compare) {
        patchIt[`coverages.${k}.similar`] = plan_compare[k]
      }
      resetMarket('', false, true)
      await gpsStore.patch(gps.value._id, { $set: patchIt })
      console.log('got res', res);
    } else $infoNotify('Add at least one plan to compare')
  }
  onMounted(() => {
    tryInit(0)
  })

</script>

<style lang="scss" scoped>


  .__left {
    position: relative;

    > div {
      &:first-child {
        position: absolute;
        top: 0;
        right: 0;
        height: 90%;
        width: 60px;
        border-radius: 0 20px 20px 0;
        background: repeating-linear-gradient(
                -45deg,
                var(--q-p4) 0px,
                var(--q-p4) 2px,
                transparent 2px,
                transparent 9px
        )
      }
    }
  }

  .__az {
    width: 100%;
    max-width: 600px;
    padding: 25px 15px;
    border-radius: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 8px var(--ir-light);
    position: relative;
    background: white;

    .__azt {
      padding: 0 10px 15px 10px;
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--ir-mid);

    }
  }

  .__result {
    padding: max(40px, 2vh) min(2vw, 30px) max(100px, 6vh) min(2vw, 30px);
    background: linear-gradient(115deg, var(--ir-bg), var(--q-a0), var(--q-p0), var(--ir-bg));
  }

  .__facts {
    width: 100%;
    display: grid;
    grid-template-columns: min(600px, 40vw) min(600px, 40vw);
    grid-gap: max(2vw, 10px);


    > div {
      width: 100%;
      padding: 35px max(1.5vw, 14px) !important;

      > div {
        &:first-child {
          padding-bottom: 15px;
          font-size: var(--text-sm);
          font-family: var(--alt-font);
          font-weight: 600;
          color: var(--ir-mid);
        }
      }
    }

    .__hh {
      > div {
        display: grid;
        grid-template-columns: 100px 1fr;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
      }
    }
  }

  .__wn {
    position: relative;
    padding: max(3vw, 3vh) max(2vw, 25px);
    border-radius: 15px;
    //background: white;
    overflow: visible;
    color: var(--ir-deep);

    .__l, .__r {
      position: absolute;
      top: 0;
      height: 100%;
      width: 30px;
    }

    .__l {
      border-radius: 15px 0 0 15px;
      left: -30px;
      background: repeating-linear-gradient(
              -45deg,
              var(--q-p2) 0px,
              var(--q-p2) 1px,
              transparent 1px,
              transparent 7px
      )
    }
  }

  .__grid {
    padding: 15px 0;
    background: repeating-linear-gradient(
            -90deg,
            white 0px,
            white 5px,
            transparent 5px,
            transparent 9px
    ), repeating-linear-gradient(
            180deg,
            white 0px,
            white 5px,
            transparent 5px,
            transparent 9px
    )
  }

  .__ai_chip {
    margin-top: 30px;
    width: 100%;

    .AiChip {
      padding: 30px 1vw;
      border-radius: 15px;
      background: linear-gradient(180deg, var(--q-a11), var(--q-a9));
      box-shadow: 0 2px 8px var(--ir-light);
      width: 1500px;
      max-width: 100%;
      transition: all .3s;
      display: grid;
      grid-template-rows: 1fr auto;
    }
    .__on {
      width: 750px;
    }
  }


</style>
