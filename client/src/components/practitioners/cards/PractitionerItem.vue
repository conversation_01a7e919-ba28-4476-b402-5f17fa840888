<template>
  <q-item
      v-bind="{ ...$attrs }"
  >
    <q-item-section avatar>
      <default-avatar :bg-in="bg" :model-value="p" name-path="firstName" :use-atc-store="useAtcStore"></default-avatar>
    </q-item-section>
    <q-item-section>
      <q-item-label class="font-1r tw-five">{{p?.name_prefix || ''}} {{p?.firstName || ''}} {{p?.lastName || ''}} <span class="font-3-4r">{{p?.credential || ''}}</span></q-item-label>
      <q-item-label class="font-7-8r text-grey-8">{{taxonomy}}</q-item-label>
      <q-item-label class="font-7-8r text-p6">{{cities}}</q-item-label>
    </q-item-section>
    <slot name="side" :item="p"></slot>
  </q-item>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {usePractitioners} from 'stores/practitioners';
  import {computed} from 'vue';
  import {getGenderColor} from 'components/households/utils';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = usePractitioners();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: p } = idGet({
    value: computed(() => props.modelValue),
    store,
    useAtcStore
  })

  const bg = computed(() => {
    return getGenderColor(p.value);
  })

  const taxonomy = computed(() => {
    if(p.value?.taxonomy3) return p.value.taxonomy3
    if(p.value?.taxonomy2) return p.value.taxonomy2
    if(p.value?.taxonomy1) return p.value.taxonomy1
    return 'Healthcare Practitioner'
  })

  const cities = computed(() => {
    return (p.value?.cities || []).map(a => `${a.city}, ${a.state}`).join(' & ')
  });
</script>

<style lang="scss" scoped>

</style>
