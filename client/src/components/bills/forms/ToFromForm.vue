<template>
  <div class="_fa">

    <div class="row items-center">
      <div>
        <q-input @input="handleInput" dense input-class="text-xs text-mb-xs text-primary" borderless
                 placeholder="Billing Statement"
                 v-model="form.title"></q-input>
        <div class="text-xxs text-mb-xxs text-ir-grey-6">
          <div>{{ $lget(from, getBillConfig({ subject: 'from', path: 'namePath' })) }}</div>
          <div>{{ $lget(from, getBillConfig({ subject: 'from', path: 'emailPath' })) }}</div>
          <div>{{ $lget(from, getBillConfig({ subject: 'from', path: 'addressDisplayPath' })) }}</div>
        </div>
      </div>
      <q-space></q-space>
      <default-avatar
          size-in="80px"
          square
          :model-value="form"
          avatar-path="logo"
          :name-path="config.from.namePath"
          :use-atc-store="useAtcStore"
      ></default-avatar>
    </div>

    <div class="row">
      <div class="col-12 col-sm-6">
        <div class="q-py-md _fw mw500">
<!--          TODO:add other services here-->
          <org-select emit-value v-model="form.to"></org-select>
        </div>
      </div>
      <div class="col-12 col-sm-6">
        <div class="row justify-end items-end _fh">
          <div>
            <div class="w100">
              <inline-date :input-attrs="{ label: 'Bill Date' }" v-model="form.billDate"></inline-date>
            </div>
            <div class="w100">
              <inline-date :input-attrs="{ label: 'Due Date' }" v-model="form.due"></inline-date>
            </div>
          </div>
        </div>
      </div>
    </div>
    <q-dialog v-model="toDialog">
      <q-card class="q-pa-md _fw mw500">
        <default-item
            :avatar-path="config.to.avatarPath"
            :name-path="config.to.namePath"
            :model-value="form.to"
            :store="config.to.store ? config.to.store() : undefined"
            :use-atc-store="useAtcStore"
        ></default-item>
        <q-separator class="q-my-sm" inset></q-separator>
        <q-input label="Enter Email" v-model="form.toEmail" @update:model-value="handleInput">
          <template v-slot:prepend>
            <q-icon name="mdi-email" color="primary"></q-icon>
          </template>
        </q-input>
        <tomtom-autocomplete v-model="form.toAddress" :use-tomtom-geocode="useTomtomGeocode"></tomtom-autocomplete>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import OrgSelect from 'components/orgs/lists/OrgSelect.vue';

  import {computed, ref, watch} from 'vue';
  import {HForm} from 'src/utils/hForm';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useTomtomGeocode} from 'stores/tomtom-geocode';
  import {useBills} from 'stores/bills';
  import {idGet} from 'src/utils/id-get';
  import {configBill, configOptions} from 'components/bills/utils';


  const billStore = useBills();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    from: Object,
    modelValue: Object
  })

  const addressDialog = ref(false);
  const toDialog = ref(false);
  const fromIndex = ref(0);
  const fromModelIndex = ref(0);
  const toIndex = ref(0);
  const toModelIndex = ref(0);

  const { item: bill } = idGet({
    value: computed(() => props.modelValue),
    store: billStore
  ,
    useAtcStore
  })
  const { form } = HForm({
    store: billStore,
    value: bill
  })

  const { config } = configBill(bill);

  watch(() => props.from, (nv) => {
    if (!form.value?.fromEmail) {
      form.value.fromEmail = nv[config.from.emailPath]
    }
  }, { immediate: true })

  const activeFrom = computed(() => configOptions.fromModels[fromModelIndex.value || 0])
  const activeTo = computed(() => configOptions.toModels[toModelIndex.value || 0])

  const handleInput = () => {
    form.value.toModel = activeTo.value;
    form.value.fromModel = activeFrom.value;
    emit('update:model-value', form.value);
  }
  const addTo = (val) => {
    form.value.toEmail = val[config.to.emailPath] || ''
    form.value.toAddress = val[config.to.addressPath] || undefined
    this.handleInput();
  }

</script>

<style scoped>

</style>
