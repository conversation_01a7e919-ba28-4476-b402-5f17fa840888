<template>
  <div class="row" style="overflow: hidden; width: 100%; height: 100%;">

    <div class="__contract_tool bg-primary text-white">
      <div class="row items-center q-py-sm q-pl-sm q-pr-xl">
        <div class="text-xs text-mb-xs text-weight-medium">Bill</div>
        <q-space></q-space>
        <q-btn dense flat text-color="white" icon="mdi-undo" @click="undo">
          <q-tooltip>Undo</q-tooltip>
        </q-btn>
        <q-btn dense flat text-color="white" icon="mdi-redo" @click="redo">
          <q-tooltip>Redo</q-tooltip>
        </q-btn>
        <q-btn dense flat text-color="white" icon="mdi-eye" @click="previewDialog = true;">
          <q-tooltip>Preview Bill</q-tooltip>
        </q-btn>
      </div>
    </div>

    <div v-if="$q.screen.lt.md" class="t-r" style="z-index: 100; top: 35px;">
      <q-btn round color="white" text-color="primary" icon="mdi-menu" @click="drawer = !drawer"></q-btn>
    </div>
    <div class="col-12 col-md-9 bg-white" style="height: 100vh; overflow-y: scroll; padding-bottom: 50px;">

      <div style="width: 100%;" class="q-pa-md">

        <q-card flat class="q-ma-sm q-pa-sm" style="width: 100%;">

          <to-from-form
              :from="from"
              v-model="form"
          ></to-from-form>

        </q-card>

        <q-separator inset class="q-my-md"></q-separator>

        <div class="text-xs text-mb-xs text-weight-medium q-px-md">Line Items</div>

        <q-item clickable @click="form.lineItems = [defLine(0)], hover = 0"
                v-if="!form.lineItems || !form.lineItems.length">
          <q-item-section avatar>
            <q-icon name="mdi-plus"></q-icon>
          </q-item-section>
          <q-item-section>
            <q-item-label>Add Line</q-item-label>
          </q-item-section>
        </q-item>


        <div style="width: 100%;" v-for="(line, i) in form.lineItems"
             :key="`line-${i}`">

          <q-expansion-item
              style="width: 100%;"
              @click="hover === i ? hover = -1 : hover = i"
              :model-value="hover === i"
          >
            <template v-slot:header>
              <q-item style="width: 100%;">
                <q-item-section avatar>
                  <div class="flex items-center">
                    <div style="display: flex; flex-direction: column; align-items: center">
                      <q-btn dense flat size="sm" icon="mdi-menu-up" v-if="i > 0"
                             @click.stop="moveUp(line, i)"></q-btn>
                      <q-btn dense flat size="sm" icon="mdi-menu-down" v-if="i < form.lineItems.length - 1"
                             @click.stop="moveDown(line, i)"></q-btn>
                    </div>
                    <div class="text-xs text-mb-xs text-weight-bold">{{ i + 1 }}</div>
                  </div>
                </q-item-section>
                <q-item-section>
                  <div class="text-xxs text-mb-xxs text-weight-medium">
                    {{ line && line.title ? line.title : 'Untitled Line' }}
                  </div>
                </q-item-section>
              </q-item>
            </template>

            <div style="width: 100%; height: 2px;" class="bg-primary"></div>
            <div
                style="width: 100%; border-left: solid 2px var(--q-primary); padding-left: 20px; margin-left: 20px;"
                class="q-pa-md q-my-md">
              <line-item-form
                  @remove="form.lineItems.splice(i, 1)"
                  v-model="form.lineItems[i]"
                  @total="taxTotals[i] = $event"
              ></line-item-form>
            </div>

          </q-expansion-item>

          <q-slide-transition>
            <div class="q-px-lg" v-if="hover === i">
              <q-separator inset></q-separator>
              <q-item clickable
                      @click="form.lineItems ? form.lineItems.splice(i + 1, 0, defLine(i + 1)) : form.lineItems = [defLine(i + 1)]"
              >
                <q-item-section avatar>
                  <q-icon name="mdi-plus"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label>Add Line</q-item-label>
                </q-item-section>
              </q-item>
            </div>
          </q-slide-transition>

        </div>
      </div>

      <div class="q-px-md q-py-xl">
        <bill-total
            :tax-totals="taxTotals"
            :model-value="form"
            :currency-icon="currencyIcon"
        ></bill-total>
      </div>

    </div>
    <div :class="`bg-ir-grey-2 col-12 col-md-3 q-pa-md __tree_drawer ${$q.screen.lt.lg ? '__absolute_drawer' : ''}`"
         :style="{ transform: $q.screen.gt.md || drawer ? 'none' : 'translate(150%, 0)', paddingTop: '20px'}">

      <q-card style="width: 500px; max-width: 100%; height: 100%;" class="q-pa-md">

        <div class="text-xxs text-mb-xxs text-ir-grey-6 text-weight-bold">Bill Details</div>
        <q-separator inset class="q-my-sm"></q-separator>


        <div class="text-xxs text-mb-xxs text-ir-grey-6 text-weight-bold">Add Logo Image</div>
        <div class="row items-center">
          <div class="col" v-if="$lisEmpty($lget(form, 'logo')) && !logoSet">
            <q-chip clickable outline color="dark"
                    @click="form.logo = from[config.from.avatarPath], logoSet = true">
              <default-avatar
                  size-in="27px"
                  :model-value="from"
                  square
                  avatar-path="avatar"
                  :use-atc-store="useAtcStore"
              ></default-avatar>
              <div class="q-ml-sm">Use Logo</div>
            </q-chip>

            <q-chip clickable icon="mdi-upload" color="dark" outline label="upload">
              <q-popup-proxy>
                <image-form file-path="logo" @update:model-value="logoSet = true" v-model="form.logo" :use-atc-store="useAtcStore"></image-form>
              </q-popup-proxy>
            </q-chip>
          </div>

          <div v-else class="q-pa-sm" style="position: relative;">
            <q-btn class="t-r-a" size="sm" round push icon="mdi-close" color="negative"
                   @click="form.logo = undefined, logoSet = false">
              <q-tooltip>remove logo</q-tooltip>
            </q-btn>
            <default-avatar square size-in="80px" :model-value="form" avatar-path="logo" name-path="!" :use-atc-store="useAtcStore"></default-avatar>
          </div>

        </div>

        <r-rule-chip-form v-model="form.recurrence"></r-rule-chip-form>

        <div class="q-pt-lg row justify-end">
          <q-btn label="save changes" icon="mdi-content-save" @click="save" color="primary" push></q-btn>
        </div>
      </q-card>
    </div>


    <q-dialog v-model="previewDialog" :maximized="$q.screen.lt.sm" transition-hide="slide-down"
              transition-show="slide-up">
      <q-card style="width: 1000px; max-width: 100%; height: 100vh;">
        <bill-view :model-value="form"></bill-view>
      </q-card>
    </q-dialog>
  </div>

</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import RRuleChipForm from 'components/common/recurrance/RRuleChipForm.vue';
  import ImageForm from 'components/common/uploads/images/ImageForm.vue';

  import ToFromForm from 'components/bills/forms/ToFromForm';
  import BillView from 'components/bills/cards/BillView';
  import LineItemForm from 'components/bills/forms/LineItemForm';
  import BillTotal from 'components/bills/cards/BillTotal';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {HForm} from 'src/utils/hForm';

  import {useBills} from 'stores/bills';
  import {idGet} from 'src/utils/id-get';
  import {configBill} from 'components/bills/utils';

  const billStore = useBills();

  const props = defineProps({
    modelValue: Object,
    toId: { required: false },
    toOptions: { required: false, type: Array },
    from: { required: false },
    fromOptions: { required: false, type: Array },
    fromModel: { required: false },
    logoPath: { type: String, default: 'avatar' },
    backupLogoPath: { type: String },
    productModel: { type: String }
  })

  const taxTotals = ref({})
  const logoSet = ref(false);
  const hover = ref(-1);
  const previewDialog = ref(false);
  const drawer = ref(false);

  const { item:bill } = idGet({
    store:billStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const { config } = configBill(bill);
  const { form, save } = HForm({
    store: billStore,
    value: bill,
    validate: true,
    vOpts: ref({
      to: { name: 'Bill To', v: ['notEmpty'] },
      toModel: { name: 'Bill To Entity', v: ['notEmpty'] },
      toEmail: { name: 'Bill To Email', v: ['email'] },
      from: { name: 'Billing', v: ['notEmpty'] },
      fromModel: { name: 'Billing Entity', v: ['notEmpty'] },
      fromEmail: { name: 'Billing Email', v: ['email'] },
      billDate: { name: 'Bill Date', v: ['notEmpty'] },
      due: { name: 'Due Date', v: ['notEmpty'] },
    })
  })

  const arrMove = (arr, fromIndex, toIndex, obj) => {
    let cloneArr = JSON.parse(JSON.stringify(arr));
    cloneArr.splice(fromIndex, 1);
    cloneArr.splice(toIndex, 0, obj);
    return cloneArr;
  };

  const defaultLine = () => {
    return {
      title: undefined,
      description: '',
      amount: 0,
      currency: 'usd',
      settings: {
        tax: undefined
      }
    };
  };

  const defLine = (order) => {
    return defaultLine(order);
  }

  const moveUp = (item, i) => {
    const obj = { ...item, order: i - 1 };
    form.value.lineItems = arrMove(form.value.lineItems, i, i - 1, obj);
    form.value.lineItems.map((a, idx) => a.order = idx);
  }
  const moveDown = (item, i) => {
    let obj = { ...item, order: i + 1 };
    form.value.lineItems = arrMove(form.value.lineItems, i, i + 1, obj);
    form.value.lineItems.map((a, idx) => a.order = idx);
  }
  watch(() => props.from, (nv) => {
    if (nv) {
      if (!form.value.from) form.value.from = nv._id || nv;
      if (!form.value.fromModel) form.value.fromModel = props.fromModel;
    }
  }, { immediate: true })

  const currencyIcon = computed(() => 'mdi-currency-usd')

</script>

<style scoped lang="scss">
  .comment-rich-editor {
    height: 150px;
    overflow: scroll;
  }

  .__tree_drawer {
    transition: all .3s ease-out;
    transform: none;
    height: 100vh;
    overflow-y: scroll;
  }

  .__absolute_drawer {
    position: absolute;
    top: 0;
    right: 0;
  }

  .__contract_tool {
    width: 100%;
    height: 50px;
    border-radius: 0 0 5px 5px;
  }
</style>
