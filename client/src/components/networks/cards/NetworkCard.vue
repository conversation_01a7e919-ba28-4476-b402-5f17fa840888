<template>
  <div class="_fw">
    <template v-if="network">
      <div class="tw-six font-1r">{{ network.name }}</div>
      <access-chip :model-value="network.access"></access-chip>
      <q-chip v-if="!network.subs?.length">Free</q-chip>
      <q-chip v-else>
        <q-avatar class="bg-a12 text-white">
          <div class="flex flex-center _fa tw-six">{{ network.subs?.length }}</div>
        </q-avatar>
        <span>{{$possiblyPlural('Membership Option', network.subs)}}</span>
      </q-chip>
      <q-chip color="p0">
        <q-avatar class="bg-p10 text-white">
          <div class="flex flex-center _fa tw-six">{{ network.bundles?.length || 0 }}</div>
        </q-avatar>
        <span>{{$pluralExtension('Bundle', network.bundles || [])}}</span>

      </q-chip>
      <q-separator class="q-my-sm"></q-separator>
      <div class="font-7-8r">{{ $limitStr(network.description, 50, '...') }}
        <q-tooltip>
          <div class="w400 mw100 font-7-8r">{{ network.description }}</div>
        </q-tooltip>
      </div>
    </template>
  </div>
</template>

<script setup>
  import AccessChip from 'components/networks/cards/AccessChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useNetworks} from 'stores/networks';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {$limitStr, $pluralExtension, $possiblyPlural} from 'src/utils/global-methods';

  const networkStore = useNetworks();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: false }
  })

  const { item: network } = idGet({
    store: networkStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
