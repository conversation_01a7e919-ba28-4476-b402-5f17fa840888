<template>
  <q-btn
      text-color="white"
      :color="isIn ? 'secondary' : 'primary'"
      no-caps
  >
    <span class="q-mr-sm">{{isIn ? 'Leave Network' : 'Join Network'}}</span>
    <q-icon :name="isIn ? 'mdi-minus' : 'mdi-plus'"></q-icon>
    <remove-proxy
        :remove-label="isIn ? 'Leave Network?' : 'Join Network?'"
        @remove="isIn ? remove() : add()"
    ></remove-proxy>
  </q-btn>
</template>

<script setup>
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useNetworks} from 'stores/networks';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const planStore = usePlans();
  const networkStore = useNetworks();
  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const props = defineProps({
    network: { required: true }
  })

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  ,
    useAtcStore
  })

  const isIn = computed(() => (plan.value?.networks || []).includes(props.network?._id));

  const remove = async () => {
    const updated = await planStore.patch(plan.value._id, { $pull: { networks: props.network._id } })
        .catch(err => $errNotify(`Error removing plan from network: ${err.message}`))

    if (updated) {
      $successNotify(`Plan removed from ${props.network.name} - it may take a few minutes for changes to be reflected`)
      networkStore.get(props.network._id, { runJoin: { sync_plans: true } })
    }
  }
  const add = async () => {
    const updated = await networkStore.patch(props.network._id, { $addToSet: { plan_reqs: plan.value._id } })
        .catch(err => $errNotify(`Error adding plan to network: ${err.message}`))
    if (updated) {
      $successNotify(`Plan added to ${props.network.name} - it may take a few minutes for changes to be reflected`)
    }
  }
</script>

<style lang="scss" scoped>

</style>
