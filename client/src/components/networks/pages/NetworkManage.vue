<template>
  <div class="_fw">

    <div class="row justify-center">
      <div class="_cent">

        <div class="q-pa-sm tw-six font-1r text-grey-7">Edit which networks you participate in</div>

        <manage-strip></manage-strip>


        <q-tab-panels class="_panel" :model-value="!!selected" animated>
          <q-tab-panel class="_panel" :name="true">
            <div class="row items-center">
              <q-btn flat icon="mdi-chevron-left" color="secondary" @click="selected = undefined"></q-btn>
            </div>

            <template v-if="ctx === 'provider'">
              <div class="q-pa-sm font-1r"><provider-chip :model-value="provider"></provider-chip> bundles listed in <b>{{
                  selected?.name
                }}</b></div>
              <q-input filled class="w500 mw100" dense v-model="pbSearch.text" placeholder="Search Bundles">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <div v-if="!p$.data.length" class="q-pa-md text-italic font-1r">No bundles listed</div>
              <div class="row">
                <div class="col-12 col-md-4 q-pa-sm" v-for="(pb, i) in p$.data" :key="`pb-${i}`">
                  <div class="__c">
                    <bundle-card :model-value="pb" :pbSearch="pbSearch.text"></bundle-card>
                    <div class="row justify-end items-center q-pt-sm">
<!--                      <q-btn no-caps flat @click="open(pb)">-->
<!--                        <span>View</span>-->
<!--                        <q-icon class="q-ml-sm" color="accent" name="mdi-eye"></q-icon>-->
<!--                      </q-btn>-->
                      <remove-proxy-btn :icon="undefined" no-caps @remove="removeBundle(pb)" :name="pb.name">
                        <span class="text-black">Remove</span>
                        <q-icon name="mdi-delete" color="red" class="q-ml-sm"></q-icon>
                      </remove-proxy-btn>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row justify-end q-pt-sm">
                <q-pagination
                    @update:model-value="p$.toPage($event)"
                    :model-value="pagination.currentPage"
                    :min="1"
                    :max="pagination.pageCount"
                    direction-links
                    boundary-numbers
                ></q-pagination>
              </div>

            </template>
            <template v-if="ctx === 'admin'">
              <div class="q-pa-md row font-1r">
                <div>
                  <span class="tw-six text-p6">{{plan?.name}}</span> participates in <b>{{selected?.name}}</b>
                  <div class="row justify-end q-pt-sm">
                    <remove-proxy-btn :icon="undefined" no-caps @remove="removePlan()" :name="pb.name">
                      <span class="text-black">Remove</span>
                      <q-icon name="mdi-delete" color="red" class="q-ml-sm"></q-icon>
                    </remove-proxy-btn>
                  </div>
                </div>
              </div>
            </template>

          </q-tab-panel>
          <q-tab-panel class="_panel" :name="false">
            <q-input class="w500 mw100" v-model="search.text" dense filled>
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
              <template v-slot:append>
                <div v-if="n$.isPending">
                  <q-spinner size="30px" color="primary"></q-spinner>
                </div>
              </template>
            </q-input>
            <div v-if="!n$.data.length" class="q-pa-md text-italic font-1r">No networks found</div>

            <div class="row">
              <div class="col-12 col-md-4 q-pa-sm" v-for="(n, i) in n$.data" :key="`network-${i}`">
                <div class="__c" @click="selected = n">
                  <network-card :model-value="n"></network-card>
                </div>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>


  </div>
</template>

<script setup>
  import NetworkCard from 'components/networks/cards/NetworkCard.vue';
  import ManageStrip from 'components/networks/utils/ManageStrip.vue';
  import BundleCard from 'components/providers/bundles/cards/BundleCard.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HQuery} from 'src/utils/hQuery';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {useNetworks} from 'stores/networks';
  import {useProviders} from 'stores/providers';
  import {url_ctx} from 'stores/utils/ctx';
  import {useBundles} from 'stores/bundles';

  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  import {usePlans} from 'stores/plans';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const networkStore = useNetworks();
  const providerStore = useProviders();
  const bundleStore = useBundles();
  const planStore = usePlans();

  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const { ctx } = url_ctx();

  const selected = ref(undefined)

  const { item: provider } = idGet({
    store: providerStore,
    value: computed(() => LocalStorage.getItem('provider_id'))
  ,
    useAtcStore
  })
  const { item:plan } = idGet({
    store: planStore,
    value: getPlanId
  ,
    useAtcStore
  })

  const query = computed(() => {
    const q = {};
    if (ctx.value === 'provider') {
      q.bundles = { $in: provider.value?.bundles || [] }
    } else if(ctx.value === 'admin') {
      q.plans = { $in: [plan.value?._id] }
    }
    return q
  })

  const { search, searchQ } = HQuery({})

  const limit = ref(10);

  const { h$: n$ } = HFind({
    store: networkStore,
    limit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          ...query.value
        }
      }
    })
  })

  const { search: pbSearch, searchQ: pbQ } = HQuery({})
  const { h$: p$, pagination } = HFind({
    store: bundleStore,
    pause: computed(() => ctx.value !== 'provider'),
    limit,
    params: computed(() => {
      return {
        query: {
          ...pbQ.value,
          _id: { $in: selected.value?.bundles || [] }
        }
      }
    })
  })

  const removeBundle = (pb) => {
    bundleStore.patch(pb._id, { $pull: { networks: selected.value._id } })
        .catch(err => $errNotify(`Error removing bundle from network: ${err.message}`))
        .then(() => {
          $successNotify('Bundle removed, changes will be reflected within roughly 1 hour')
        })
    networkStore.patch(selected.value._id, { lastSync: new Date() }, { runJoin: { sync_bundles: true }})
  }

  const removePlan = async () => {
    await planStore.patch(plan.value._id, { $pull: { networks: selected.value._id } })
        .catch(err => $errNotify(`Error removing plan fro network: ${err.message}`))
    networkStore.patch(selected.value._id, { lastSync: new Date() }, { runJoin: { sync_plans: true }})
    selected.value = undefined;
  }

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    margin: 10px 0;
    padding: 20px 15px;
    transform: none;
    transition: all .2s;
    cursor: pointer;

    &:hover {
      transform: translate(0, -3px);
      box-shadow: 0 4px 15px -4px #999;
    }
  }
</style>
