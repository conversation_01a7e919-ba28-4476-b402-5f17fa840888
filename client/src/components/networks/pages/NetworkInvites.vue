<template>
  <div class="_fw">

    <div class="row justify-center">
      <div class="_cent">

        <manage-strip></manage-strip>

        <q-tab-panels class="_panel" :model-value="!!selected" animated>
          <q-tab-panel class="_panel" :name="true">
            <div class="row items-center">
              <q-btn flat icon="mdi-chevron-left" color="secondary" @click="selected = undefined"></q-btn>
              <q-space></q-space>
              <div class="font-7-8r">{{ ctx === 'provider' ? provider?.name : plan?.name }} was invited to join
                <q-chip v-if="ctx === 'provider'" text-color="accent" class="tw-six" clickable>
                  <q-popup-proxy>
                    <div class="w400 mw100 q-pa-md bg-white">
                      <q-list separator>
                        <q-item-label header>Bundles invited</q-item-label>
                        <q-item v-for="(pb, i) in p$.data" :key="`pb-${i}`">
                          <q-item-section>
                            <q-item-label>{{ pb.name }}</q-item-label>
                          </q-item-section>
                          <q-item-section side>
                            <q-btn icon="mdi-check-circle" color="green" @click="accept(pb)">
                              <q-tooltip>Accept Invite</q-tooltip>
                            </q-btn>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-popup-proxy>
                </q-chip>
                <q-chip v-if="ctx === 'admin'" text-color="accent" class="tw-six" clickable @click="accept">
                </q-chip>
              </div>
            </div>

            <network-page :model-value="selected"></network-page>

          </q-tab-panel>
          <q-tab-panel class="_panel" :name="false">
            <div class="q-pa-md font-1r tw-six">
              Networks <provider-chip :model-value="provider" v-if="ctx === 'provider'"></provider-chip><q-chip v-if="ctx === 'admin'" class="tw-six" :label="plan?.name"></q-chip> has been invited to join
            </div>
            <q-input class="w500 mw100" v-model="search.text" dense filled>
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
              <template v-slot:append>
                <div v-if="n$.isPending">
                  <q-spinner size="30px" color="primary"></q-spinner>
                </div>
              </template>
            </q-input>
            <div v-if="!n$.data.length" class="q-pa-md text-italic font-1r">No networks found</div>

            <div class="row">
              <div class="col-12 col-md-4 q-pa-sm" v-for="(n, i) in n$.data" :key="`network-${i}`">
                <div class="__c" @click="selected = n">
                  <network-card :model-value="n"></network-card>
                </div>
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>


  </div>
</template>

<script setup>
  import NetworkCard from 'components/networks/cards/NetworkCard.vue';
  import ManageStrip from 'components/networks/utils/ManageStrip.vue';
  import NetworkPage from 'components/networks/pages/NetworkPage.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HQuery} from 'src/utils/hQuery';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {useNetworks} from 'stores/networks';
  import {useProviders} from 'stores/providers';
  import {url_ctx} from 'stores/utils/ctx';
  import {useBundles} from 'stores/bundles';

  import {usePlans} from 'stores/plans';
  import {$errNotify} from 'src/utils/global-methods';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const networkStore = useNetworks();
  const providerStore = useProviders();
  const bundleStore = useBundles();
  const planStore = usePlans();
  const envStore = useEnvStore();
  const { getPlanId } = contextItems(envStore);

  const { ctx } = url_ctx();

  const selected = ref(undefined)

  const { item: provider } = idGet({
    store: providerStore,
    value: computed(() => LocalStorage.getItem('provider_id'))
  ,
    useAtcStore
  })
  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  ,
    useAtcStore
  })

  const query = computed(() => {
    const q = {};
    if (ctx.value === 'provider') {
      q.bundles_invites = { $in: provider.value?.bundles || [] }
    } else if (ctx.value === 'admin') {
      q.plan_invites = { $in: [plan.value?._id] }
    }
    return q
  })

  const { search, searchQ } = HQuery({})

  const limit = ref(10);

  const { h$: n$ } = HFind({
    store: networkStore,
    limit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          ...query.value
        }
      }
    })
  })

  const { h$: p$ } = HFind({
    store: bundleStore,
    pause: computed(() => ctx.value !== 'provider'),
    limit,
    params: computed(() => {
      return {
        query: {
          _id: { $in: selected.value?.bundle_invites || [] }
        }
      }
    })
  })

  const accept = async (pb) => {
    if(ctx.value === 'provider') {
      const p = await bundleStore.patch(pb._id, { $addToSet: { network: selected.value._id } })
          .catch(err => $errNotify(`Error accepting invite: ${err.message}`))
      if(p){
        networkStore.get(selected.value._id, { runJoin: { sync_bundles: true }})
      }
    } else if(ctx.value === 'admin') {
      const p = await planStore.patch(plan.value._id, { $addToSet: { network: selected.value._id } })
          .catch(err => $errNotify(`Error accepting invite: ${err.message}`))
      if(p){
        networkStore.get(selected.value._id, { runJoin: { sync_plans: true }})
      }
    }
  }

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    margin: 10px 0;
    padding: 20px 15px;
    transform: none;
    transition: all .2s;
    cursor: pointer;

    &:hover {
      transform: translate(0, -3px);
      box-shadow: 0 4px 15px -4px #999;
    }
  }
</style>
