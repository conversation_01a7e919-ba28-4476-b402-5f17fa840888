<template>
  <div class="_fw" v-if="network">
    <div class="row q-pa-md" v-if="['admin', 'provider'].includes(ctx)">
      <q-chip class="tw-six text-accent" v-if="canEdit.ok" clickable color="transparent"  @click="tab === 'reqs' ? tab = 'main' : tab = 'reqs'">
        <q-icon v-if="tab === 'reqs'" name="mdi-chevron-left"></q-icon>
        <span class="q-mx-sm">{{tab === 'main' ? 'Join Requests' : 'Back'}}</span>
      </q-chip>
      <q-space></q-space>
      <pb-manager-btn v-if="ctx === 'provider'" :network="network"></pb-manager-btn>
      <remove-plan-btn v-if="ctx === 'admin'" :network="network"></remove-plan-btn>
    </div>
    <q-tab-panels animated class="_panel" v-model="tab">
      <q-tab-panel class="_panel" name="reqs" v-if="canEdit.ok">


        <network-reqs :network="network"></network-reqs>


      </q-tab-panel>
      <q-tab-panel class="_panel" name="main">

        <div class="row" v-if="canEdit.ok">
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__t">Network Details</div>
              <div class="_form_grid _f_g_r">
                <div class="_form_label">Name</div>
                <div class="q-px-sm">
                  <q-input input-class="font-1-1-8r tw-six" dense borderless :model-value="network.name"
                           @update:model-value="set('name', $event)" @blur="autoSave()"></q-input>
                </div>
                <div class="_form_label">Description</div>
                <div class="q-px-sm">
                  <q-input autogrow dense borderless input-class="font-7-8r" :model-value="network.description"
                           @update:model-value="set('description', $event)" @blur="autoSave()"></q-input>
                </div>
                <div class="_form_label">Access</div>
                <div class="q-px-sm">
                  <access-chip picker :model-value="network.access"
                               @update:model-value="set('access', $event, true)"></access-chip>
                </div>
              </div>

            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__t">Permissions</div>
              <div class="_form_grid _f_g_r">
                <div class="_form_label">Admins</div>
                <div class="q-pa-sm">
                  <div class="__cap">Can manage all network features</div>
                  <budget-members

                      adding
                      :store="networkStore"
                      path="managers"
                      :model-value="network"
                  ></budget-members>
                </div>
                <div class="_form_label">Editors</div>
                <div class="q-pa-sm">
                  <div class="__cap">Can add/approve providers, plans, and edit settings</div>
                  <budget-members
                      adding
                      :store="networkStore"
                      path="writers"
                      :model-value="network"
                  ></budget-members>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="_fw" v-else>
          <div class="__c">
            <div class="__t">Network Details</div>
            <div class="q-pa-md">
              <div class="font-1-1-4r tw-six">{{ network.name }}</div>
              <div class="font-1r">{{ network.description }}</div>
              <access-chip :model-value="network.access"></access-chip>
              <q-chip v-if="!network.subs?.length">Free</q-chip>
              <q-chip v-else>
                <q-avatar class="bg-a12 text-white">
                  <div class="flex flex-center _fa tw-six">{{network.subs?.length}}</div>
                </q-avatar>
                <span>Membership Options</span>
              </q-chip>
            </div>
          </div>
        </div>

        <div class="_fw">
          <div class="__cr __c">
            <div class="row items-center">
              <q-chip label="Bundles" color="transparent" clickable @click="$router.push({ name: 'network-page', params: route.params })" :class="route.name === 'network-page' ? 'text-accent tw-six' : ''"></q-chip>
              <div>|</div>
              <q-chip label="Plans" color="transparent" clickable @click="$router.push({ name: 'network-plans', params: route.params })" :class="route.name === 'network-plans' ? 'text-accent tw-six' : ''"></q-chip>
            </div>
            <router-view :canEdit="canEdit"></router-view>
          </div>
        </div>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';
  import AccessChip from 'components/networks/cards/AccessChip.vue';
  import PbManagerBtn from 'components/networks/cards/PbManagerBtn.vue';
  import RemovePlanBtn from 'components/networks/cards/RemovePlanBtn.vue';
  import NetworkReqs from 'components/networks/cards/NetworkReqs.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useRoute} from 'vue-router';
  import {useNetworks} from 'stores/networks';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {url_ctx} from 'stores/utils/ctx';

  const { login } = loginPerson()


  const route = useRoute();
  const networkStore = useNetworks();

  const props = defineProps({
    modelValue: { required: false }
  })

  const { ctx } = url_ctx();

  const { item: network } = idGet({
    store: networkStore,
    value: computed(() => props.modelValue || route.params.networkId)
  ,
    useAtcStore
  })

  const { canEdit } = clientCanU({
    subject: network,
    or: true,
    caps: computed(() => [['networks', ['write']]]),
    login,
    loginPass: [[['managers/owner'], '*'], [['writers/owner'], ['patch']]]
  })

  const tab = ref('main')

  const patchObj = ref({})

  const dirty = ref(false);
  const time = ref()
  const autoSave = () => {
    if (!dirty.value) return network.value
    networkStore.patchInStore(network.value._id, patchObj.value);
    if (time.value) clearTimeout(time.value);
    time.value = setTimeout(async () => {
      await networkStore.patch(network.value._id, patchObj.value);
      patchObj.value = {}
    }, 2000)
  }

  const set = (path, val, save) => {
    patchObj.value[path] = val;
    dirty.value = true;
    if (save) {
      autoSave()
    }
  }


</script>

<style lang="scss" scoped>
  .__cr, .__c {
    width: 100%;
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
  }

  .__c {
    position: relative;
    padding: 30px 15px 20px 15px;
    height: 100%;

    .__t {
      position: absolute;
      top: 0;
      left: 35px;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: #999;
      color: white;
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }
  .__cr {
    margin: 10px 0;
    padding: 10px;
  }
</style>
