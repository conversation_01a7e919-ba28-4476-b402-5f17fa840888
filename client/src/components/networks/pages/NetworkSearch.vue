<template>
  <div class="_fw">
    <div class="row">
      <div class="q-pa-md">
        <div class="flex items-center">
          <q-img class="h30 w30 q-mr-md" :src="logo"></q-img>
          <div class="tw-six font-1-1-4r">Networks</div>
          <q-btn @click="dialog = true" flat icon="mdi-plus" color="accent"></q-btn>
        </div>
        <q-separator class="q-my-sm"></q-separator>
        <div class="font-1r">Explore and create public or private networks for transparent, direct, cash pricing.
        </div>
      </div>
    </div>

    <q-input class="" v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>

    <div class="row items-center q-py-sm">
    <div class="q-px-xs" v-for="(k, i) in Object.keys(searchTypes)" :key="`st-${i}`">
      <q-radio color="accent" v-model="searchType" :val="k" :label="searchTypes[k].label"></q-radio>
    </div>
    </div>

    <div class="row q-py-md">
      <div class="__c" v-for="(nw, i) in n$.data" :key="`nw-${i}`" @click="openNetwork(nw._id)">
        <network-card :model-value="nw"></network-card>
      </div>
    </div>

    <common-dialog setting="right" v-model="dialog">
      <div class="_fw q-pa-md bg-white">
        <network-form
            @update:model-value="openNetwork($event._id)"></network-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import logo from 'assets/commoncare_icon.svg';
  import NetworkForm from 'components/networks/forms/NetworkForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import NetworkCard from 'components/networks/cards/NetworkCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {HFind} from 'src/utils/hFind';
  import {useNetworks} from 'stores/networks';
  import {HQuery} from 'src/utils/hQuery';
  import {useProviders} from 'stores/providers';
  import {loginPerson} from 'stores/utils/login';
  import {usePlans} from 'stores/plans';
  import {useRouter} from 'vue-router';
  import {url_ctx} from 'stores/utils/ctx';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const { person } = loginPerson()

  const orgStore = useOrgs();
  const networkStore = useNetworks();
  const providerStore = useProviders();
  const planStore = usePlans();
  const router = useRouter();

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const dialog = ref(false);

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  ,
    useAtcStore
  })
  const limit = ref(10);
  const { ctx } = url_ctx();
  const { h$: providers } = HFind({
    store: providerStore,
    limit,
    pause: computed(() => ctx.value !== 'provider'),
    params: computed(() => {
      return {
        query: { org: { $in: (person.value?.inOrgs || []).filter(a => !!a) } }
      }
    })
  })

  const providerFilter = ref([])

  const { h$: plans } = HFind({
    store: planStore,
    limit,
    pause: computed(() => !['account', 'admin'].includes(ctx.value)),
    params: computed(() => {
      return {
        query: { org: { $in: [...(person.value?.inOrgs || []).filter(a => !!a), org.value._id] } }
      }
    })
  })

  const planFilter = ref([])

  const searchType = ref('all');

  const managerFilter = computed(() => {
    return {
      managers: { $in: [person.value._id] },
      writers: { $in: [person.value._id] }
    }
  })
  const partFilter = computed(() => {
    if (searchType.value !== 'part') return {};
    else {
      const q = { ...managerFilter.value };
      if (plans.total) q.plans = { $in: (planFilter.value.length ? planFilter.value : plans.data).map(a => a._id) }
      if (providers.total) q.bundles = { $in: (providerFilter.value.length ? providerFilter.value : providers.data).map(a => a.bundles || []).flat(1) }
      return q;
    }
  })

  const adminFilter = computed(() => {
    if (searchType.value !== 'manage') return {};
    else {
      return {
        $or: [
          { managers: { $in: [person.value._id] } },
          { writers: { $in: [person.value._id] } }
        ]
      }
    }
  })

  const query = computed(() => {
    return {
      ...partFilter.value,
      ...adminFilter.value
    }
  })

  const { search, searchQ } = HQuery({ query, or: true })

  const { h$: n$ } = HFind({
    store: networkStore,
    limit,
    params: computed(() => {
      return {
        query: searchQ.value
      }
    })
  })

  const searchTypes = ref({
    'all': {
      label: 'All Networks'
    },
    'manage': {
      label: 'Networks you manage',
    },
    'part': {
      label: 'Networks you participate in'
    }
  })

  const openNetwork = (id) => {
    router.push({ name: 'network-page', params: { networkId: id } })
  }

</script>

<style lang="scss" scoped>
  .__c {
    width: min(90vw, max(400px, 30vw));
    border-radius: 15px;
    background: white;
    padding: 20px 15px;
    box-shadow: 2px 4px 8px -2px #dedede;
    cursor: pointer;
    transition: all .2s ease;
    margin: 10px;

    &:hover {
      box-shadow: 0 3px 20px -7px #999;
    }
  }
</style>
