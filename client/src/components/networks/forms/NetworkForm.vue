<template>
  <div class="_fw">
    <div class="_f_l _a_chip">Info</div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Name</div>
      <div class="q-pa-sm">
        <q-input dense filled v-model="form.name"></q-input>
      </div>
      <div class="_form_label">Description</div>
      <div class="q-pa-sm">
        <q-input autogrow dense filled v-model="form.description"></q-input>
      </div>
      <div class="_form_label">Access</div>
      <div class="q-pa-sm">
        <access-form v-model="form.access"></access-form>
      </div>
    </div>
    <div class="row justify-end q-py-md">
      <q-btn push class="_a_btn" no-caps label="Open Network" @click="save"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import AccessForm from 'components/networks/forms/AccessForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HForm} from 'src/utils/hForm';
  import {useNetworks} from 'stores/networks';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';

  const networkStore = useNetworks();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: false }
  })

  const { item: network } = idGet({
    store: networkStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const formFn = (defs) => {
    return {
      access: 'private',
      ...defs
    }
  }
  const { form, save } = HForm({
    store: networkStore,
    value: network,
    formFn,
    afterFn: (val) => {
      emit('update:model-value', val);
    }
  })
</script>

<style lang="scss" scoped>

</style>
