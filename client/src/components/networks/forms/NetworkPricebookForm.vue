<template>
  <q-tabs align="left" v-model="tab" no-caps>
    <q-tab name="search" label="Search"></q-tab>
    <q-tab name="add" label="Add New"></q-tab>
  </q-tabs>
  <q-tab-panels v-model="tab" class="_panel" animated>
    <q-tab-panel class="_panel" name="search">
      <q-input filled class="q-my-sm" v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>

      <q-radio v-model="searchAll" :val="true" label="Public Bundles"></q-radio>
      <q-radio v-model="searchAll" :val="false" label="Your Bundles"></q-radio>

      <q-slide-transition>
        <div v-if="!searchAll" class="row q-py-sm">
          <provider-chip :model-value="useProvider" empty-label="No Provider Accounts">
            <template v-slot:right>
              <q-icon v-if="providers.total > 1" name="mdi-menu-down"></q-icon>
            </template>
            <template v-slot:menu>
              <q-menu>
                <div class="w300 mw100 q-pa-md">
                  <q-input v-model="providerSearch.text">
                    <template v-slot:prepend>
                      <q-icon name="mdi-magnify"></q-icon>
                    </template>
                  </q-input>
                  <q-list separator>
                    <q-item v-for="(p, i) in providers.data" :key="`p-${i}`" clickable @click="setProvider = p">
                      <q-item-section>
                        <q-item-label class="tw-six">{{ p.name }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
              </q-menu>
            </template>
          </provider-chip>
        </div>
      </q-slide-transition>

      <div v-if="!p$.data.length" class="q-pa-md text-italic font-1r">No Bundles Found</div>
      <div class="_fw q-pa-sm">
        <div class="__c" v-for="(pb, i) in p$.data" :key="`pb-${i}`">
          <bundle-card :model-value="pb"></bundle-card>
          <div class="row q-pt-sm justify-end">
            <q-btn flat no-caps @click="preview = pb">
              <span class="q-mr-xs">View</span>
              <q-icon name="mdi-eye" color="accent"></q-icon>
            </q-btn>
            <div>
              <q-btn v-if="invites.includes(pb._id)" class="q-ml-sm _p_btn" no-caps push>
                <span class="q-mr-xs">Invited</span>
                <q-icon name="mdi-dots-horizontal"></q-icon>
              </q-btn>
              <q-btn glossy v-else-if="reqs.includes(pb._id)" class="q-ml-sm bg-orange" no-caps push>
                <span class="q-mr-xs">Approve</span>
                <q-icon name="mdi-check-circle"></q-icon>
              </q-btn>
              <q-btn v-else class="q-ml-sm _a_btn" no-caps push>
                <span class="q-mr-xs">Invite</span>
                <q-icon name="mdi-check-circle"></q-icon>
              </q-btn>
              <q-popup-proxy>
                <div class="q-pa-md bg-white">
                  <div class="tw-six font-1r">{{reqs.includes(pb._id) ? 'Approve request to add' : 'Invite'}} {{ pb.name }} to {{ fullNetwork.name }}?</div>
                  <div class="q-pt-sm row justify-end">
                    <q-btn flat @click="add(pb)">
                      <span class="q-mr-xs">{{reqs.includes(pb._id) ? 'Approve' : 'Invite'}}</span>
                      <q-icon color="green" name="mdi-check-circle"></q-icon>
                    </q-btn>
                  </div>
                </div>
              </q-popup-proxy>
            </div>
          </div>
        </div>
      </div>

      <common-dialog setting="smmd" :model-value="!!preview" @update:model-value="closePreview">
        <div class="_fw bg-grey-1 _fh pw1 pd2">
          <bundle-page inline :model-value="preview"></bundle-page>

        </div>
      </common-dialog>

    </q-tab-panel>
    <q-tab-panel class="_panel" name="add">
      <bundle-form @update:model-value="add"></bundle-form>
    </q-tab-panel>
  </q-tab-panels>

</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import BundleForm from 'components/providers/bundles/forms/BundleForm.vue';
  import BundleCard from 'components/providers/bundles/cards/BundleCard.vue';
  import BundlePage from 'components/providers/bundles/pages/BundlePage.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useNetworks} from 'stores/networks';
  import {HFind} from 'src/utils/hFind';
  import {loginPerson} from 'stores/utils/login';
  import {HQuery} from 'src/utils/hQuery';
  import {useOrgs} from 'stores/orgs';
  import {useProviders} from 'stores/providers';
  import {useBundles} from 'stores/bundles';
  import {$errNotify, $successNotify} from 'src/utils/global-methods';
  const { person } = loginPerson()

  const networkStore = useNetworks();
  const orgStore = useOrgs();
  const providerStore = useProviders();
  const pbStore = useBundles();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    network: { required: true }
  })

  const preview = ref(undefined);

  const closePreview = (val) => {
    if (!val) preview.value = undefined
  };

  const { item: fullNetwork } = idGet({
    store: networkStore,
    value: computed(() => props.network)
  ,
    useAtcStore
  })
  const invites = computed(() => fullNetwork.value?.bundle_inites || []);
  const reqs = computed(() => fullNetwork.value?.bundle_reqs || []);

  const tab = ref('search');

  const { h$: o$ } = HFind({
    store: orgStore,
    limit: ref(10),
    params: computed(() => {
      return {
        'owners.id': { $in: [person.value?._id] },
        _id: { $in: (person.value?.inOrgs || []).filter(a => !!a) }
      }
    })
  })

  const searchAll = ref(true)

  const setProvider = ref(undefined)

  const { search: providerSearch, searchQ: searchP } = HQuery({})
  const { h$: providers } = HFind({
    store: providerStore,
    limit: ref(10),
    params: computed(() => {
      return {
        query: { ...searchP.value, org: { $in: o$.data.map(a => a._id) } }
      }
    })
  })

  const useProvider = computed(() => setProvider.value || providers.data[0])

  const { search, searchQ } = HQuery({});

  const { h$: p$ } = HFind({
    store: pbStore,
    params: computed(() => {
      let q = { ...searchQ.value };
      const mq = useProvider.value ? { provider: useProvider.value._id } : { provider: { $in: providers.data.map(a => a._id) } }
      if (searchAll.value) q.$or = [{ public: true }, mq]
      else q = mq;
      const pbs = fullNetwork.value?.bundles || [];
      if(pbs.length) q._id = { $nin: pbs }
      return {
        query: q
      }
    })
  })

  const add = async (pb) => {
    if(reqs.value.includes(pb._id)){
      const nw = await networkStore.patch(fullNetwork.value._id, { $addToSet: { bundles: pb._id }, $pull: { bundle_reqs: pb._id } })
          .catch(err => {
            $errNotify(`Error approving add bundle: ${err.message}`)
            console.error(`Error approving bundle: ${err.value}`)
          })
      if (nw) {
        $successNotify('Invited to network')
        emit('update:model-value', nw)
      }
    } else {
      const nw = await networkStore.patch(fullNetwork.value._id, { $addToSet: { bundle_invites: pb._id } })
          .catch(err => {
            $errNotify(`Error inviting to network: ${err.message}`)
            console.error(`Error inviting bundle: ${err.value}`)
          })
      if (nw) {
        $successNotify('Invited to network')
        emit('update:model-value', nw)
      }
    }
  }


</script>

<style lang="scss" scoped>

  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    margin: 10px 0;
    padding: 20px 15px;
    transform: none;
    transition: all .2s;
    cursor: pointer;

    &:hover {
      transform: translate(0, -3px);
      box-shadow: 0 4px 15px -4px #999;
    }
  }
</style>
