<template>
  <div class="_fw">
    <slot name="title" v-bind="{ geo:form, toggleOpen, open }"></slot>
    <q-slide-transition>
      <div class="_fw" v-if="open">
        <div class="row items-center q-py-xs">
          <template v-for="k in ['list', 'map', 'add']" :key="`t-${k}`">
          <q-chip dense square color="transparent"  clickable @click="tab = k" :label="$capitalizeFirstLetter(k)" :class="tab === k ? 'tw-six' : ''"></q-chip>
          <div v-if="k !== 'add'">|</div>
          </template>
        </div>

        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="list">
            <table>
              <tbody>
              <tr v-for="(feature, i) in form?.features || []" :key="`feature-${i}`">
                <td>{{ parseId(feature) }}</td>
                <td>
                  <remove-proxy-btn @remove="removeFeature(i)" name="Feature"
                                v-bind="{ dense: true, label: '', icon: 'mdi-close', size: 'sm' }"></remove-proxy-btn>
                </td>
              </tr>
              <tr @click="tab = 'add'">
                <td>Add Location</td>
                <td>
                  <q-icon size="18px" color="primary" name="mdi-plus"></q-icon>
                </td>
              </tr>
              </tbody>
            </table>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="map">
            <div class="_fw bg-white q-pa-md relative-position h500 mh100">
              <map-box
                  id="GeoMap"
                  v-if="mapOn"
                  :geo-in="mapGeo"
                  :center="center"
                  fit-bounds
              ></map-box>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="add">

            <div class="_fw bg-white">
              <q-radio v-model="loc" val="state" label="By State"></q-radio>
              <q-radio v-model="loc" val="address" label="By Address"></q-radio>
              <q-tab-panels class="_panel" v-model="loc" animated transition-prev="jump-down" transition-next="jump-up">
                <q-tab-panel class="_panel" name="state">
                  <state-geo-picker
                      :use-junk-drawers="useJunkDrawers"
                      :model-value="[]"
                      dense
                      filled
                      multiple
                      @update:geo="setGeo($event, 'states')"
                  ></state-geo-picker>
                </q-tab-panel>
                <q-tab-panel class="_panel" name="address">
                  <div class="__grd">
                    <km-picker
                        :limit="400"
                        v-model="range"
                        :chip-attrs="{
            class: 'tw-six text-secondary'
          }"
                    ></km-picker>
                    <div>of</div>
                    <div>
                      <q-chip clickable color="transparent">
                        <q-icon color="red" name="mdi-map-marker"></q-icon>
                        <span
                            class="q-mx-sm">{{ address?.city }}{{
                            address?.city ? ', ' : ''
                          }}{{ address?.region || 'Choose Location' }}</span>
                        <q-icon name="mdi-menu-down"></q-icon>
                        <q-popup-proxy>
                          <div class="w400 mw100 q-pa-lg">
                            <tomtom-autocomplete
                                :model-value="address"
                                @update:model-value="envStore.setAddress"
                                dense
                                filled
                                borderless
                                input-class="text-p6"
                            ></tomtom-autocomplete>
                          </div>
                        </q-popup-proxy>
                      </q-chip>
                      <q-btn v-if="address?.city && address?.region && (!lastRange || lastRange !== range)" dense flat
                             icon="mdi-check-circle" color="green" @click="setRange(range)"></q-btn>
                    </div>
                  </div>
                </q-tab-panel>
              </q-tab-panels>

            </div>
          </q-tab-panel>
        </q-tab-panels>


      </div>
    </q-slide-transition>

  </div>
</template>

<script setup>
  import StateGeoPicker from 'components/common/mapbox/utils/StateGeoPicker.vue';
  import TomtomAutocomplete from
        'components/common/address/tomtom/TomtomAutocomplete.vue';
  import KmPicker from 'components/common/mapbox/utils/KmPicker.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import MapBox from 'components/common/mapbox/map/MapBox.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {_get} from 'symbol-syntax-utils';
  import {computed, nextTick, onMounted, ref, watch} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {createGeoJSONCircle, kmToMi} from 'src/utils/geo-utils';
  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {getStateName} from 'components/common/geo/data/states';
  import {useCoverages} from 'stores/coverages';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import {useJunkDrawers} from 'stores/junk-drawers';

  const envStore = useEnvStore()

  const orgStore = useOrgs();
  const coverageStore = useCoverages();

  const props = defineProps({
    plan: { required: false },
    coverage: { required: true },
    adding: Boolean
  })

  const loc = ref('state')
  const tab = ref('list')
  const range = ref(41);
  const open = ref(true);

  const toggleOpen = () => {
    open.value = !open.value;
  }

  const fullPlan = computed(() => props.plan);
  const fullC = computed(() => props.coverage);

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => fullPlan.value?.org)
  ,
    useAtcStore
  })

  const address = ref({});
  const lngLat = ref([82.5310, 27.3365])

  const form = ref({});
  const mapGeo = ref({})
  watch(form, (nv) => {
    if (nv?.geo) {
      mapGeo.value = JSON.parse(JSON.stringify(nv));
    }
  }, { immediate: true })

  watch(fullC, (nv) => {
    if(nv) form.value = nv.geo || {};
  }, {immediate: true})

  watch(org, (nv, ov) => {
    if (nv?.address && nv._id !== ov?._id) envStore.setAddress(nv.address)
  }, { immediate: true })

  const center = computed(() => {
    const { longitude, latitude } = address.value || {
      longitude: _get(lngLat.value, [0], 82.5310),
      latitude: _get(lngLat.value, [1], 27.3365)
    }
    return [longitude, latitude];
  })

  const mapOn = ref(true);

  const resetMap = () => {
    mapOn.value = false;
    nextTick(() => {
      mapOn.value = true;
    })
  }

  const parseId = (feature) => {
    if (typeof feature?.id !== 'string') return '';
    if (feature.id?.includes('states|')) return getStateName(feature.id.split('|')[1])
    else {
      const spl = feature.id.split('|');
      return `< ${kmToMi(spl[0])}mi of ${spl[2] || ''} ${spl[3] || 'Unknown'}`
    }
  }

  const featureWithId = (idKey, feature) => {
    const map = {
      'states': feature.id,
      'range': `${range.value}|km|${address.value?.city}|${address.value?.region}`,
    }
    return { type: 'Feature', ...feature, id: map[idKey] }
  };
  const setGeo = (val, idKey) => {
    val.features = val.features.map(a => featureWithId(idKey, a))
    if (_get(form.value, 'features[0]')) {
      const arr = [...(form.value?.features || [])];
      form.value.geo = { type: 'FeatureCollection', ...form.value, features: [...arr, ...val.features] }
    } else form.value = { type: 'FeatureCollection', ...val };
    coverageStore.patch(fullC.value._id, { $set: { geo: form.value } });
    resetMap()
  }

  const removeFeature = (i) => {
    form.value.features.splice(i, 1);
    if (form.value.features.length === 0) {
      coverageStore.patch(fullC.value._id, { '$unset': { geo: '' } })
    } else coverageStore.patch(fullC.value._id, { $set: { geo: form.value } });
    resetMap()
  }

  const lastRange = ref('');

  const setRange = (val, tries = 0) => {
    if (val) {
      if (typeof val === 'number') {
        if (center.value && center.value[1]) {
          lastRange.value = val;
          setGeo({ 'features': [{ geometry: createGeoJSONCircle(center.value, range.value) }] }, 'range')
        } else if (tries < 5) setTimeout(() => {
          setRange(val, tries + 1);
        }, 250)
      }
    }
  }

  onMounted(() => {
    setTimeout(() => {
      if(envStore.lngLat) lngLat.value = [...envStore.lngLat]
      if(envStore.address) address.value = envStore.address;
    }, 500)
  })

</script>

<style lang="scss" scoped>
  .__grd {
    width: 100%;
    display: grid;
    grid-template-columns: auto auto 1fr auto;
    grid-column-gap: 3px;
    align-items: center;
  }

  @media screen and (max-width: 700px) {
    .__grd {
      grid-template-columns: auto auto 1fr;
      grid-template-rows: auto auto;
      grid-row-gap: 5px;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: .75rem;

    td {
      border-bottom: solid .2px #999;
      padding: 2px 5px;
    }

    tr:last-child {
      cursor: pointer;
      transition: all .2s;
      background: white;

      td {
        padding: 5px 8px;
      }

      &:hover {
        background: var(--q-p0)
      }
    }
  }
</style>
