<template>
  <div class="q-pa-md _fw">
    <div v-if="!!title" class="font-1r">{{ title }}</div>
    <q-separator v-if="!!title" class="q-my-md"></q-separator>
    <div class="_form_grid">
      <!--      LOGO-->
      <template v-if="!limited && !noLogo">
        <div class="_form_label">Logo</div>
        <div class="q-pa-sm">
          <image-select-or-upload v-model="form.avatar" @update:model-value="autoSave('avatar')"></image-select-or-upload>
        </div>
      </template>
      <!--    NAME-->
      <div class="_form_label"><span v-if="full">Trade&nbsp;</span>Name</div>
      <div class="q-pa-sm">
        <q-input @update:model-value="autoSave('name')" v-model="form.name" placeholder="Type Org Name"></q-input>

      </div>
      <template v-if="full">
        <div class="_form_label">Legal Name</div>
        <div class="q-pa-sm">
          <q-input @update:model-value="autoSave('legalName')" v-model="form.legalName" placeholder="Full Legal Name"></q-input>

        </div>
      </template>
      <!--    EMAIL-->
      <template v-if="!limited">
        <div class="_form_label">Email</div>
        <div class="q-pa-sm">
          <email-field @update:model-value="autoSave('email')" :label="undefined" v-model="form.email" :icon="null"
                       placeholder="Type Email"></email-field>

        </div>
      </template>

      <!--    PHONE-->
      <template v-if="!limited">
        <div class="_form_label">Phone</div>
        <div class="q-pa-sm">
          <phone-input @update:model-value="autoSave('phone')" :input-attrs="{ placeholder: 'Type Phone' }"
                       v-model="form.phone"></phone-input>
        </div>
      </template>

      <template v-if="full && form?._id">
        <org-supplement :model-value="form"></org-supplement>
      </template>

      <template v-if="owners">
        <div class="_form_label">Ownership</div>
        <div class="q-pa-sm">
          <cap-table :org="form" v-model="form.owners"></cap-table>
        </div>
      </template>

    </div>


    <!--    SAVE-->
    <div class="row justify-end q-py-md" v-if="!form._id">
      <q-btn push label="Save" color="primary" @click="save()" icon-right="mdi-content-save"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';
  import EmailField from 'src/components/common/input/EmailField.vue';
  import CapTable from 'components/orgs/owners/CapTable.vue';
  import OrgSupplement from 'components/orgs/forms/OrgSupplement.vue';
  import ImageSelectOrUpload from 'components/common/uploads/images/ImageSelectOrUpload.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HForm, HSave} from 'src/utils/hForm';
  import {useOrgs} from 'src/stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref} from 'vue';


  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value', 'close']);
  const props = defineProps({
    modelValue: Object,
    title: { type: String },
    limited: Boolean,
    full: Boolean,
    owners: Boolean,
    noLogo: Boolean
  });

  const { item: org } = idGet({
    value: computed(() => props.modelValue),
    store: orgStore
  ,
    useAtcStore
  });

  const { form, save } = HForm({
    store: orgStore,
    value: org,
    addToStore: true,
    errWatch: true,
    validate: true,
    afterFn: (val) => {
      emit('update:model-value', val);
      emit('close');
    },
    vOpts: ref({
      'name': { name: 'name', v: ['notEmpty'] },
      // 'email': { name: 'email', v: ['notEmpty', 'email']}
    })
  });

  const { autoSave, setBeforeUnmount } = HSave({
    form,
    store: orgStore,
    pause: computed(() => !form.value?._id)
  });

  onMounted(() => {
    setBeforeUnmount();
  })
</script>

<style scoped>
</style>
