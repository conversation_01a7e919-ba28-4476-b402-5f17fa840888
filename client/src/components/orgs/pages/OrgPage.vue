<template>
  <div class="_fw">
    <template v-if="org?._id">
      <div class="row justify-center">
        <div class="_cent pw2 bg-white">

          <div class="_fw __top" :style="{ backgroundImage: `url(${coverImage})`}">
            <div class="__b q-pa-md">
              <div class="_sent">
                <div class="row items-center">
                  <default-avatar size-in="60px" :model-value="org" :use-atc-store="useAtcStore"></default-avatar>
                  <div class="q-px-md font-1r text-weight-bold">{{ org?.name }}</div>
                </div>
              </div>
            </div>

            <q-chip v-if="isAdmin" class="t-l z20" color="white" clickable @click="goAdmin()">
              <q-icon color="primary" name="mdi-star"></q-icon>
              <span class="q-ml-sm">Group Admin - <span class="text-primary tw-six">go</span> to admin page</span>
            </q-chip>

          </div>
          <div class="_fw _bg_ow __gp">
            <q-tabs
                align="left"
                v-model="tab"
                no-caps
                @update:model-value="setTab"
                class="bg-white"
            >
              <q-tab
                  v-for="(t, i) in Object.keys(tabs)"
                  :key="`t-${i}`"
                  :name="t"
                  :label="tabs[t].label"
              ></q-tab>
            </q-tabs>

            <q-tab-panels v-model="tab" class="__p">
              <q-tab-panel
                  class="__pan"
                  v-for="(p, i) in Object.keys(tabs)"
                  :key="`p-${i}`"
                  :name="p"
                  :label="tabs[p].label"
              >
                <component :is="tabs[p].component" v-bind="tabs[p].attrs"></component>
              </q-tab-panel>
            </q-tab-panels>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="row justify-center">
        <div class="_cent pd6 pw3 bg-white">
          <my-orgs></my-orgs>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
  import DefaultAvatar from 'src/components/common/avatars/DefaultAvatar.vue';
  import OrgPlans from 'src/components/orgs/pages/OrgPlans.vue';
  import CamsPage from 'components/comps/cams/pages/CamsPage.vue';
  import MyOrgs from 'components/orgs/cards/MyOrgs.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {getFile} from 'src/utils/fs-utils';

  import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  import {useOrgs} from 'src/stores/orgs';
  import {canU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {useEnvStore} from 'stores/env';
  const { login } = loginPerson()

  const envStore = useEnvStore();
  const store = useOrgs();

  const tab = ref('plans');


  const props = defineProps({
    modelValue: { required: true }
  })

  const runJoin = computed(() => {
    return { owners: true, owns: true };
  });

  const mv = computed(() => props.modelValue || envStore.getOrgId);

  const { item: org, refreshItem, loadedOnce } = idGet({
    value: mv,
    store,
    params: computed(() => {
      return { runJoin: runJoin.value };
    })
  });
  const orgId = computed(() => route.params.orgId || mv.value?._id);

  const coverImage = computed(() => {
    return getFile({
      obj: org.value,
      path: 'cover',
      type: 'image',
      def: 'https://cdn.quasar.dev/img/material.png'
    })
  })

  const isAdmin = ref(false);

  const setTab = (t) => {
    router.push({ ...route, params: { tab: t, id: route.params.id } });
  };

  const routeTab = computed(() => {
    return route.params.tab;
  })

  const goAdmin = () => {
    window.open(`admin.${window.location.host.replace(/^www\./, '')}`, '_blank')
  }

  watch(routeTab, (nv) => {
    if (nv && nv) tab.value = nv;
  });
  watch(mv, (nv) => {
    if (nv && !loadedOnce.value && !nv._fastjoin) {
      refreshItem();
    }
  }, { immediate: true });
  onMounted(() => {
    if (route.params.tab) tab.value = route.params.tab;
  })

  const tabs = computed(() => {
    return {
      'plans': {
        label: 'Benefits',
        component: OrgPlans,
        attrs: {
          org: org.value
        }
      },
      'comp': {
        label: 'My Pay',
        component: CamsPage,
        attrs: {
          org: org.value
        }
      }
    }
  })

  watch(org, async (nv) => {
    const { ok } = await canU({
      requiredCapabilities: [[`orgs:${orgId.value}`, ['orgAdmin']], [`orgs:${orgId.value}`, ['WRITE']], ['orgs', 'WRITE']],
      or: true,
      subject: nv,
      login: login.value
    })
    isAdmin.value = ok;
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__top {
    height: min(500px, 30vh);
    max-height: 50vh;
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 0 0 20px 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px -4px #999;

    .__b {
      background: rgba(255, 255, 255, .5);
      backdrop-filter: blur(10px);
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      display: inline-flex;
      justify-content: center;
    }
  }

  .__p {
    padding: 0 !important;
    background: transparent;
  }

  .__pan {
    background: transparent;
    padding: 2vh 10px;
  }

  .__gp {
    min-height: 50vh;
  }
</style>
