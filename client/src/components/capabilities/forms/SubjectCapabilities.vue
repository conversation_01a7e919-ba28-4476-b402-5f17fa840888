<template>
  <div class="row q-py-md">
    <q-btn @click="dialog = !dialog" no-caps flat color="white">
      <span class="q-mr-sm text-black">Add New</span>
      <q-icon name="mdi-plus" color="primary"></q-icon>
    </q-btn>
  </div>
  <div class="row">
    <div class="col-12 col-md-4 col-lg-3">
      <div class="__c cursor-pointer" v-for="(c, i) in Object.keys(cap?.caps || {})" :key="`cap-${i}`" @click="editing = c">
        <org-cap-card :model-value="capRecord.caps[c]" :name-key="c"></org-cap-card>
      </div>
    </div>
  </div>
  <common-dialog setting="right" @update:model-value="toggleDialog" :model-value="dialog || !!editing">
    <div class="_fw q-pa-md bg-white">
      <org-cap-form @update:path="editing = $event" :need="defNeed" :caps="caps" :model-value="cap" :path="editing"></org-cap-form>
    </div>
  </common-dialog>
</template>

<script setup>

  import OrgCapCard from 'components/capabilities/orgs/cards/OrgCapCard.vue';
  import OrgCapForm from 'components/capabilities/orgs/forms/OrgCapForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useCaps} from 'stores/caps';
  import {ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';

  const capStore = useCaps();

  const props = defineProps({
    caps: { required: true, type: Array }, /** available capabilities to list */
    subjectId: { required: true, type: String }, /** capability subjectId */
    defNeed: { required: true, type: Object } /** Default capability */
  })

  const dialog = ref(false);
  const editing = ref('');

  const toggleDialog = (val) => {
    if(!val) {
      dialog.value = false;
      editing.value = '';
    }
  }

  const capRecord = ref({})
  const { item:cap } = idGet({
    store: capStore,
    value: capRecord
  ,
    useAtcStore
  })

  watch(() => props.subjectId, async (nv) => {
    if(nv && capRecord.value?.subject !== nv){
      const data = await capStore.find({ query: { subject: nv, $limit: 1 }})
      if(data.total) capRecord.value = data.data[0];
      else {
        capRecord.value = await capStore.create({ subject: nv })
      }
    }
  }, { immediate: true });
</script>

<style lang="scss" scoped>
  .__c {
    padding: 20px 2vw;
    background: white;
    border-radius: 12px;
    border: solid 3px var(--ir-light);
  }
</style>
