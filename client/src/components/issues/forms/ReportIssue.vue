<template>
  <div class="_fw">
    <q-tabs inline-label no-caps v-model="form.type" align="left">
      <q-tab  name="content" label="💬 Bugs/Content"></q-tab>
      <q-tab name="complaint" label="⚠️ Complaints"></q-tab>
    </q-tabs>
    <q-tab-panels class="_panel" v-model="form.type" animated>
      <q-tab-panel class="_panel" name="complaint">
        <div class="q-py-md tw-six font-1r">We hope to fix your concern, help us understand how</div>
        <q-list dense>
          <q-item v-for="(item, i) in complaints" :key="`c-${i}`" clickable @click="form.category = item">
            <q-item-section avatar>
              <q-radio v-model="form.category" :val="item"></q-radio>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="content">
        <div class="font-1r text-weight-bold q-py-md font-1r">Tell us what's wrong with this</div>
        <q-list dense>
          <q-item v-for="(item, i) in types" :key="`type-${i}`" clickable @click="form.category = item">
            <q-item-section avatar>
              <q-radio v-model="form.category" :val="item"></q-radio>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-tab-panel>
    </q-tab-panels>
    <slot name="bottom" v-bind="{ type: form?.type, setForm, autoSave, form, by }"></slot>

    <q-slide-transition>
      <div class="_fw" v-if="by?._id">
        <div class="tw-six font-1r q-py-md">Company</div>
        <org-select-chip :params="{ query: { _id: { $in: (by?.inOrgs || []).filter(a => !!a)}}}" v-model="form.org" emit-value></org-select-chip>
      </div>
    </q-slide-transition>

    <q-input class="q-my-md" v-model="form.message" dense autogrow label="Tell us more"></q-input>
    <div class="row justify-end q-py-sm">
      <q-btn flat size="sm" no-caps class="text-weight-bold" @click="save()" label="Submit"
             icon-right="mdi-send"></q-btn>
    </div>

  </div>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useIssues} from 'src/stores/issues';
  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {HFind} from 'src/utils/hFind';
  import {useOrgs} from 'stores/orgs';
  import OrgSelectChip from 'components/orgs/lists/OrgSelectChip.vue';

  const store = useIssues();
  const pplsStore = usePpls();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps({
    record: { type: String },
    service: { type: String, required: false },
    type: { type: String }
  })

  const { form, save } = HForm({
    name: 'ReportIssue',
    store,
    beforeFn: (f) => {
      f.record = props.record;
      f.service = props.service;
      if(!f.type) f.type = props.type;
      return f;
    },
    formFn: (defs) => {
      return {
        service: props.service,
        record: props.record,
        type: 'content',
        ...defs
      }
    },
    afterFn: (val) => {
      emit('update:model-value', val);
    },
    validate: true,
    clearForm: true
  })

  const { autoSave, setForm } = HSave({ form, store, pause: computed(() => !form.value?._id)})

  const { item:by } = idGet({
    store: pplsStore,
    value: computed(() => form.value?.by)
  ,
    useAtcStore
  })

  const types = computed(() => {
    return ['Software Bug', 'Inappropriate Content', 'Irrelevant Content', 'Friendly Suggestion']
  })
  const complaints = computed(() => {
    return ['Provider/Vendor Issue', 'Payment Issue', 'Account Issue', 'Security Flaw', 'Other Issue/Complaint']
  })

  const { h$:o$ } = HFind({
    store: orgStore,
    pause: computed(() => !by.value),
    params: computed(() => {
      return {
        _id: { $in: (by.value?.inOrgs || []).filter(a => !!a) }
      }
    })
  })

  watch(() => props.type, (nv, ov) => {
    if(nv && nv !== ov) {
      setTimeout(() => {
        form.value.type = nv
      }, 100)
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
