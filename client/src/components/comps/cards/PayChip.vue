<template>
  <q-chip
      v-bind="{
    color: 'ir-bg2',
    class: 'tw-six alt-font',
    ...$attrs,
    label: undefined,
    icon: undefined
      }"
  >
    <q-icon size="16px" color="primary" name="mdi-currency-usd"></q-icon>
    <span class="q-pl-xs">{{ display }}</span>
    <slot name="menu"></slot>
  </q-chip>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useComps} from 'src/stores/comps';
  import {fullDisplay} from 'src/components/comps/utils';
  import {useAtcStore} from 'src/stores/atc-store';

  const props = defineProps({
    modelValue: { required: true },
    interval: String
  })

  const store = useComps();

  const { item: comp } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const display = computed(() => {
    if (comp.value) return fullDisplay(comp.value, { interval: props.interval, full: true }) || ''
    else return ''
  })

</script>

<style lang="scss" scoped>

</style>
