<template>
  <div class="__ua">
    <div class="__d row items-center">
      <q-tabs content-class="text-a3" active-class="text-accent" no-caps v-model="tab" indicator-color="a3">
        <q-tab v-for="(sec, i) in sections" :key="`d-${i}`" :name="i">
          <span class="text-xxs tw-six">{{ sec.label }}</span>
        </q-tab>
      </q-tabs>
    </div>
    <div class="__f">
      <q-tab-panels class="_panel" v-model="tab" animated>
        <q-tab-panel class="_panel" v-for="(sec, i) in sections" :name="i" :key="`panel-${i}`">
          <component :is="sections[i].component" v-bind="sections[i].attrs" v-on="sections[i].on"></component>
        </q-tab-panel>
      </q-tab-panels>
    <div class="q-pt-lg row justify-end items-center">
      <q-chip class="tw-six bg-accent" text-color="white" square icon="mdi-chevron-left" v-if="tab > 0" :label="sections[tab-1].label" clickable @click="tab--"/>
      <q-chip class="tw-six text-white bg-accent" text-color="white" square icon-right="mdi-chevron-right" v-if="tab < sections.length - 1" :label="sections[tab+1].label" clickable @click="tab++"/>
    </div>
    </div>
  </div>
</template>

<script setup>
  import HouseholdInfo from 'components/market/shop/utils/ua/sections/HouseholdInfo.vue';
  import HouseholdFinance from 'components/market/shop/utils/ua/sections/HouseholdFinance.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useHouseholds} from 'stores/households';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import CoverageHistory from 'components/market/shop/utils/ua/sections/CoverageHistory.vue';
  import AttestForm from 'components/market/shop/utils/ua/sections/AttestForm.vue';
  import {useShops} from 'stores/shops';

  const hhStore = useHouseholds();
  const pplStore = usePpls();
  const shopStore = useShops();

  const props = defineProps({
    person: { required: true },
    shop: { required: true }
  })

  const tab = ref(0)

  const { item: ppl } = idGet({
    store: pplStore,
    value: computed(() => props.person)
  ,
    useAtcStore
  })

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => ppl.value.household)
  ,
    useAtcStore
  })

  const patchObj = ref({})
  const saveTo = ref()
  const maybeSaveShop = (v) => {
    for(const k in v){
      patchObj.value[k] = v[k];
    }
    if(saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(async () => {
      await shopStore.patch(props.shop._id, { $set: patchObj.value })
      patchObj.value = {}
    }, 2000)
  }

  const sections = computed(() => {
    return [
      {
        key: 'household',
        label: 'Household',
        icon: 'mdi-home',
        component: HouseholdInfo,
        attrs: {
          household: hh.value,
          person: ppl.value
        }
      },
      {
        key: 'finance',
        label: 'Finances',
        icon: 'mdi-bank',
        component: HouseholdFinance,
        attrs: {
          person: ppl.value
        }
      },
      {
        key: 'coverage',
        label: 'Coverage',
        icon: 'mdi-security',
        component: CoverageHistory,
        attrs: {
          person: ppl.value,
          household: hh.value
        }
      },
      {
        key: 'sign',
        label: 'Sign',
        icon: 'mdi-file-sign',
        component: AttestForm,
        attrs: {
          shop: props.shop
        },
        on: {
          'update:model-value': (val) => {
            maybeSaveShop({ attest: val })
          }
        },
      }
    ]
  })


</script>

<style lang="scss" scoped>

  .__ua {
    width: 100%;
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    grid-template-rows: auto 1fr;

    .__f {
      padding: 15px 0;
      position: relative;
      width: 100%;
    }
  }

</style>
