<template>
  <q-page class="q-pa-md">
    <div class="row justify-center">
      <div class="_cent">
        <div class="row q-py-sm">
          <div class="col-12 col-md-6">
            <q-input rounded outlined label="Search" v-model="search.text">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
          </div>
        </div>
        <q-table
            :rows-per-page-options="[0]"
            flat
            :columns="columns"
            :rows="h$.data"
            hide-no-data
            hide-bottom
            hide-pagination
        >
          <template v-slot:header="scope">
            <!--        <q-th auto-width></q-th>-->
            <q-th
                v-for="col in scope.cols"
                :key="col.name"
                :props="scope"
            >
              {{ col.label }}
            </q-th>
            <q-th></q-th>
          </template>
          <template v-slot:body="scope">
            <q-tr :props="scope">
              <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
                <component v-if="col.component" :is="col.component" v-bind="col.attrs(scope.row, col)"></component>
                <div v-else>{{ col.value }}</div>
              </q-td>
              <q-td>
                <q-btn @click="editing = scope.row" dense flat icon="mdi-dots-vertical"></q-btn>
              </q-td>
            </q-tr>
          </template>
        </q-table>
        <div class="row justify-end">
          <q-pagination
              @update:model-value="h$.toPage($event)"
              :model-value="pagination.currentPage"
              :min="1"
              :max="pagination.pageCount"
              direction-links
              boundary-numbers
          ></q-pagination>
        </div>
      </div>
    </div>

    <common-dialog :model-value="!!editing" @update:modelValue="editing ? editing = undefined : ''">
      <upload-form
          :model-value="editing"
          @update:model-value="editing = undefined"
      ></upload-form>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import UsageChip from 'src/components/uploads/admin/UsageChip.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import UploadForm from 'components/uploads/admin/UploadForm.vue';

  import fileTypeHandler from 'src/components/common/uploads/file-types/fileTypeHandler.vue';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useUploads} from 'stores/uploads';
  const store = useUploads();
  import { bytes } from 'src/components/common/uploads/utils';
  import {HQuery} from 'src/utils/hQuery';


  const editing = ref(undefined);
  const columns = computed(() => {
    return [
      {
        label: 'File',
        name: 'owner',
        component: fileTypeHandler,
        attrs: (row) => {
          return {
            divAttrs: { style: 'width: 40px; height: 40px'},
            file: row,
            url: row.url
          }
        }
      },
      {
        name: 'originalname',
        label: 'Name',
        component: TdText,
        attrs: (row, col) => {
          return {
            col
          }
        }
      },
      {
        name: 'info.type',
        label: 'Type',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: row.info?.type}
          }
        }
      },
      {
        name: 'info.size',
        label: 'Size',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: bytes(row.info.size) }
          }
        }
      },
      {
        name: 'usage',
        label: 'Usage',
        component: UsageChip,
        attrs: (row) => {
          return {
            modelValue: row
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name,
        ...a
      };
    })
  });

  const search = ref({ text: '', keys: ['originalname'] });

  const query = computed(() => {
    return {}
  })
  const { searchQ } = HQuery({
    search,
    query
  })

  const params = computed(() => {
    return {
      query: searchQ.value
    }
  })
  const { h$, pagination } = HFind({
    store,
    params,
    qid: 'UploadsAdmin',
    limit: ref(10)
  })
</script>

<style scoped>

</style>
