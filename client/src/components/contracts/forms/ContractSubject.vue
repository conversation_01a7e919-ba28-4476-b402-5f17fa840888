<template>
  <div class="flex items-center">
    <q-chip
        v-bind="{color: 'ir-grey-2', label: subjects[subjectService]?.label || 'Subject Type', iconRight:  'mdi-menu-down', ...chipAttrs}">
      <q-menu>
        <div class="w250 mw100 q-pa-sm bg-white">
          <q-list separator>
            <q-item-label header>Contract Subjects</q-item-label>
            <q-item clickable v-for="(k, i) in Object.keys(subjects)" :key="`subj-${i}`"
                    @click="emit('update:subject-service', k)">
              <q-item-section>
                <q-item-label>{{ subjects[k].label }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon v-if="subjectService === k" color="green" name="mdi-check"></q-icon>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-menu>
    </q-chip>
    <div>
      <default-chip
          v-if="subject"
          v-bind="{ ...subjects[subjectService]?.optionAttrs || {}}"
          :model-value="fullSubject"
          icon-right="mdi-menu-down"
          :use-atc-store="useAtcStore"
      ></default-chip>
      <q-chip v-else
              v-bind="{ label: `Select ${subjects[subjectService]?.itemLabel || 'subject'}`, color: 'ir-grey-2', iconRight: 'mdi-menu-down', ...chipAttrs}"></q-chip>
      <q-menu>
        <div class="w300 mw100 q-pa-sm bg-white">
          <q-input dense filled :model-value="search" @update:model-value="emit('update:search', $event)">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <q-list separator>
            <default-item
                v-for="(opt, i) in subjectOptions || []"
                :key="`opt-${i}`" clickable
                @click="emit('update:subject', opt._id)"
                :model-value="opt"
                :use-atc-store="useAtcStore"
                v-bind="{ ...subjects[subjectService]?.optionAttrs || {}}"
            >
            </default-item>
          </q-list>
        </div>
      </q-menu>
    </div>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';

  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import { contractSubjects } from '../utils/contract-subjects';
  import {useAtcStore} from 'src/stores/atc-store';

  const emit = defineEmits(['update:subject-service', 'update:subject', 'update:search'])
  const props = defineProps({
    subjectService: { default: 'plans' },
    chipAttrs: Object,
    selectAttrs: Object,
    subject: { required: true },
    hostId: { required: false },
    subjectOptions: Array,
    search: String
  })

  const { subjects } = contractSubjects();

  const { item:fullSubject } = idGet({
    store: subjects.value[props.subjectService]?.store,
    value: computed(() => props.subject)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
