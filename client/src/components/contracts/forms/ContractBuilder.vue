<template>
  <div class="_fw">

    <div class="q-pa-sm tw-six font-1r">
      {{ $route.params.type === 'new' ? 'Add' : $route.params.type === 'copy' ? 'Copy' : 'Edit' }} Contract
    </div>
    <div class="row">
      <div class="col-12 col-md-6 q-px-sm q-py-md">
        <div class="__c">
          <div class="__t">Contract Details</div>
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Title</div>
            <div class="q-pa-sm">
              <q-input @update:model-value="autoSave('name')" dense filled v-model="form.name"></q-input>
            </div>
            <div class="_form_label">Contract Description</div>
            <div class="q-pa-sm">
              <q-input dense filled autogrow @update:model-value="autoSave('description')"
                       v-model="form.description"></q-input>
            </div>
            <div class="_form_label">Visibility</div>
            <div class="q-pa-sm">
              <div class="font-3-4r">The executed version of the contract is always private, public visibility means
                others can use this de-personalized contract as a template
              </div>
              <q-radio :model-value="!!form.public" @update:model-value="form.public = $event" :val="false"
                       label="Private"></q-radio>
              <q-radio :model-value="!!form.public" @update:model-value="form.public = $event" :val="true"
                       label="Public"></q-radio>
            </div>
            <div class="_form_label">Reusable Template</div>
            <div class="q-pa-sm">
              <q-checkbox @update:model-value="autoSave('template')" v-model="form.template" :label="form.template ? 'Yes' : 'No'"></q-checkbox>
            </div>

          </div>

          <div class="_fw row justify-end q-pa-md" v-if="!form._id && form.name">
            <q-btn class="_a_btn tw-six" no-caps label="Create Contract" @click="save"></q-btn>
          </div>

        </div>
      </div>
      <div class="col-12 col-md-6 q-px-sm q-py-md">
        <div class="__c">
          <div class="__t">Parties</div>
          <div class="_form_grid _f_g_r">

            <div class="_form_label">Owner</div>
            <div class="q-pa-sm">
              <template v-if="!ownerId">
                <default-chip v-if="form.ownerService === 'orgs'" :store="orgStore" :model-value="form.owner" :use-atc-store="useAtcStore">
                  <template v-slot:right>
                    <q-icon name="mdi-menu-down"></q-icon>
                  </template>
                  <template v-slot:menu v-if="form.ownerService === 'orgs'">
                    <q-popup-proxy>
                      <div class="_fw mw400 q-pa-md bg-white">
                        <q-input v-model="orgSearch.text" dense filled>
                          <template v-slot:prepend>
                            <q-icon name="mdi-magnify"></q-icon>
                          </template>
                        </q-input>
                        <q-list separator>
                          <default-item v-for="(o, i) in o$.data" :key="`org-${i}`" :model-value="o" clickable
                                        :use-atc-store="useAtcStore"
                                        @update:model-value="setForm('owner', o._id)"></default-item>
                        </q-list>
                      </div>
                    </q-popup-proxy>
                  </template>
                </default-chip>
                <default-chip v-else :store="pplStore" :model-value="form.owner" :use-atc-store="useAtcStore"></default-chip>
              </template>
              <template v-else>
                <default-chip :store="orgStore" :model-value="ownerId" :use-atc-store="useAtcStore"></default-chip>
              </template>
            </div>
            <div class="_form_label">Owner Type</div>
            <div class="q-pa-sm">
              <q-radio :disable="!!ownerService" v-for="k in Object.keys(ownerTypes)" :key="`k-${k}`"
                       v-model="form.ownerService" @update:model-value="autoSave('ownerService')" :val="k"
                       :label="ownerTypes[k]"></q-radio>
            </div>
            <div class="_form_label">Herein referred to as</div>
            <div class="q-pa-sm">
              <q-input dense filled :model-value="form.parties['owner']?.aka"
                       @update:model-value="setParty('owner', 'aka', $event)"></q-input>
            </div>
            <div class="_form_label">Contract Subject</div>
            <div class="q-pa-sm">
              <contract-subject
                  :subject-options="subjectOptions"
                  v-model:subject="form.subject"
                  @update:subject="autoSave('subject')"
                  :subject-service="form.subjectService"
                  @update:subject-service="setSubjectService"
                  @update:search="emitSearch"
                  :search="search"
              ></contract-subject>
            </div>
            <div class="_form_label">Signer</div>
            <div class="q-pa-sm">
              <default-chip v-bind="{ ...config.participantAttrs || {}}" :model-value="subjParticipant" :use-atc-store="useAtcStore"></default-chip>
            </div>
            <div class="_form_label">Herein referred to as</div>
            <div class="q-pa-sm">
              <q-input dense filled :model-value="form.parties[config.participantTag]?.aka" @update:model-value="setParty(config.participantTag, 'aka', $event)"></q-input>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="_fw q-px-sm q-py-md">
      <div class="__c">
        <div class="__t">Terms</div>
        <div class="font-1r q-pa-lg __top">When editing this contract, you can use the following tags to auto-render the name
          or "referred to as" when the contract is displayed. This improves reusability and maintainability of
          contracts.
          <div class="_form_grid _f_g_r q-py-md">
            <div class="_form_label">{{ org?.legalName || org?.name }}</div>
            <div class="q-pa-sm">
              <div class="font-1r">${owner}</div>
            </div>
            <div class="_form_label">{{ (fullSubject || {})[config.namePath] }}</div>
            <div class="q-pa-sm">
              <div class="font-1r">{{'${'}}{{ config.tag }}{{'}'}}</div>
            </div>
            <div class="_form_label">{{ (subjParticipant || {})[config.participantNamePath] }}</div>
            <div class="q-pa-sm">
              <div class="font-1r">{{'${'}}{{ config.participantTag }}{{'}'}}</div>
            </div>
          </div>
        </div>
        <div class="font-1r q-pa-lg tw-five" v-if="form.subject && form.subjectService">
          Agreement between {{ fullSubject[config.namePath] }} wherein
          {{ (subjParticipant || {})[config.participantNamePath] }}
          {{ form.parties[config.participantTag]?.aka ? `herein referred to as "${form.parties[config.participantTag].aka}"` : '' }}
          as the {{ config.relationship || 'associate' }} is
          contracting {{ org?.legalName || org?.name }} {{ aka ? `dba ${aka}` : '' }}
          {{ form.parties['owner']?.aka ? `herein referred to as "${form.parties['owner'].aka}"` : '' }} for the
          services and by the
          terms
          outlined in this document.
        </div>

        <div class="_fw" v-if="form?._id">
          <docs-editor
              :id="form._id"
              v-model="form.sections"
              @update:model-value="autoSave('sections', $event)"
              :custom-values="config.customValues"
              :custom-args="args"
          ></docs-editor>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import DocsEditor from 'components/plans/docs/forms/DocsEditor.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import ContractSubject from 'components/contracts/forms/ContractSubject.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useContracts} from 'stores/contracts';
  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, watch} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {useOrgs} from 'stores/orgs';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useRoute} from 'vue-router';
  import {copyContract} from 'components/contracts/utils/copy';
  import {contractSubjects} from 'components/contracts/utils/contract-subjects';
  import {loginPerson} from 'stores/utils/login';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  const { person } = loginPerson()

  const pplStore = usePpls();
  const orgStore = useOrgs();
  const route = useRoute();
  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const store = useContracts();
  const ownerTypes = {
    'orgs': 'Company',
    'ppls': 'Person'
  }

  const props = defineProps({
    modelValue: { required: false },
    subjectService: String,
    subjectOptions: Array,
    search: String,
    ownerService: String,
    ownerId: String,
    aka: String
  })

  const { subjects } = contractSubjects();

  const { item: contract } = idGet({
    store,
    value: computed(() => props.modelValue || route.params.contractId)
  ,
    useAtcStore
  });

  const { item: org } = idGet({
    store: orgStore,
    value: getOrgId
  ,
    useAtcStore
  })
  const owner = computed(() => {
    const service =  route.query.ownerService
    return {
      id: route.query.ownerId,
      service,
      val: service === 'ppls' ? person.value : org.value
    }
  })

  const formFn = (defs) => {
    let ovr = {}
    if (route.params.type === 'copy') {
      ovr = copyContract(defs);
      ovr.owner = owner.value._id || person.value._id;
      ovr.ownerService = owner.value.service || 'orgs';
    }
    if (!defs?.subjectService) ovr.subjectService = props.subjectService || 'plans'
    return {
      owner: owner.value.id || person.value?._id,
      ownerService: owner.value.service || 'ppls',
      parties: {},
      ...defs,
      ...ovr
    }
  }
  const { form, save } = HForm({
    store,
    formFn,
    value: contract,
    beforeFn:(val) => {
      if(!val.owner){
        val.owner = owner.value.id || person.value?._id
        val.ownerService = owner.value.service || 'ppls'
      }
      if(val.parties?.owner && !val.parties.owner.id){
        val.parties.owner.id = val.owner || contract.value.owner;
        val.parties.owner.idService = val.ownerService || contract.value.ownerService;
      }
      return val;
    }
  })

  const { autoSave, setForm } = HSave({ form, save, store, pause: computed(() => !form.value._id) })

  const config = computed(() => subjects.value[form.value?.subjectService] || {})

  const { item: fullSubject } = idGet({
    store: config.value.store,
    value: computed(() => form.value?.subject)
  ,
    useAtcStore
  })

  const { item: subjParticipant } = idGet({
    store: config.value?.participantStore,
    value: computed(() => (fullSubject.value || {,
    useAtcStore
  })[config.value?.participantPath])
  })

  const args = computed(() => {
    return {
      subject: fullSubject.value,
      participant: { ...subjParticipant.value, ...form.value[config.value.participantTag]},
      owner: owner.value.val
    }
  })

  const setParty = (key, path, val) => {
    form.value.parties[key] = { ...form.value.parties[key], [path]: val }
    autoSave('parties');
  }

  const setSubjectService = (val) => {
    if (!props.subjectService) {
      form.value.subjectService = val;
      autoSave('subjectService')
    }
  }
  const emitSearch = (val) => {
    emit('update:search', val)
  }

  watch(owner, (nv) => {
    if (nv.id && !form.value?.owner) {
      form.value.owner = nv.id;
      if (nv.service) form.value.ownerService = nv.service
    } else if (person.value && !form.value?.owner) {
      form.value.ownerSerivce = 'ppls'
      form.value.owner = person.value?._id
    }
  }, { immediate: true })
  //
  const os = computed(() => form.value?.ownerService);
  watch(os, (nv, ov) => {
    if (nv && nv !== ov) {
      if (nv === 'orgs' && org.value?._id) form.value.owner = org.value?._id
      else if (nv === 'ppls' && person.value?._id) form.value.owner = person.value._id
    }
  }, { immediate: true })

  const { search: orgSearch, searchQ } = HQuery({})
  const { h$: o$ } = HFind({
    store: orgStore,
    pause: computed(() => !form.value?.ownerService === 'orgs'),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

  .__c {
    padding: 25px 15px 25px 15px;
    height: 100%;
    border-radius: 15px;
    background: white;
    box-shadow: 2px 2px 8px -2px #dedede;
    position: relative;
  }

  .__t {
    padding: 5px 10px 15px 10px;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .__top {
    background: var(--q-ir-grey-2);
    border-radius: 10px;
  }

</style>
