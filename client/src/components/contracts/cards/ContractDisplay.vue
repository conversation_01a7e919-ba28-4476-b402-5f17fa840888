<template>
  <div class="_fw relative-position">
    <div class="row q-py-sm" v-if="isMobile">
      <q-btn icon="mdi-menu" color="blue-10" flat @click="drawer = !drawer"></q-btn>
    </div>
    <div class="row">
      <div :class="isMobile ? `__drawer __d bg-white ${drawer ? '' : '__off'}` : 'col-3 bg-ir-grey-2 __d'">
        <q-scroll-area style="height: 100%; width: 100%">
          <div v-if="isMobile" class="row q-py-md">
            <q-btn icon="mdi-close" color="blue-10" @click="drawer = false"></q-btn>
          </div>
          <sections-list
              @update:active="setActive"
              :model-value="doc?.sections"
              v-model:active="active"
          ></sections-list>
        </q-scroll-area>
      </div>
      <div :class="`col-${isMobile ? '12' : '9'} __d`">
        <q-scroll-area ref="scrollAreaRef" style="width: 100%; height: 100%;">
          <!--        SECTION TITLE-->
          <div class="font-1-1-4r tw-six text-p7 q-pa-md">
            <span class="num-font __n">
          {{ section }}</span>&nbsp;-&nbsp;{{ activeSection?.title }}
          </div>
          <!--        SLIDES-->
          <div class="__preview" v-if="slides">
            <!--          SUB SECTION TITLE-->
            <div class="font-1-1-4r tw-six text-grey-8 q-py-sm"><span class="num-font __n">{{ active || '' }}</span>&nbsp;
              {{ activeBody?.title }}
            </div>
            <md-preview style="line-break: normal; width: 100%; word-wrap:break-word; word-break:normal" :model-value="activeBody?.body"></md-preview>
          </div>
          <!--        CONTINUOUS VIEW-->
          <div v-else class="__preview">
            <div class="_fw"
                 v-for="(k, i) in Object.keys(activeSection?.sections || {})"
                 :key="`section-${i}`"
                 :id="`${section}_${k}`">
              <!--            SUBSECTION TITLE-->
              <div class="font-1-1-4r tw-six text-grey-8 q-py-sm"><span class="num-font __n">{{ section }}.{{
                  k
                }}</span>&nbsp;
                {{ activeSection.sections[k].title || '' }}
              </div>
              <div class="q-py-sm _fw">
                <md-preview style="line-break: normal; width: 100%; word-wrap:break-word; word-break:normal" :model-value="activeSection.sections[k].body"></md-preview>
              </div>
            </div>
          </div>
        </q-scroll-area>
      </div>
    </div>
  </div>
</template>

<script setup>
  import SectionsList from 'components/plans/docs/cards/SectionsList.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, nextTick, ref} from 'vue';
  import {Screen} from 'quasar';

  import {MdPreview} from 'md-editor-v3';
  import {_get} from 'symbol-syntax-utils';
  import {useContracts} from 'stores/contracts';

  const store = useContracts();

  const props = defineProps({
    mobile: Boolean,
    modelValue: { required: true },
    slides: Boolean
  })

  const drawer = ref(false);
  const active = ref('1.1');
  const scrollAreaRef = ref();
  const section = computed(() => active.value?.split('.')[0] || '')

  const activeSection = computed(() => {
    const spl = active.value?.split('.');
    return _get(doc.value, ['sections', spl[0]]);
  })

  const activeBody = computed(() => {
    const spl = active.value?.split('.');
    if (spl) {
      return _get(doc.value, ['sections', spl[0], 'sections', spl[1]]) || '';
    }
    return ''
  })

  const { item: doc } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const isMobile = computed(() => {
    return Screen.lt.md || props.mobile
  })



  const setActive = () => {
    drawer.value = false;
    if (!props.slides) {
      nextTick(() => {
        const id = active.value.split('.').join('_');
        const el = document.getElementById(id);
        if (el) {
          const { top } = el.getBoundingClientRect();
          scrollAreaRef.value.setScrollPosition('vertical', top - 100, 300)
        }
      })
    }
    let ps = document.querySelector('p');
    if(ps && !Array.isArray(ps)) ps = [ps];
    ps?.forEach(p => {
      p.style.wordBreak = 'normal';
    })
  }


</script>

<style lang="scss" scoped>

  .__drawer {
    position: absolute;
    top: 0;
    left: 0;
    width: 400px;
    max-width: 90%;
    transition: width .3s ease-in;
    overflow-x: hidden;
    background: white;
    z-index: 20;
  }

  .__off {
    width: 0 !important;
  }

  .__preview {
    width: 100%;
    height: 100%;
    padding: 5vh 3vw;
    font-size: 1rem;
  }

  .__n {
    font-size: .95rem;
    font-weight: 900;
  }

  .__d {
    height: 97vh;
  }

</style>
