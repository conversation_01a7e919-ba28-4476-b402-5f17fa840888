<template>
  <div class="_fw relative-position">
    <div class="tw-six font-1r">{{ contract?.name }}</div>
    <div class="font-7-8r __desc">{{ contract?.description }}
      <q-tooltip class="tw-six text-xxs mw400">{{contract.description}}</q-tooltip>
    </div>
    <div class="q-my-sm"></div>
    <div class="row items-center">
      <q-chip v-if="statuses[contract?.status]">
        <q-avatar :color="statuses[contract.status].color"></q-avatar>
        <span class="tw-five alt-font">{{statuses[contract.status].label}}</span>
      </q-chip>
      <q-chip v-if="contract?.public" color="teal" label="Public" dark class="tw-five alt-font"></q-chip>
      <q-chip v-else color="purple" dark class="tw-five alt-font" label="Private"></q-chip>
      <q-space></q-space>
      <q-icon size="25px" color="accent" v-if="contract.template" name="mdi-stamper">
        <q-tooltip>Reusable Template</q-tooltip>
      </q-icon>
    </div>
  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useContracts} from 'stores/contracts';
  import {useAtcStore} from 'src/stores/atc-store';

  const contractStore = useContracts();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: contract } = idGet({
    store: contractStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const statuses = {
    'open': { label: 'Open', color: 'blue' },
    'rejected': { label: 'Rejected', color: 'red' },
    'executed': { label: 'Executed', color: 'green' },
  }
</script>

<style lang="scss" scoped>

  .__desc {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
</style>
