<template>
  <div class="_fw">
    <div class="row q-py-sm items-center">
      <template v-for="(sub, i) in linkObj?.Group?.subs || []" :key="`ls-${i}`">
        <q-chip dense square clickable @click="$router.push(sub.link.route)" :class="sub.on ? 'bg-transparent tw-six' : 'bg-transparent'" text-color="ir-text">
          <span>{{sub.label}}</span>
        </q-chip>
        <div v-if="i < Object.keys(linkObj).length -1">|</div>
      </template>
    </div>

    <div class="row items-center">
      <div class="font-3-4r tw-six text-ir-mid">Plan:&nbsp;</div>
      <q-chip class="bg-ir-bg2" clickable>
        <default-avatar v-if="planFilter._id" :model-value="planFilter._fastjoin?.org" :use-atc-store="useAtcStore"></default-avatar>
        <span>{{ planFilter.name || 'Select Plan' }}</span>
        <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
        <q-popup-proxy v-model="planMenu">
          <div class="w400 mw100 q-pa-sm">
            <q-input placeholder="Search plans..." dense filled v-model="planSearch.text">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>

            <q-item clickable @click="setPlanId(p._id)" v-for="(p, i) in p$.data" :key="`p-${i}`">
              <q-item-section avatar>
                <default-avatar :model-value="p._fastjoin?.org" :use-atc-store="useAtcStore"></default-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ p.name }}</q-item-label>
                <q-item-label caption>{{ p._fastjoin?.org?.name }}</q-item-label>
              </q-item-section>
            </q-item>

          </div>
        </q-popup-proxy>
      </q-chip>
    </div>

    <q-slide-transition>
      <div class="_fw" v-if="planId">
        <div class="row items-center">
          <div class="font-3-4r tw-six text-ir-mid">Plan Year:&nbsp;</div>
          <plan-year-picker :model-value="activeYear" color="ir-bg2"
                            @update:model-value="setYear"></plan-year-picker>
          <q-chip class="bg-ir-bg2" v-model="evt" clickable>
            <span>{{ evt || 'Select Event' }}</span>
            <q-icon v-if="!evt" class="q-ml-sm" name="mdi-menu-down"></q-icon>
            <q-btn v-else dense flat size="sm" color="red" icon="mdi-close" @click="evt = undefined"></q-btn>
            <q-menu v-model="evtMenu">
              <div class="w300 mw100 q-pa-sm">
                <q-list separator>
                  <q-item v-for="(k, i) in Object.keys(planFilter.enrollments || {})" :key="`evt-${i}`" clickable
                          @click="setEvt(k)">
                    <q-item-section>{{ k.split('_').join('-') }}</q-item-section>
                    <q-item-section caption>{{ $limitStr(planFilter.enrollments[k].description, 80, '...') }}
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-chip>
        </div>
        <div class="row items-center q-py-xs">
          <q-icon color="accent" size="15px" name="mdi-filter" class="q-mr-sm"></q-icon>
          <people-select :chip-attrs="{ removable: false }" :model-value="personId" @update:model-value="setPersonId" emit-value :params="pplParams"></people-select>
          <q-btn v-if="personId" dense flat size="sm" color="red" icon="mdi-close"
                 @click="setPersonId(undefined)"></q-btn>
          <status-chip label="Enrollment Status" color="ir-bg2" :outline="false" picker multiple :model-value="statusFilter" @update:model-value="setStatus"></status-chip>
        </div>
      </div>
    </q-slide-transition>

    <router-view v-bind="{ personFilter, statusFilter, planFilter, evt, activeYear }"></router-view>


  </div>
</template>

<script setup>
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import PeopleSelect from 'components/ppls/lists/PeopleSelect.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {$limitStr, fakeId} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useRoute, useRouter} from 'vue-router';
  const router = useRouter();
  const route = useRoute()

  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {computed, onMounted, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {usePlans} from 'stores/plans';
  import {getCurrentPlanYear} from 'components/plans/utils';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';

  const planStore = usePlans();
  const pplStore = usePpls();

  const props = defineProps({
    linkObj:Object
  })

  const hostId = computed(() => LocalStorage.getItem('host_id') || fakeId)
  const planMenu = ref(false);
  const planId = ref(undefined);
  const setPlanId = (v) => {
    planId.value = v;
    router.replace({ ...route, query: { ...route.query, planId: v }})

  }
  const {item:planFilter } = idGet({
    store: planStore,
    value: planId,
    params: ref({
      runJoin: { plan_org: true }
    })
  })

  const { search: planSearch, searchQ: planQ } = HQuery({})
  const { h$: p$ } = HFind({
    store: planStore,
    pause: computed(() => !planMenu.value),
    params: computed(() => {
      return {
        runJoin: { plan_org: true },
        _search: { teamId: hostId.value },
        query: {
          ...planQ.value
        }
      }
    })
  })

  const yearSelect = ref();
  const setYear = (v) => {
    yearSelect.value = v;
    router.replace({ ...route, query: { ...route.query, year: v }})
  }
  const activeYear = computed(() => yearSelect.value || getCurrentPlanYear(planFilter.value));
  const evt = ref('');
  const evtMenu = ref(false);
  const setEvt = (v) => {
    evt.value = v;
    evtMenu.value = false;
    router.replace({ ...route, query: { ...route.query, evt: v }})
  }

  const statusFilter = ref([])
  const setStatus = (v) => {
    const arr =  Array.isArray(v) ? v : v.split(',')
    statusFilter.value = arr;
    router.replace({ ...route, query: { ...route.query, status: arr.join(',') }})

  }
  const personId = ref()
  const setPersonId = (id) => {
    personId.value = id;
    router.replace({ ...route, query: { ...route.query, personId: id }})
  }
  const {item:personFilter} = idGet({
    store: pplStore,
    value: personId
  ,
    useAtcStore
  })
  const pplParams = computed(() => {
    return {
      query: { inGroups: { $in: planFilter.value.groups || [] } }
    }
  })

  onMounted(() => {
    const q = route.query || {}
    if(q.personId) setPersonId(q.personId);
    if(q.planId) setPlanId(q.planId);
    if(q.evt) {
      setEvt(q.evt);
      setYear(q.evt.split('_')[0])
    } else if (q.year) setYear(q.year);
    if(q.status) setStatus(q.status);
  })
</script>

<style lang="scss" scoped>


</style>
