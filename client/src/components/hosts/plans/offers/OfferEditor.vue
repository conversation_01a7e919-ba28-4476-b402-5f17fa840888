<template>
  <div class="_fw">
    <div class="_fw q-pa-sm">
      <host-offer-form :model-value="offer"></host-offer-form>

    </div>
    <div class="text-center tw-five font-1r">Preview</div>
    <q-separator class="q-mt-sm q-mb-lg"></q-separator>
    <div class="_fw q-pa-sm">
      <offer-page></offer-page>

    </div>
  </div>
</template>

<script setup>
  import HostOfferForm from 'components/hosts/offers/forms/HostOfferForm.vue';
  import OfferPage from 'components/hosts/offers/pages/OfferPage.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useOffers} from 'stores/offers';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';

  const offerStore = useOffers()

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:offer } = idGet({
    store: offerStore,
    value: computed(() => props.modelValue),
    def: {}
  })




</script>

<style lang="scss" scoped>

</style>
