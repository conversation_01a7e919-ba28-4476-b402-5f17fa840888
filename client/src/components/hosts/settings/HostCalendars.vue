<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent q-py-md pw2">
        <div class="row">
          <div class="col-12 q-pa-md">
            <q-chip color="transparent" clickable @click="addC = true">
              <q-icon color="a3" name="mdi-calendar"></q-icon>
              <span class="q-mx-sm tw-six">Calendars</span>
              <q-icon color="accent" name="mdi-plus"></q-icon>
            </q-chip>

            <q-input v-model="search.text" class="_fw mw500" filled dense>
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>

            <div class="row q-py-md">
              <div class="col-12 col-md-4 col-lg-3 q-pa-sm">
                <div class="__c" @click="editC = c" v-for="(c, i) in c$.data" :key="`c-${i}`">
                  <calendar-card :use-calendars="useCalendars" :model-value="c" :use-atc-store="useAtcStore"></calendar-card>
                </div>
              </div>
            </div>
            <div class="row items-center _fw">
                  <span>{{ ((pagination.currentPage * limit) - limit) + 1 }} - {{ pageRecordCount }} of {{
                      c$.total
                    }}</span>
              <q-space></q-space>
              <q-pagination
                  @update:model-value="c$.toPage($event)"
                  :model-value="pagination.currentPage"
                  :min="1"
                  :max="pagination.pageCount"
                  direction-links
                  boundary-numbers
              ></q-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>

    <common-dialog :model-value="addC || !!editC" @update:model-value="toggleC" setting="right">
      <div class="_fw bg-ir-bg text-ir-text q-pa-md">
        <calendar-form :model-value="editC" owner-service="hosts" :owner-id="host._id" @update:model-value="toggleC(false)"></calendar-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CalendarForm from 'components/common/calendar/forms/CalendarForm.vue';
  import CalendarCard from 'components/common/calendar/cards/CalendarCard.vue';

  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {useAtcStore} from 'src/stores/atc-store';
  import {HFind} from 'src/utils/hFind';
  import {useCalendars} from 'stores/calendars';
  import {HQuery} from 'src/utils/hQuery';

  const hostStore = useHosts();
  const calendarStore = useCalendars()

  const { item:host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id'))
  ,
    useAtcStore
  })

  const addC = ref(false)
  const editC = ref();

  const toggleC = (v) => {
    if(!v){
      addC.value = false;
      editC.value = undefined;
    }
  }

  const { search, searchQ } = HQuery({})

  const limit = ref(5)
  const { h$:c$, pagination, pageRecordCount } = HFind({
    store: calendarStore,
    limit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          owner: host.value._id,
          ownerService: 'hosts'
        }
      }
    })
  })


</script>

<style lang="scss" scoped>
  .__c {
    padding: 20px;
    border-radius: 10px;
    border: solid 2px var(--ir-mid);
    //box-shadow: 0 2px 12px -5px var(--ir-mid);
    cursor:pointer;
    background: var(--ir-bg1);
    transition: all .2s;

    &:hover {
      background: var(--ir-bg);
    }
  }
</style>
