<template>
  <div class="_fw row justify-center">
    <div class="_cent pd4 pw2">
      <div class="row q-pb-md">
        <q-btn no-caps class="tw-six" flat @click="adding = true">
          <span>Team</span>
          <q-icon class="q-ml-sm" color="primary" name="mdi-plus"></q-icon>
        </q-btn>
      </div>
      <div class="text-xs tw-six">Use teams to organize customer support workflows</div>

      <div class="_fw">
        <div class="w600 mw100 q-py-lg">
          <q-input v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>

        <div class="row">
          <div class="col-12 col-md-4 col-lg-3 q-pa-sm" v-for="(t, i) in t$.data" :key="`t-${i}`">
            <div class="__c cursor-pointer" @click="openTeam(t)">
              <team-card :model-value="t"></team-card>
            </div>
          </div>
        </div>
        <pagination-row v-bind="{ h$:t$, pagination, pageRecordCount }"></pagination-row>
      </div>
    </div>
  </div>

  <common-dialog setting="right" :model-value="adding || editing" @update:model-value="toggleDialog">
    <div class="_fw bg-ir-bg text-ir-text q-pa-md">
      <team-form
          @update:model-value="openTeam"
          :model-value="editing"
          :host-id="host._id"
      ></team-form>
    </div>
  </common-dialog>
</template>

<script setup>
  import TeamForm from 'components/hosts/team/forms/TeamForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import TeamCard from 'components/hosts/team/cards/TeamCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useTeams} from 'stores/teams';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {HQuery} from 'src/utils/hQuery';
  import {useHosts} from 'stores/hosts';
  import {useRouter} from 'vue-router';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  const router = useRouter();

  const teamStore = useTeams();
  const hostStore = useHosts();

  const adding = ref(false);
  const editing = ref();
  const toggleDialog = (v) => {
    if (!v) {
      adding.value = false;
      editing.value = undefined
    }
  }
  const { item: host } = idGet({
    store: hostStore,
    value: computed(() => LocalStorage.getItem('host_id')),
    def: {}
  })

  const { search, searchQ } = HQuery({})

  const limit = ref(10);
  const { h$: t$, pagination, pageRecordCount } = HFind({
    store: teamStore,
    limit,
    params: computed(() => {
      return {
        query: { _id: { $in: host.value?.teams || [] }, ...searchQ.value }
      }
    })
  })

  const openTeam = (t) => {
    SessionStorage.setItem('team_name', t.name)
    router.push({ name: 'host-team', params: { teamId: t._id } })
  }

</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 2px 8px var(--ir-light);
    padding: 22px 15px;
    background: var(--ir-bg);
  }
</style>
