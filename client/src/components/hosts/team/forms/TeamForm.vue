<template>
  <div class="_fw">
    <div class="q-pa-sm font-1r tw-six text-ir-mid">{{ modelValue ? 'Edit' : 'Add' }} Team</div>
    <div class="_f_g">
      <div class="_f_l _f_chip">Details</div>
      <div class="_form_grid _f_g_r">
        <div class="_form_label">Name</div>
        <div class="q-pa-sm">
          <q-input v-model="form.name" @update:model-value="autoSave('name')"></q-input>
        </div>
        <div class="_form_label">Types</div>
        <div class="q-pa-sm">
          <q-checkbox label="Sales" @update:model-value="toggleType('sales', $event)"
                      :model-value="types.includes('sales')"></q-checkbox>
          <q-checkbox label="Support" @update:model-value="toggleType('support', $event)"
                      :model-value="types.includes('support')"></q-checkbox>
        </div>
        <div class="_form_label">Default Phone</div>
        <div class="q-pa-sm">
          <phone-input :input-attrs="{ dense: true }" v-model="form.phone"
                       @update:model-value="autoSave('phone')"></phone-input>
        </div>
        <div class="_form_label">Default SMS</div>
        <div class="q-pa-sm">
          <phone-input :input-attrs="{ dense: true }" v-model="form.sms"
                       @update:model-value="autoSave('sms')"></phone-input>
        </div>
        <div class="_form_label">Default Email</div>
        <div class="q-pa-sm">
          <email-field dense v-model="form.email" @update:model-value="autoSave('email')"/>
        </div>
      </div>
      <div v-if="!form?._id" class="q-pa-md row justify-end">
        <q-btn class="_pl_btn tw-six" push no-caps label="Create Team" @click="save"></q-btn>
      </div>
      <q-slide-transition>
        <div class="_fw" v-if="form?._id">
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Calendar</div>
            <div class="q-pa-sm">
              <calendar-chip :use-calendars="useCalendars" color="ir-bg2" :owner-id="form.host" v-model="form.calendar" picker emit-value :use-atc-store="useAtcStore"></calendar-chip>
            </div>
            <div class="_form_label">Support Priority</div>
            <div class="q-pa-sm">
              <ref-priority v-model="form.priority" :query="{ host: form.host, approved: true }"></ref-priority>

            </div>
            <div class="_form_label">Remove Team</div>
            <div class="q-pa-md">
              <remove-proxy-btn two-step :name="form.name" @remove="removeTeam"></remove-proxy-btn>
            </div>
          </div>
          <div class="q-pa-md row justify-end" v-if="Object.keys(patchObj || {}).length">
            <q-btn @click="saveAll" class="_pl_btn tw-six" no-caps label="Save Changes"></q-btn>
          </div>
        </div>
      </q-slide-transition>
    </div>
  </div>
</template>

<script setup>
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import CalendarChip from 'components/common/calendar/utils/CalendarChip.vue';
  import RefPriority from 'components/hosts/team/utils/RefPriority.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, ref} from 'vue';
  import {useTeams} from 'stores/teams';
  import {$errNotify} from 'src/utils/global-methods';
  import { useCalendars } from 'stores/calendars';
  import {useAtcStore} from 'src/stores/atc-store';

  const teamStore = useTeams();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    hostId: { required: false }
  })

  const { form, save } = HForm({
    store: teamStore,
    afterFn: (val) => {
      emit('update:modelValue', val);
    },
    beforeFn: (val) => {
      return {
        host: props.hostId,
        ...val
      }
    },
    value: computed(() => props.modelValue)
  })

  const { autoSave, patchObj } = HSave({
    form,
    save,
    store: teamStore,
    pause: ref(true),
    delay: 100
  })


  const saveAll = () => {
    if (Object.keys(patchObj.value || {}).length) {
      teamStore.patch(form.value._id, patchObj.value)
    }
  }

  const removeTeam = async () => {
    teamStore.remove(form.value._id)
        .catch(err => $errNotify(`Error removing team: ${err.message}`))
  }


  const types = computed(() => form.value?.types || [])

  const toggleType = (val, add) => {
    if (add) {
      if (types.value.includes(val)) return
      form.value.types = [...types.value, val]
    } else {
      const idx = types.value.indexOf(val);
      form.value.types.splice(idx, 1);
    }
    autoSave('types')
  }
</script>

<style lang="scss" scoped>

</style>
