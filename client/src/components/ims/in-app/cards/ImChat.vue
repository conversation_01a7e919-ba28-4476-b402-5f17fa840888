<template>
  <div class="__chat_window">
    <div id="ChatScroll" class="_fw q-pa-sm" @scroll="senseScroll">
      <div v-if="!messages.length" class="q-pa-md font-3-4r text-italic">Start the conversation</div>
      <template v-else>
        <div v-for="(m, i) in (messages || []).slice(0, mSlice)" :key="`m-${i}`"
             :class="`__msg_wrap text-ir-deep ${editing === m.id ? '__editing' : ''}`">
          <div>
            <template v-if="!m.repeat">
              <default-avatar
                  :dark="dark"
                  :size-in="avatarSize()"
                  :bg-in="avatarColors[m.by.pid]"
                  v-if="m.pid === participantId"
                  :model-value="person"
                  :default-character="(im.participant?.name || '-').charAt(0)"
                  :use-atc-store="useAtcStore"
              ></default-avatar>
              <default-avatar
                  v-else
                  :bg-in="avatarColors[m.by.pid]"
                  :size-in="avatarSize()" :dark="dark"
                  :model-value="supportById[m.pid]"
                  :use-atc-store="useAtcStore"
              ></default-avatar>
            </template>
          </div>
          <div>
            <!--            NAME AND TIMESTAMP-->
            <div>
              <template v-if="!m.repeat">
                <div>
                  {{
                    m.by?.name
                  }}
                </div>
                <div>{{ m.timestamp }}</div>
              </template>
              <q-btn dense flat size="sm" icon="mdi-dots-vertical" v-if="m.pid === pid">
                <q-menu>
                  <div class="w300 mw100 bg-white q-pa-sm">
                    <q-list separator>
                      <q-item>
                        <q-item-section>
                          <q-item-label>Edit</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn dense flat icon="mdi-pencil-box" color="accent" @click="edit(m)"></q-btn>
                        </q-item-section>
                      </q-item>
                      <q-item>
                        <q-item-section>
                          <q-item-label>Remove</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <remove-proxy-btn dense flat icon="mdi-delete" :label="undefined" name="Message"
                                        @remove="removeMessage(m)"></remove-proxy-btn>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                </q-menu>
              </q-btn>
            </div>

            <div id="MsgBody">
              <div class="font-7-8r _fw __msg_body" v-html="m.body"></div>
            </div>
          </div>
        </div>
      </template>

    </div>
    <div class="_fw q-pa-sm">
      <div class="__input">
        <div>
          <emoji-picker no-icon @update:model-value="form.body = `${form.body || ''}${$event}`"></emoji-picker>
        </div>
        <!--        <div class="_fw">-->
        <!--          <md-editor-->
        <!--              :preview="false"-->
        <!--              language="en-US"-->
        <!--              v-model="form.body"-->
        <!--              @update:model-value="form.body = $event"-->
        <!--              :toolbars="[]"-->
        <!--              :footers="[]"-->
        <!--              @focus="editorFocus = true"-->
        <!--              @blur="editorFocus = false"-->
        <!--          ></md-editor>-->
        <!--        </div>-->
        <div class="_fw">

          <q-editor
              dense
              :toolbar="[]"
              :model-value="form.body || ''"
              @update:model-value="setBody($event)"
              @blur="setTyping(false)"
              min-height="30px"
              flat
              style="background: transparent;"
              content-style="background: transparent"
              @keyup.enter="send"
          ></q-editor>

        </div>
        <div>
          <q-btn @click="send" size="xs" dense round flat class="bg-ir-light text-ir-bg" icon="mdi-arrow-up"></q-btn>
        </div>
      </div>
      <div v-if="form.body?.length > 2000" class="q-pt-sm font-3-4r text-italic q-px-md">2000 character maximum
        exceeded
      </div>
      <div class="q-pt-sm tw-five text-ir-mid font-3-4r">
        <span v-if="typing.length">{{ typing.join(', ') }} is typing...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import EmojiPicker from 'components/common/input/EmojiPicker.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  // import {MdEditor} from 'md-editor-v3'
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useIms} from 'stores/ims';
  import {HFind} from 'src/utils/hFind';
  import {usePpls} from 'stores/ppls';
  import {msg} from '../utils'
  import {$errNotify} from 'src/utils/global-methods';
  import {Screen, date} from 'quasar';
  import {LocalStorage} from 'symbol-auth-client';
  import {_set} from 'symbol-syntax-utils';

  const imStore = useIms();
  const pplStore = usePpls();

  const mSlice = ref(20);

  const props = defineProps({
    modelValue: { required: true },
    inputAttrs: Object,
    dark: Boolean,
    notabot: Boolean,
    pid: { required: true }
  })

  const showFull = ref();
  const avatarSize = () => Screen.lt.md ? '20px' : '30px'

  const dayAgo = (date1) => {
    const days = Math.abs(date.getDateDiff(date1, new Date(), 'days'));
    if (days < 1) return `Today at ${date.formatDate(date1, 'h:mm a')}`;
    if (days === 1) return `Yesterday at ${date.formatDate(date1, 'h:mm a')}`;
    return date.formatDate(date1, 'ddd MM/DD/YY h:mm a');
  }

  const { item: im } = idGet({
    store: imStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const avatarColors = computed(() => {
    const colors = ['purple', 'orange', 'light-blue', 'pink', 'cyan', 'light-green', 'dark-purple', 'blue']
    const obj = {};
    Object.keys(im.value?.support || {}).forEach((k, i) => {
      obj[k] = colors[i % colors.length]
    })
    return {
      [im.value.participant?.fp]: 'primary',
      [im.value.participant?.login]: 'primary',
      ...obj,
      ...{
        [props.pid]: 'accent',
      }
    }
  })

  const messages = computed(() => {
    if (!im.value?.messages?.length) return []
    const arr = [];
    for (let i = 0; i < im.value.messages.length; i++) {
      const msg = im.value.messages[i] || {}
      msg.time = new Date(msg.sentAt || undefined).getTime();
      let by = {};
      const { fp, login } = im.value.participant || {}
      if (msg.pid === participantId.value) by = { pid: login || fp, ...im.value.participant };
      else {
        for (const k in im.value.support || {}) {
          if (k === msg.pid) {
            by = { pid: k, ...im.value.support[k] };
            break;
          }
        }
      }
      msg.by = by;
      arr.push(msg)
    }
    const sorted = arr.sort((a, b) => b.time - a.time);
    sorted[0].timestamp = dayAgo(sorted[0].sentAt);
    for (let i = 1; i < sorted.length; i++) {
      const nextItem = sorted[i + 1]
      if(!nextItem) continue;
      const sameSender = nextItem.by?.pid === sorted[i].by?.pid;
      sorted[i].timestamp = dayAgo(sorted[i].sentAt)

      if (sameSender && Math.abs(nextItem.time - sorted[i].time) <= (1000 * 60 * 10)) {
        sorted[i].repeat = true
      }
    }
    return sorted;
  })

  const { item: person } = idGet({
    store: pplStore,
    value: computed(() => im.value?.person)
  ,
    useAtcStore
  })

  const typing = computed(() => {
    const arr = [];
    const support = im.value.support || {};
    for (const id of im.value.typing || []) {
      if (id === props.pid) continue;
      if (support[id]) arr.push(pplStore.getFromStore(id).value?.name)
      else arr.push(im.value.participant?.name || '')
      arr.push()
    }
    return arr.filter(a => !!a);
  })

  const editing = ref(undefined);
  const form = ref(msg({ pid: props.pid }))

  const setBody = (val) => {
    form.value.body = val;
    setTyping(true)
  }

  const edit = (m) => {
    editing.value = m.id;
    form.value = { ...m }
  }

  const { h$: p$ } = HFind({
    store: pplStore,
    params: computed(() => {
      return {
        query: { _id: { $in: Object.keys(im.value?.support || {}) } },
        _search: true,
      }
    })
  })

  const removeMessage = async (m) => {
    await imStore.patch(im.value._id, { $pull: { messages: { id: m.id } } })
        .catch(err => $errNotify(`Error removing message: ${err.message}`))
  }
  const supportById = computed(() => {
    const obj = {}
    for (const k in im.value?.support || {}) {
      obj[k] = pplStore.getFromStore(k)?.value || {}
    }
    return obj;
  })

  const participantId = computed(() => {
    const { login: pl, fp: pfp } = im.value.participant || {}
    return pl || pfp || ''
  })
  const isParticipant = computed(() => {
    const { login: pl, fp: pfp } = im.value.participant || {}
    return [pl, pfp].includes(props.pid)
  })

  const sendDb = ref()
  const send = async () => {
    if (sendDb.value) clearTimeout(sendDb.value);
    sendDb.value = setTimeout(async () => {
      if (!props.notabot) return $errNotify('Please verify you are human')
      else if (!form.value.body) return $errNotify('Enter a message')
      else if (form.value.body.length > 2000) return $errNotify('Message too long')
      else if (editing.value) {
        await imStore.patch(im.value._id, { $set: { 'messages.$.body': form.value.body } }, { query: { 'messages.id': editing.value } })
            .catch(err => $errNotify(`Error editing message: ${err.message}`))
        editing.value = undefined;
        form.value = msg({ pid: props.pid });
      } else {
        form.value.pid = props.pid;
        if (!isParticipant.value && !(im.value.support || {})[props.pid]) {
          let person = pplStore.getFromStore(props.pid).value;
          if (!person) person = await pplStore.get(props.pid);
          const support = {
            login: person.login,
            fp: LocalStorage.getItem('fpId'),
            name: person.name,
            email: person.email,
            phone: person.phone?.number.e164,
            sendTo: 'in-app',
            lastAt: new Date(),
            status: 0
          }
          imStore.patchInStore(im.value._id, { support: { ...im.value.support, [props.pid]: support } })
          await imStore.patch(im.value._id, { $set: { [`support.${props.pid}`]: support } })
        }

        form.value.body = form.value.body.replace('<div><br /></div>', '')
        form.value.id = `${props.pid}-${(im.value.messages || []).filter(a => a.pid === props.pid).length}`

        const data = { ...form.value }
        form.value.body = '';
        imStore.patchInStore(im.value._id, { messages: [data, ...im.value.messages || []] })
        const patched = imStore.patch(im.value._id, { new_message: data })
            .catch(err => {
              $errNotify(`Error sending message: ${err.message}`)
              console.error(`Error sending message: ${err.message}`)
              form.value.body = data.body;
              imStore.patchInStore(im.value._id, { messages: im.value.messages.slice(1) })
            })
        if (patched) form.value = msg({ pid: props.pid });
      }
    }, 200)

  }

  const more = computed(() => mSlice.value < im.value?.messages?.length);
  const stopScroll = ref(false);
  const senseScroll = (e) => {
    if (stopScroll.value || !more.value) return;
    const atBottom = Math.abs(e.target.scrollTop) + e.target.clientHeight >= (e.target.scrollHeight - 20);
    if (atBottom) {
      stopScroll.value = true
      setTimeout(() => {
        stopScroll.value = false;
      }, 500);
      mSlice.value += 20
    }
  }

  const typeTo = ref()
  const setTyping = (val) => {
    if (!props.pid) return;
    if (typeTo.value) clearTimeout(typeTo.value)
    typeTo.value = setTimeout(() => {
      if (!val) imStore.patch(im.value._id, { $pull: { typing: props.pid } })
          .catch(err => console.error(`Error removing typing: ${err.message}`))
      else imStore.patch(im.value._id, { $addToSet: { typing: props.pid } })
          .catch(err => console.error(`Error adding typing: ${err.message}`))
    }, 2000)
  }

  const checkRead = async () => {
    const patchObj = {};
    let update;
    for (let i = 0; i < (im.value.messages || []).length; i++) {
      const msg = im.value.messages[i];
      if (!msg.openedBy) {
        patchObj[`messages[${i}].openedBy`] = [props.pid];
        update = true
      } else if (!msg.openedBy.includes(props.pid)) {
        patchObj[`messages[${i}].openedBy`] = [...msg.openedBy, props.pid];
        update = true;
      }
    }
    if (update) {
      let inStorePatch = { messages: [...im.value.messages] }
      for (const k in patchObj) {
        inStorePatch = _set(patchObj, k, patchObj[k])
      }
      imStore.patchInStore(im.value._id, { messages: inStorePatch.messages })
      imStore.patch(im.value._id, { $set: patchObj })
    }
  }

  watch(messages, (nv, ov) => {
    if (nv && nv.length && nv.length !== ov?.length) checkRead()
  }, { immediate: true })

  onMounted(() => {
    setTimeout(() => {
      imStore.patch(im.value._id, { views: [{ id: props.pid, at: new Date() }, ...im.value.views || []].slice(0, 50) })
    }, 2000)
  })
</script>

<style lang="scss" scoped>

  .__chat_window {
    height: 100%;
    display: grid;
    grid-template-rows: 1fr auto;

    > div {
      &:first-child {
        display: flex;
        flex-direction: column-reverse; /* New messages appear at the bottom */
        height: 100%; /* Adjust based on your needs */
        overflow-y: scroll;
      }
    }
  }

  .__input {
    width: 100%;
    border-radius: 20px;
    background: var(--ir-bg2);
    padding: 0 10px;
    display: grid;
    align-items: center;
    align-content: center;
    grid-template-columns: auto 1fr auto;
  }

  .__msg_wrap {
    padding: 10px 0;
    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 5px;

    > div {
      &:first-child {
        padding: 5px;
        width: calc(max(20px, min(35px, 5vw)) + 10px)
      }

      &:nth-child(2) {
        width: 100%;
        display: grid;
        grid-template-rows: auto 1fr;

        > div {
          &:first-child {
            display: grid;
            grid-template-columns: auto auto auto 1fr;
            gap: 5px;
            align-items: center;
            font-weight: 600;

            > div {
              &:first-child {
                font-size: .8rem;
              }

              &:nth-child(2) {
                font-size: .65rem;
                color: var(--ir-mid);
              }
            }
          }
        }
      }
    }
  }

  .__editing {
    background: var(--ir-yellow-1);
  }

  #MsgBody {
    .__msg_body {
      height: auto;
      min-height: 2ch;
      overflow-y: scroll;
      max-height: 80vh;
    }

  }
</style>
