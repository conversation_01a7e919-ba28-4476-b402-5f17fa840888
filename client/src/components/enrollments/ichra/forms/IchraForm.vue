<template>
  <div class="_fw">

    <q-slide-transition>
      <div class="_fw" v-if="!form?.optOut">
        <div class="row q-py-md">
          <div class="col-12 col-md-6">
            <div class="row justify-between __r">
              <div class="__line"></div>
              <q-btn v-for="t in Object.keys(tabs)" :key="`t-${t}`" dense round no-caps flat
                     :class="`__btn ${tab === t ? '__ac' : ''}`"
                     @click="setTab(t)">
                <span class="alt-font">{{ tabs[t].number }}</span>
                <div class="__txt">{{ tabs[t].label }}</div>
              </q-btn>
            </div>
          </div>
        </div>

        <div class="__t alt-font flex items-center no-wrap">
          <q-btn dense round no-caps flat class="tw-six font-1r __btn __b q-mr-sm">
            <span class="alt-font">{{ tabs[tab].number }}</span>
          </q-btn>
          <div>
            {{ tabs[tab].title }}
          </div>
        </div>


        <q-tab-panels animated class="_panel" v-model="tab">
          <q-tab-panel class="_panel" name="needs">


            <div class="row">
              <div class="col-12 col-md-6 q-pa-xs">
                <q-expansion-item v-for="k in Object.keys(needs).slice(0,3)" :key="`need-${k}`" group="0" class="__c __lc" @update:model-value="active[k] = $event" hide-expand-icon>
                  <template v-slot:header>
                    <div class="_fw __er">
                      <div class="flex items-center">
                        <div class="__fl alt-font">{{ needs[k].label }}:&nbsp;</div>
                        <div class="__val alt-font">{{ needs[k].value }}</div>
                      </div>

                      <div class="flex flex-center">
                        <q-icon name="mdi-menu-down" size="20px" :class="active[k] ? '__flip' : ''"></q-icon>
                      </div>
                    </div>
                  </template>
                  <component v-bind="needs[k].attrs" emit-value :is="needs[k].component" v-model="form[k]"
                             @update:model-value="autoSave(k)"/>

                </q-expansion-item>
              </div>
              <div class="col-12 col-md-6 q-pa-xs">
                <q-expansion-item
                    v-for="k in Object.keys(needs).slice(3)" :key="`need-${k}`"
                    group="0" class="__c __lc"
                    @update:model-value="active[k] = $event" hide-expand-icon>
                  <template v-slot:header>
                    <div class="_fw __er">
                      <div class="flex items-center">
                        <div class="__fl alt-font">{{ needs[k].label }}:&nbsp;</div>
                        <div class="__val alt-font">{{ needs[k].value }}</div>
                      </div>

                      <div class="flex flex-center">
                        <q-icon name="mdi-menu-down" size="20px" :class="active[k] ? '__flip' : ''"></q-icon>
                      </div>
                    </div>
                  </template>
                  <component v-bind="needs[k].attrs" emit-value :is="needs[k].component" v-model="form[k]"
                             @update:model-value="autoSave(k)"/>

                </q-expansion-item>
              </div>

            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="household">

            <div class="__c">
              <div class="flex items-center">
                <q-radio @update:model-value="autoSave('type', 'individual')" v-model="form.type" val="individual"
                         label="Individual"></q-radio>
                <q-radio @update:model-value="autoSave('type', 'family')" v-model="form.type" val="family"
                         label="Family"></q-radio>
              </div>
              <hh-enroll-table
                  v-if="fulle?._id"
                  :coverage="c$.data[0]"
                  :can-edit="!closed"
                  :enrollment="fulle"
              ></hh-enroll-table>
            </div>

          </q-tab-panel>
          <q-tab-panel class="_panel" name="policy">
            <div class="__c">
              <policy-shop
                  :opt-out-disclosure="optOutDisclosure"
                  @opt-out="setOptOut"
                  @update:model-value="tab = 'review'"
                  v-bind="{ planCoverages: allc$.data, enrollment: fulle, reloadMarket, best, combined, premium, oop, metalsObj, modelValue: form, issuers: [...issuers, ...allCIssuers], types, market, lowest, isAffordable, marketLoading, reimbursement, contributions }"></policy-shop>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="review">
            <div class="row">
              <div class="col-12 col-md-6 q-pa-xs">
                <div class="__c">
                  <div class="__til">Your Policy</div>
                  <div v-if="!form?.policy" class="q-pa-lg text-italic">None Selected</div>
                  <div class="_fw q-pt-lg" v-else>
                    <policy-card :enrollment="fulle" :model-value="form.policy" :ichra="form"></policy-card>
                  </div>
                </div>
                <div class="__c __prm" v-if="useDeficit > 0">
                  <div class="__til">You have excess contributions</div>

                  <div class="_form_grid">
                    <div class="_form_label">⚠️</div>
                    <div class="q-pa-sm">
                      <div class="font-1r">You have more funds than you need to cover your premiums - recommend you
                        <span class="__high" @click="goToPop">redirect {{ dollarString(useDeficit, '$', 0) }} /mo</span>
                        from
                        your <span class="tw-six" @click="goToPop('benefits', 'pop')">Premium Only Plan (POP)</span> to
                        another benefit.
                      </div>
                    </div>
                  </div>
                </div>
                <div class="__c __prm" v-else>
                  <div class="__til">Adjust Your Contributions</div>
                  <div class="_form_grid">
                    <div class="_form_label">⚠️</div>
                    <div class="q-pa-sm">
                      <div class="font-1r" v-if="fulle?.cafe?.pop">Cover your premiums tax free - <span
                          class="__high"
                          @click="goToPop('benefits', 'pop')">add {{
                          dollarString((useDeficit || 0) * -1, '$', 0)
                        }}/mo</span>
                        to your <span class="tw-six" @click="goToPop('benefits', 'pop')">Premium Only Plan (POP)</span>
                        .
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6 q-pa-xs">
                <div class="__c">
                  <div class="__til">Premium Analysis</div>

                  <ichra-analysis
                      v-bind="{ enrollment: fulle, modelValue: form, best, premium, deficit, aptc, coins, reimbursement, contributions }"></ichra-analysis>

                </div>
                <div class="__c __prm">
                  <div class="__til">Opt Out</div>
                  <div class="_fw q-py-md row items-center no-wrap __wrn">
                    <div class="col-shrink q-px-md font-1-1-2r">
                      <span v-if="!isAffordable">⛔</span>
                      <span v-else>ℹ️</span>
                    </div>
                    <div class="col-shrink q-pa-sm font-1r">
                      <span v-if="!isAffordable && ((form?.aptc?.aptc || 0) > reimbursement)">Your premium tax credit exceeds your plan reimbursement. The smartest option for you is to</span><span>If for any reason you don't want to participate in this plan, you can </span>
                      <span
                          class="tw-six text-blue cursor-pointer" @click="optOutDialog = true">Opt Out</span>
                    </div>

                  </div>
                </div>
              </div>
            </div>

          </q-tab-panel>
        </q-tab-panels>

        <div class="q-py-md row justify-end">
          <q-btn @click="setTab(tabOrder[tabIdx-1])" v-if="tabIdx > 0" flat no-caps class="tw-six">
            <q-icon name="mdi-chevron-left" color="primary" class="q-mr-sm"></q-icon>
            <span>{{ tabs[tabOrder[tabIdx - 1]].label }}</span>
          </q-btn>
          <q-btn @click="setTab(tabOrder[tabIdx+1])" class="tw-six q-ml-sm" no-caps flat v-if="tabIdx < 3">
            <span>{{ tabs[tabOrder[tabIdx + 1]].label }}</span>
            <q-icon class="q-ml-sm" color="primary" name="mdi-chevron-right"></q-icon>
          </q-btn>
          <q-btn v-else no-caps class="_p_btn" @click="finish">
            <span class="tw-six">Finish</span>
            <q-icon color="white" name="mdi-flag-checkered" class="q-ml-sm"></q-icon>
          </q-btn>
        </div>
      </div>
    </q-slide-transition>

    <div v-if="form.optOut" class="q-pa-lg">
      <div class="text-blue text-italic font-1r q-py-sm">You have opted out of this plan</div>
      <div v-if="!enrollmentClosed(fulle)" class="flex items-center">
        <div>Made a mistake? This enrollment is still open.</div>
        <q-chip color="blue" dark class="tw-six q-px-sm" clickable @click="setOptOut(false)">
          Opt Back In
          <q-icon class="q-ml-sm" name="mdi-checkbox-marked"></q-icon>
        </q-chip>
      </div>
    </div>

    <common-dialog v-model="optOutDialog">
      <div class="q-pa-lg bg-white">
        <div class="tw-six font-1r q-py-sm">Opt Out</div>
        <div class="font-1r" v-html="optOutDisclosure"></div>
        <div class="q-pa-sm bg-grey-1 br5 _fw">
          <q-checkbox
              @update:model-value="setOptOut"
              :model-value="!!form.optOut"
              label="I understand and agree to opt out"
          ></q-checkbox>
        </div>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import HhEnrollTable from 'components/enrollments/forms/HhEnrollTable.vue';
  import MedsPick from 'components/enrollments/ichra/forms/needs/MedsPick.vue';
  import ProceduresPick from 'components/enrollments/ichra/forms/needs/ProceduresPick.vue';
  import ConditionsPick from 'components/enrollments/ichra/forms/needs/ConditionsPick.vue';
  import SpendPicker from 'components/enrollments/ichra/forms/needs/SpendPicker.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import IchraAnalysis from 'components/enrollments/ichra/forms/IchraAnalysis.vue';
  import DocPicker from 'components/enrollments/ichra/forms/needs/DocPicker.vue';
  import PolicyShop from 'components/enrollments/ichra/forms/PolicyShop.vue';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';


  import {idGet} from 'src/utils/id-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import {dollarString} from 'src/utils/global-methods';
  import {HFind} from 'src/utils/hFind';
  import {useCoverages} from 'stores/coverages';
  import {usePlans} from 'stores/plans';
  import {useRoute, useRouter} from 'vue-router';
  import {marketPlace} from 'components/enrollments/ichra/utils';
  import {enrollmentClosed} from 'components/enrollments/utils';
  import {ichraForm} from 'components/coverages/ichra/utils/ichra-form';
  import {pointToGeo} from 'src/utils/geo-utils';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';
  const junkStore = useJunkDrawers();

  const route = useRoute();
  const router = useRouter();

  const enrollmentStore = useEnrollments();
  const coverageStore = useCoverages();
  const planStore = usePlans();

  const props = defineProps({
    modelValue: { required: false },
    enrollment: { required: true }
  })

  const tab = ref('needs');

  const setTab = (val) => {
    tab.value = val;
    const { href } = router.resolve({ ...route, params: { ...route.params, tab: val } })
    window.history.pushState({}, '', href);
  }

  const { item: fulle } = idGet({
    store: enrollmentStore,
    value: computed(() => props.enrollment),
    params: ref({ runJoin: { enrollment_person: true } }),
    refreshWhen: computed(() => !props.enrollment?._fastjoin?.person),
  })

  const { form, ichra, autoSave, coverageId, unset } = ichraForm(fulle)


  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => fulle.value?.plan),

    useAtcStore
  })

  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: ref(1),
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(fulle.value?.coverages || {}) },
          ichra: true
        }
      }
    })
  })

  const person = computed(() => fulle.value?._fastjoin?.person);


  const optOutDialog = ref(false);
  const optOutDisclosure = computed(() => `You are opting out of this Individual Coverage Reimbursement Plan. This means you are waiving all current and future reimbursements for your individual coverage.<br><br>${isAffordable.value ? '' : `This coverage is not affordable for you under the Affordable Care Act and your income of <b>${dollarString(ichra.value?.aptc?.income || 0, '$', 0)}</b>. You attest this is accurate and therefore you may still claim your available marketplace premium tax credits.<br><br>`}You can still direct your plan contributions toward other available plan benefits, just not reimbursement for individual coverage.`)
  const setOptOut = (val) => {
    if (val) {
      form.value.optOut = new Date();
      form.value.optOutDisclosure = optOutDisclosure.value;
      autoSave(['optOut', 'optOutDisclosure'])
    } else {
      unset(['optOut', 'optOutDisclosure'], '')
    }
    setTimeout(() => optOutDialog.value = false, 1000)
  }

  const closed = computed(() => {
    if (!fulle.value) return false;
    else return new Date().getTime() >= new Date(fulle.value.close).getTime()
  })


  const active = ref({
    annualSpend: false
  });


  const cvg = computed(() => (c$.data || [])[0]);

  const {
    reloadMarket,
    setMarket,
    premium,
    isAffordable,
    contributions,
    reimbursement,
    best,
    deficit,
    combined,
    aptc,
    oop,
    metalsObj,
    marketLoading,
    issuers,
    types,
    market,
    lowest
  } = marketPlace({ enrollment: fulle, coverage: cvg, plan, person })



  onMounted(() => {
    if (route.params.tab) tab.value = route.params.tab;
  })

  const useDeficit = computed(() => {
    if (form.value?.policy?.premium) return (reimbursement.value || 0) - form.value.policy.premium;
    else return deficit.value
  })

  const zipData = ref({});
  const getZip = async () => {
    const zip = fulle.value.address.postal;
    const d = await junkStore.find({ itemId: `zips|${zip.substring(0, 3)}` })
    const getObj = (z, tries = 0) => {
      const obj = d.data[0].data[z]
      if (obj && typeof obj === 'object') zipData.value = obj;
      else if (tries < 50) getObj(Number(z) + 1, tries + 1)
    }
    if (d) getObj(zip)
  }

  const limit = ref(25);
  const { h$:allc$ } = HFind({
    store: coverageStore,
    limit,
    params: computed(() => {
      const $or = [{ geo: { $exists: false } }]
      if (zipData.value.lngLat) $or.push({ ['geo.geometry']: { $geoIntersects: { $geometry: pointToGeo(zipData.value.lngLat)?.geometry } } })
      return {
        query: {
          $sort: { fortyPremium: 1 },
          covered: 'individual',
          type: { $in: Object.keys(fulle.value.coverages || {}).some(a => fulle.value.coverages[a].shop) ? ['hs', 'mm'] : ['mm'] },
          $and: [
            { $or },
            {
              $or: [
                { public: true },
                {
                  _id: { $in: Object.keys(plan.value.coverages || {}) }
                }
              ]
            }
          ],
        }
      }
    })
  })

  const { byId:uploadById } = manageFindUploads({ sources: allc$, paths: ['carrierLogo']})

  const allCIssuers = computed(() => {
    const byName = {};
    for(let i = 0; i < allc$.data.length; i++){
      const d = allc$.data[i];
      if(d.carrierName) {
        if (!byName[d.carrierName]) byName[d.carrierName] = { count: 1, value: d.carrierName, logo: d.carrierLogo?.url || uploadById.value.bySource[d._id]?.url}
        else byName[d.carrierName].count++;
      }
    }
    return Object.keys(byName).map(a => byName[a])
  })


  watch(fulle, async (nv) => {
    if (nv?._id) {
      setTimeout(() => {
        setMarket()
      }, 2000)
      if (nv.address && !zipData.value.lngLat) getZip()

    }
  }, { immediate: true })

  const needs = computed(() => {
    const { annualSpend, rxSpend, meds, procedures, conditions, practitioners } = form.value || { annualSpend: 0 }
    return {
      'annualSpend': {
        description: 'Total annual medical spend (including Meds)',
        label: 'Annual Medical Expenses',
        value: annualSpend ? dollarString(annualSpend, '$', 0) : '$0',
        component: SpendPicker
      },
      'rxSpend': {
        description: 'Annual spend on prescription drugs',
        label: 'Annual Drug Costs',
        value: rxSpend ? dollarString(rxSpend, '$', 0) : '$0',
        component: SpendPicker
      },
      'meds': {
        description: 'Meds you need to have covered',
        label: 'Priority Drugs 💊',
        value: meds?.length || 0,
        component: MedsPick
      },
      'practitioners': {
        description: 'Add your doctor',
        label: 'Doctors',
        value: practitioners?.length || 0,
        component: DocPicker,
        attrs: {}
      },
      'procedures': {
        description: 'Procedures you plan to have',
        label: 'Planned Procedures 🩻',
        value: procedures?.length || 0,
        component: ProceduresPick,
        attrs: {
          nameOnly: true
        }
      },
      'conditions': {
        description: 'Conditions that are or will require treatment',
        label: 'Notable Conditions 🌡️',
        value: conditions?.length || 0,
        component: ConditionsPick
      }
    }
  })

  const tabOrder = ref(['needs', 'household', 'policy', 'review']);

  const tabIdx = computed(() => tabOrder.value.indexOf(tab.value));
  const tabs = ref({
    'needs': {
      label: 'Needs',
      title: 'Start with what you expect to cover (besides the unexpected)',
      number: 1
    },
    'household': {
      label: 'Household',
      title: 'View household data and double check who\'s covered',
      number: 2
    },
    'policy': {
      label: 'Policies',
      title: 'Filter results - pick the best policy',
      number: 3
    },
    'review': {
      label: 'Review',
      title: 'Review and submit',
      number: 4
    }
  })

  const goToPop = (tab, subTab) => {
    const { href } = router.resolve({ name: 'enroll', params: { enrollId: fulle.value._id, tab, subTab } })
    window.open(href, '_blank');
  }

  const finish = () => {
    enrollmentStore.patch(fulle.value._id, { $set: { [`coverages.${coverageId.value}.status`]: 2 } });
  }
</script>

<style lang="scss" scoped>


  .__c {
    padding: 20px 2vw;
    border-radius: 20px;
    background: white;
    box-shadow: 0 2px 8px -5px #666;
    margin: 10px 2px;
    position: relative;
  }


  .__prm {
    position: relative;
    padding-top: 40px;
    margin: 20px 2px;
  }

  .__til {
    position: absolute;
    top: 0;
    left: 30px;
    transform: translateY(-30%);
    border-radius: 5px;
    background: linear-gradient(0deg, var(--q-p6), var(--q-primary));
    color: white;
    font-weight: 600;
    padding: 5px 10px;
    font-size: 1rem;
  }

  .__lc {
    padding: 10px 2vw;
    margin: 10px 0;
  }

  .__er {
    display: grid;
    grid-template-columns: auto 20px;
  }

  .__s_hov {

    &:hover {
      background: var(--q-p0);
    }
  }

  .__tabs {
    background: linear-gradient(3deg, var(--q-p1), white);
    border-radius: 12px;
    //color: white;
    //background: var(--q-p9);
  }

  .__t {
    font-size: 1rem;
    padding: 10px 5px 20px 5px;
    font-weight: 600;
    color: #666;
    //width: 95%;
  }

  .__icon {
    padding: 10px 15px;
    transform: none;
    transition: transform .2s;
  }

  .__flip {
    transform: rotate(180deg);
  }

  .__r {
    position: relative;
    padding: 0 15px;
    margin-bottom: 30px;
  }

  .__btn {
    z-index: 2;
    border: solid 4px var(--q-primary);
    background: white;
    position: relative;
    font-weight: 600;
    color: #666;

    .__txt {
      position: absolute;
      bottom: 0;
      left: 0;
      transform: translate(-25%, 30px);
      font-size: .8rem;
      font-weight: 600;
      color: #999;
    }
  }

  .__b {
    border: solid 4px #D6D6D6;
  }


  .__ac {
    background: var(--q-primary);
    color: white;
  }

  .__line {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    right: 20px;
    height: 4px;
    background: var(--q-p2);
  }

  .__fl {
    font-weight: 600;
    padding: 10px 3px;
    //color: var(--q-primary);
  }

  .__tg {
    position: relative;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 6px -2px #999;
    padding: 10px 15px;
    margin: 5px;
    transition: all .2s;
    cursor: pointer;
    font-size: .9rem;

    &:hover {
      box-shadow: 0 3px 8px -2px #999;
      transform: translateY(-2px);
    }

    span:first-child {
      font-weight: 500;
      color: #666;
    }
  }

  .__in {
    position: absolute;
    bottom: -3px;
    left: 0;
    right: 100%;
    height: 3px;
    background: var(--q-p3);
    transition: all .2s;
  }

  .__on {
    right: 0;
    //background: var(--q-p0);
    //font-weight: 600;
  }

  .__val {
    font-weight: 600;
    color: var(--q-primary);
    font-size: 1.1rem;
  }

  .__title {
    font-weight: 600;
    font-size: 1rem;
    color: #666;
  }

  .__high {
    font-weight: 600;
    color: #2196F3;
    cursor: pointer;
  }


  //.__wrn {
  //  border-radius: 8px;
  //  border: solid 2px var(--q-secondary);
  //}
</style>
