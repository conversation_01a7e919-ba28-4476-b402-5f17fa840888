<template>
  <div class="_fw">
    <div class="q-pa-sm font-1r">Find group members who have no compensation setup (and therefore cannot enroll)</div>

    <q-input v-model="search" placeholder="Search..." class="q-my-sm">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <q-table
        flat
        :columns="columns"
        :rows="no_cams || []"
        hide-bottom
        hide-pagination
    >
      <template v-slot:top v-if="loading">
        <div class="flex items-center">
          <q-spinner color="primary" size="30px"></q-spinner>
          <q-chip color="transparent" label="processing..."></q-chip>
        </div>
      </template>
      <template v-slot:header="scope">
        <!--        <q-th auto-width></q-th>-->
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope">
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component
                v-if="col.component"
                :is="col.component"
                v-bind="col.attrs(scope.row)"
            ></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div class="row justify-end q-py-sm">
      <q-pagination
          @update:model-value="toPage($event)"
          :model-value="currentPage"
          :min="1"
          :max="Math.max(1, Math.ceil(total / limit))"
          direction-links
          boundary-numbers
      ></q-pagination>
    </div>

  </div>
</template>

<script setup>
  import TdChip from 'components/common/tables/TdChip.vue';
  import MemberEditButton from 'components/groups/utils/MemberEditButton.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {computed, ref, watch} from 'vue';
  import {usePlans} from 'stores/plans';
  import {_get} from 'symbol-syntax-utils';
  import {useAtcStore} from 'src/stores/atc-store';

  const planStore = usePlans();

  const props = defineProps({
    plan: { required: true }
  })

  const fullPlan = computed(() => props.plan)

  const loading = ref(false);
  const loadedPlan = ref({});

  const from = ref(0);
  const to = ref(25);
  const search = ref('');
  const total = computed(() => loadedPlan.value?.no_cams?.length || 0);
  const limit = ref(25);

  const toPage = (val) => {
    const upper = val * limit.value;
    const lower = upper - limit.value;
    if (lower < total.value) {
      from.value = to.value;
      to.value = upper;
    }
  }

  const currentPage = computed(() => Math.ceil(to.value / limit.value))

  const no_cams = computed(() => loadedPlan.value?.no_cams?.filter(a => a.name?.toLowerCase().includes(search.value?.toLowerCase()) || a.email?.toLowerCase().includes(search.value?.toLowerCase())).slice(from.value, to.value));

  watch(fullPlan, async (nv, ov) => {
    if (nv?._id && nv._id !== ov?._id) {
      loading.value = true;
      loadedPlan.value = await planStore.get(nv._id, { runJoin: { no_cams: true } })
          .catch(() => {
            return undefined
          })
      loading.value = false;
    }
  }, { immediate: true })

  const columns = computed(() => {
    return [
      {
        label: 'Member',
        name: 'owner',
        component: DefaultChip,
        attrs: (row) => {
          return {
            modelValue: row,
            useAtcStore: useAtcStore
          }
        }
      },
      {
        name: 'email',
        label: 'Email',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row.email
            }
          }
        }
      },
      {
        name: 'phone',
        label: 'Phone',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(row, 'phone.number.national')
            }
          }
        }
      },
      {
        name: 'login',
        label: 'Has Login',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: !!row?.login ? 'Yes' : 'No'
            }
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        sortable: false,
        align: 'left',
        field: a.name,
        ...a
      };
    })
  })

</script>

<style lang="scss" scoped>

</style>
