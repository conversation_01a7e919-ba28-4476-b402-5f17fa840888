<template>
  <div class="_fw">
    <template v-if="canEdit.ok">
    <div v-if="spec?.status === 'approved'">
      <div class="font-1r">These changes were approved, and enrollment adjusted.</div>
      <div class="font-1r flex items-center">
        When you're done implementing these changes, you can
        <div></div>
        <q-chip clickable @click="confirm('archived')" class="tw-six" color="grey-8" dark icon="mdi-archive" label="Archive Them"></q-chip>
      </div>
    </div>
    <div v-if="spec?.status === 'archived'">
      <div class="font-1r">These changes were approved and archived.</div>
      <div class="font-1r flex items-center">
        If you weren't done implementing them, you can
        <div></div>
        <q-chip clickable @click="confirm('approved')" class="tw-six" color="green" dark icon-right="mdi-refresh" label="Move out of archives"></q-chip>
      </div>
    </div>
    </template>
    <div class="_fw row">
      <div class="_form_grid __fg">
        <div class="_form_label">Qualifying Event</div>
        <div class="q-px-sm q-pb-sm q-pt-md">
          {{qEvents[spec.event]?.name}}
        </div>
        <div class="_form_label">Description</div>
        <div class="q-px-sm q-pb-sm q-pt-md">
          {{spec?.description}}
          <span v-if="!spec?.description" class="text-italic">No Description</span>
        </div>
        <div class="_form_label">Message</div>
        <div class="q-px-sm q-pb-sm q-pt-md">
          <span v-if="spec?.message">{{spec.message}}</span>
          <span v-else><i>No Message</i></span>
        </div>
      </div>
    </div>
    <div v-for="(change, i) in Object.keys(changes)" :key="`change-${i}`" class="_fw">
      <template v-if="getSpec(change)">
        <div class="tw-six font-7-8r">{{ getSpec(change).name }}</div>
        <component
            :is="getSpec(change).component"
            v-bind="{ modelValue: changes[change], path: change }"
        ></component>
      </template>
      <div v-else class="q-pa-md">
        <i>Unknown change requested. Recommend you delete and try again on this one.</i>
      </div>
    </div>

    <div v-if="spec?.status === 'pending'">
      <div v-if="!pending" class="row items-center q-py-md justify-end">
        <q-btn flat no-caps class="tw-six" @click="pending = 'rejected'">
          <span>Reject</span>
          <q-icon class="q-ml-sm" color="red" name="mdi-cancel"></q-icon>
        </q-btn>
        <q-btn flat no-caps class="tw-six" @click="pending = 'approved'">
          <span>Approve</span>
          <q-icon class="q-ml-sm" color="green" name="mdi-check"></q-icon>
        </q-btn>
        '
      </div>
      <q-slide-transition>
        <div class="_fw row justify-end" v-if="!!pending && canEdit.ok">
          <div>
            <div class="tw-six text-grey-8">Confirm you want to {{ pending === 'approved' ? 'Approve' : 'Reject' }}
              these
              changes
            </div>

            <div class="row items-center q-py-md justify-end">
              <q-btn flat @click="pending = ''">
                <span>Cancel</span>
                <q-icon class="q-ml-sm" name="mdi-close" color="red"></q-icon>
              </q-btn>
              <q-btn flat @click="confirm(pending)">
                <span>Yes, {{ pending === 'approved' ? 'Approve' : 'Reject' }}</span>
                <q-icon class="q-ml-sm" name="mdi-check" color="green"></q-icon>
              </q-btn>
            </div>
          </div>
        </div>
      </q-slide-transition>
    </div>
  </div>
</template>

<script setup>

  import {useSpecs} from 'stores/specs';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {getSpec, qEvents} from 'components/enrollments/specs/utils';
  import {canU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {$errNotify} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const { login } = loginPerson()

  const specStore = useSpecs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: spec } = idGet({
    store: specStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const pending = ref('');

  const changes = computed(() => {
    return spec.value?.changes || {};
  })

  const canEdit = ref({ ok: false });
  watch(() => props.modelValue, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const requiredCapabilities = [[`orgs:${nv.org}`, 'WRITE'], ['groups', 'WRITE'], ['orgs', 'WRITE']];
      // if (login.value?._id && (nv.members || []).includes(login.value._id)) canEdit.value = { ok: true };
      canEdit.value = await canU({ requiredCapabilities, or: true, login })
    }
  }, { immediate: true })

  const confirm = async (status) => {
    const patchObj = { $set: { status } }
    specStore.patchInStore(spec.value._id, patchObj)
    await specStore.patch(spec.value._id, patchObj)
        .catch(err => $errNotify(`Error changing status, try again: ${err.message}`));
  }
</script>

<style lang="scss" scoped>
  .__fg {
    width: 100%;
    margin: 20px 0;
    padding: 20px 10px;
    border-radius: 8px;
    border: solid 1px #999;
  }
</style>
