<template>
  <div class="_fw">
    <div class="row">
      <q-tabs no-caps dense align="left" indicator-color="primary" v-model="tab">
        <q-tab name="detail">
          <span class="font-3-4r tw-six">Details</span>
        </q-tab>
        <q-tab name="payer">
          <span class="font-3-4r tw-six">Payer</span>
        </q-tab>
        <q-tab name="method">
          <span class="font-3-4r tw-six">Payment</span>
        </q-tab>
      </q-tabs>
    </div>
    <q-tab-panels class="_panel" v-model="tab" animated>
      <q-tab-panel class="_panel" name="detail">

        <div class="_form_grid">

          <div class="_form_label">Plan</div>
          <div class="q-pa-sm">
            <q-chip color="transparent">
              <default-avatar :model-value="plan?._fastjoin?.org" :use-atc-store="useAtcStore"></default-avatar>
              <span>{{ plan?.name }}</span>
            </q-chip>
          </div>
          <div class="_form_label">Provider</div>
          <div class="q-pa-sm">
            <provider-chip :model-value="provider"></provider-chip>
          </div>
          <div class="_form_label">Household</div>
          <div class="q-pa-sm">
            <member-chip class="tw-four" color="transparent"
                         :model-value="fullVisit?.person || fullClaim?.person"></member-chip>
          </div>
          <div class="_form_label">Participant</div>
          <div class="q-pa-sm">
            <member-chip color="transparent" class="tw-four" :model-value="patientId"></member-chip>
          </div>

          <div class="_form_label">Balance</div>
          <div class="q-pa-sm">

            <div :class="`text-${balance > 0 ? 'accent' : 'secondary'} tw-six font-1r`">
              {{ dollarString((balance || 0) / 100, '$', 2) }}
            </div>
          </div>
          <div class="_form_label">Coverage</div>
          <div class="q-px-sm">
            <q-tab-panels class="_panel" :model-value="!selected?._id" animated transition-prev="jump-up"
                          transition-next="jump-down">
              <q-tab-panel class="_panel" :name="true">
                <q-list separator>
                  <q-item-label header>Choose Coverage</q-item-label>
                  <q-item v-if="c$.isPending">
                    <q-item-section avatar>
                      <q-spinner size="30px" color="primary"></q-spinner>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Loading Coverages...</q-item-label>
                    </q-item-section>
                  </q-item>
                  <coverage-item
                      v-for="(cov, i) in c$.data"
                      :key="`cov-${i}`"
                      :model-value="cov"
                      :enrollment="current"
                      clickable
                      @update:model-value="selected = cov"
                  >
                    <template v-slot:caption>
                      <q-item-label caption>{{ coverageTypes[cov.type] }}</q-item-label>
                    </template>
                    <template v-slot:side>
                      <span></span>
                    </template>
                  </coverage-item>
                </q-list>
              </q-tab-panel>
              <q-tab-panel class="_panel" :name="false">
                <coverage-item :model-value="selected" :enrollment="current">
                  <template v-slot:caption>
                    <q-item-label caption>{{ coverageTypes[selected.type] }}</q-item-label>
                  </template>
                  <template v-slot:side>
                    <q-btn icon="mdi-close" dense flat color="red" @click="selected = undefined"></q-btn>
                  </template>
                </coverage-item>

              </q-tab-panel>
            </q-tab-panels>

          </div>

          <template v-if="selected?._id">
            <div class="_form_label">Coverage Payment History</div>
            <div class="q-px-sm">
              <payment-history
                  :selected="selected"
                  :paid="paid"
                  :enrollment="current"
                  :coverage="selected"
                  :visit="fullVisit"
                  :claim="fullClaim"
              ></payment-history>
            </div>
          </template>

        </div>

        <div v-if="selected?._id" class="q-py-sm row justify-end">
          <q-btn no-caps flat @click="tab = 'payer'">
            <span class="q-mr-sm tw-six">Payer Splits</span>
            <q-icon color="primary" name="mdi-chevron-right"></q-icon>
          </q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="payer">

        <pay-split
            v-bind="{
          paid,
          selected,
          payer,
          claim:fullClaim,
          visit,
          byId,
          coverage:selected,
          hasBeenAdj,
          balance,
          claims,
          amount,
          payToday,
          splits,
          discounts
            }"
            @update:claims="claims.data = $event"
            @update:by-id="byId = $event"
        ></pay-split>
        <div class="q-py-sm row justify-end" v-if="selected">
          <q-btn no-caps flat @click="tab = 'method'">
            <span class="q-mr-sm tw-six">Payment Method</span>
            <q-icon color="primary" name="mdi-chevron-right"></q-icon>
          </q-btn>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="method">

        <choose-method
            v-bind="{
          payer,
          byId,
          claim,
          visit:fullVisit,
          enrollmentId:current?._id,
          coverageId:coverage?._id,
          splits,
          discounts,
          payToday,
          balance
            }"
            @update:ca="setCareAccounts"
        ></choose-method>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ChooseMethod from 'components/claim-payments/forms/ChooseMethod.vue';
  import PaySplit from 'components/claim-payments/forms/PaySplit.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import CoverageItem from 'components/coverages/cards/CoverageItem.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import PaymentHistory from 'components/coverages/cards/PaymentHistory.vue';

  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';
  import {computed, ref, watch} from 'vue';
  import {useClaims} from 'stores/claims';
  import {usePlans} from 'stores/plans';
  import {useClaimPayments} from 'stores/claimPayments';
  import {useProviders} from 'stores/providers';
  import {dollarString} from 'src/utils/global-methods';
  import {useVisits} from 'stores/visits';
  import {types as coverageTypes} from 'components/coverages/utils/types';
  import {HFind} from 'src/utils/hFind';
  import {useEnrollments} from 'stores/enrollments';
  import {useCoverages} from 'stores/coverages';
  import {lineItems, paySplits} from 'components/claim-payments/utils/pay-claims';

  const claimStore = useClaims();
  const cpStore = useClaimPayments();
  const planStore = usePlans();
  const pStore = useProviders()
  const visitStore = useVisits();
  const enrollmentStore = useEnrollments();
  const coverageStore = useCoverages();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    claim: { required: false },
    visit: { required: false },
    payer: { required: true }
  })

  const tab = ref('detail')

  const { item: fullClaim } = idGet({
    store: claimStore,
    value: computed(() => props.claim || {,
    useAtcStore
  }),
  })
  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit || fullClaim.value || {,
    useAtcStore
  })
  })
  const { item: provider } = idGet({
    store: pStore,
    params: ref({ runJoin: { provider_avatar: true } }),
    value: computed(() => fullClaim.value?.provider || fullVisit.value?.provider)
  })
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => fullClaim.value?.plan || fullVisit.value?.plan || {,
    useAtcStore
  }),
    refreshOn: (val) => !val || !val._fastjoin?.org,
    params: ref({ runJoin: { plan_org: true } })
  })

  const { byId, total:payToday, hasBeenAdj, claims, balance, amount } = lineItems(fullVisit, fullClaim);
  const coverage = ref(undefined);
  const patientId = computed(() => fullClaim.value?.patient || fullVisit.value?.patient);
  const preventive = computed(() => fullClaim.value?.preventive || fullVisit.value?.preventive);


  const providerCareAccount = ref(undefined);
  const method = ref('');

  const addPayment = async (val) => {
    const { claim, visit, plan, person, patient, provider } = val;
    return await cpStore.create({
      claim,
      visit,
      plan,
      org: plan.value.org,
      person,
      patient,
      provider,
      amount: amount.value
    })
  }

  const warnDialog = ref(false);
  const save = async (override) => {
    if (!override && balance.value !== amount.value) {
      warnDialog.value = true;
    } else {

      const addClaim = async (c) => {
        const { _id, visit, plan, person, patient, provider } = c;
        await addPayment({
          claim: _id,
          visit,
          plan,
          person,
          patient,
          provider
        })
      }
      if (fullVisit.value?._id) {

      } else addPayment
    }
  }

  const setCareAccounts = (pca) => {
    providerCareAccount.value = pca._id;
    method.value = 'ca'
  }

  const { h$: e$ } = HFind({
    store: enrollmentStore,
    limit: ref(2),
    params: computed(() => {
      const q = {
        $sort: { version: -1 },
        plan: plan.value?._id,
        // planYear: planYear.value,
        person: fullClaim.value?.person || fullVisit.value?.person
      };
      // if (statusFilter.value?.length) q.status = { $in: statusFilter.value }
      return {
        query: q
      }
    })
  })

  const current = computed(() => e$.data ? e$.data[0] : undefined);

  const coverageIds = computed(() => Object.keys(current.value?.coverages || {}).filter(a => current.value?.coverages[a].participants?.includes(patientId.value)));
  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: computed(() => coverageIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: coverageIds.value }
        }
      }
    })
  })

  const cov = computed(() => props.coverage)
  const { selected, splits, discounts, paid } = paySplits({
    patientId,
    enrollment:current,
    preventive,
    balance: payToday,
    coverage: cov,
    /** Join the appropriate coinsurance or copays */
    claims: computed(() => {
      const { coins, coinsurance, copays } = props.coverage || {}
      return (claims.value.data || []).map(a => {
        const category = a.category || '*'
        if(!a.category || a.category === category){
          a._fastjoin = { ...a._fastjoin, coinsurance_amount: coinsurance.amount || 0 }
        } else {
          for(const k in coins || {}){
            if(coins[k].category === category){
              a._fastjoin = { ...a._fastjoin, coinsurance_amount: coins[k].amount || 0 }
              break;
            }
          }
        }
        for(const k in copays || {}){
          if(copays[k].category === category){
            a._fastjoin = { ...a._fastjoin, copay_amount: coins[k].amount || 0 }
            break;
          }
        }
      })
    })
  })

  watch(hasBeenAdj, async (nv) => {
    if(nv?.coverage && !selected.value){
      const c = coverageStore.getFromStore(nv.coverage);
      if(c.value) selected.value = c.value;
      else selected.value = await coverageStore.get(nv.coverage);
    }
  }, { immediate: true })

  watch(fullVisit, (nv) => {
    if(nv) {
      if (nv._id && nv.balanceSyncedAt && new Date().getTime() - new Date(nv.balanceSyncedAt).getTime() > (1000 * 60)) {
        visitStore.patch(nv._id, { balanceSyncedAt: new Date() });
      }
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
