<template>
  <q-chip v-bind="{clickable: true, color: 'transparent', ...$attrs}" @click="dialog = true">
    <q-avatar>
      <div class="_fa flex flex-center">
        <div class="tw-six alt-font">{{ claim?.payments?.length || 0 }}</div>
      </div>
    </q-avatar>
    <span>{{ $pluralExtension('Payment', claim?.payments || []) }}</span>

    <common-dialog setting="smmd" v-model="dialog">
      <div class="_fw q-pa-md bg-white">
        <claim-payment-manager :model-value="claim"></claim-payment-manager>
      </div>
    </common-dialog>
  </q-chip>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import ClaimPaymentManager from 'components/claim-payments/forms/ClaimPaymentManager.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useClaims} from 'stores/claims';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {$pluralExtension} from 'src/utils/global-methods';

  const claimStore = useClaims();

  const props = defineProps({
    modelValue: { required: true }
  })

  const dialog = ref(false);

  const { item: claim } = idGet({
    store: claimStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
