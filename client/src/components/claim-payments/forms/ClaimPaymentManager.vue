<template>
  <div class="_fw">
    <q-tab-panels class="_panel" :model-value="!selected" animated>
      <q-tab-panel :name="true" class="_panel">
        <div v-if="!paymentLength" class="q-pa-sm text-italic font-7-8r">No Payments</div>
        <template v-else>
          <q-item-label header>Claim Payments</q-item-label>
          <claim-payment-table :claim="claim" @select="selected = $event"></claim-payment-table>
        </template>
      </q-tab-panel>
      <q-tab-panel :name="false" class="_panel">
        <div class="row justify-end">
          <q-btn dense flat color="red" icon="mdi-close" @click="selected = undefined"></q-btn>
        </div>
        <cp-card :model-value="selected"></cp-card>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import ClaimPaymentTable from 'components/claim-payments/lists/ClaimPaymentTable.vue';
  import CpCard from 'components/claim-payments/cards/CpCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useClaims} from 'stores/claims';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';

  const claimStore = useClaims();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: claim } = idGet({
    store: claimStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })
  const paymentLength = computed(() => claim.value?.payments?.length)
  const selected = ref()
</script>

<style lang="scss" scoped>

</style>
