<template>
  <div class="_fw q-py-sm">
    <template v-if="coverage">
      <q-tab-panels class="_panel" :model-value="isAdj" animated transition-prev="slide-down"
                    transition-next="slide-up">
        <q-tab-panel class="_panel" :name="true">

          <div class="row q-py-sm justify-end">
            <q-btn dense flat icon="mdi-close" color="red" @click="adj=false"></q-btn>
          </div>
          <claim-adj
              v-bind="{
            byId,
            paid,
            selected,
            coverage,
            enrollment,
            claim:fullClaim,
            plan,
            visit:fullVisit,
            hasBeenAdj,
            amount,
            balance,
            splits,
            discounts,
            payToday
              }"
              @update:claims="updateClaims"
              @update:by-id="setById"
          ></claim-adj>

        </q-tab-panel>
        <q-tab-panel class="_panel" :name="false">
          <q-chip color="transparent" :clickable="canEdit.ok" @click="adj = !adj">
            <span class="font-3-4r">{{hasBeenAdj?.adjAt ? `Reviewed ${$ago(hasBeenAdj.adjAt)}` : 'Not Yet Reviewed'}}</span>
            <q-icon v-if="hasBeenAdj" class="q-ml-sm" color="green" name="mdi-check-circle"></q-icon>
          </q-chip>
          <div class="_f_chip _f_l">
            Line Item(s)
          </div>
          <claims-table :visit="fullVisit" :claims="claims.data">
            <template v-slot:th-right>
              Approved
            </template>
            <template v-slot:td-right="scope">
              <td class="text-right">
                <span
                    class="tw-six text-accent font-7-8r">{{
                    dollarString((byId[scope.item._id] || 0) / 100, '$', 2)
                  }}</span>
              </td>
              <td>
                <q-icon color="green" name="mdi-check-circle" size="18px"
                        v-if="scope.item.adj?.total && scope.item.adj.total >= byId[scope.item._id]">
                  <q-tooltip class="font-7-8r">Plan admin has agreed to
                    {{ dollarString(scope.item.adj.total / 100, '$', 2) }}
                  </q-tooltip>
                </q-icon>
                <q-icon v-else color="grey-2" name="mdi-check-circle" size="18px">
                  <q-tooltip class="font-7-8r" v-if="scope.item.adj?.adjAt">Plan admin has only agreed to
                    {{ dollarString(scope.item.adj?.total / 100, '$', 2) }}
                  </q-tooltip>
                  <q-tooltip v-else class="font-7-8r">Plan admin has not reviewed claim yet</q-tooltip>
                </q-icon>
              </td>
            </template>
            <template v-slot:header-right>
              <th>
                <q-icon size="15px" name="mdi-gavel" color="grey-7"></q-icon>
                <q-tooltip class="font-7-8r">Plan has agreed to amount</q-tooltip>
              </th>
            </template>
          </claims-table>


          <div class="_f_l _f_chip">Payer Split</div>

          <div class="_fw">
            <span v-if="fullClaim?.preventive || fullVisit?.preventive" class="font-7-8r q-pa-sm">⚠️ This is marked as a preventive service - no deductible should apply</span>
            <!--          <q-checkbox label="Include Pending Transactions" v-model="includePending"></q-checkbox>-->

            <split-table
                v-bind="{
              amount,
              discounts,
              balance,
              splits,
              payToday,
              hasBeenAdj,
              plan,
              provider
                }"
            ></split-table>


          </div>
          <template v-if="payer === 'plan' && canEdit.ok">
            <div class="_f_l _f_chip">Adjudicate Claim</div>
            <div class="q-pa-sm">
              <div class="font-7-8r">You are a plan claims admin. <span class="cursor-pointer text-accent tw-six" @click="adj = true">Edit Adjudication</span>
              </div>
              <div class="font-3-4r" v-if="isAdj">Before moving on to payment, approve/reject claim amounts and payer
                split. To reject a claim, set the pay amount to 0. This indicates the plan does not intend to cover it.
              </div>
            </div>
          </template>

        </q-tab-panel>
      </q-tab-panels>

    </template>
    <template v-else>
      <div class="q-pa-md text-italic font-1r">Select valid coverage</div>
    </template>
  </div>
</template>

<script setup>
  import ClaimsTable from 'components/claims/lists/ClaimsTable.vue';
  import ClaimAdj from 'components/claims/forms/ClaimAdj.vue';
  import SplitTable from 'components/claim-payments/cards/SplitTable.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {$ago, dollarString} from 'src/utils/global-methods';
  import {useVisits} from 'stores/visits';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {useClaims} from 'stores/claims';
  import {useEnrollments} from 'stores/enrollments';
  import {useProviders} from 'stores/providers';
  const { login } = loginPerson()

  const planStore = usePlans();
  const visitStore = useVisits();
  const claimStore = useClaims();
  const enrollmentStore = useEnrollments();
  const providerStore = useProviders();

  const emit = defineEmits(['update:total', 'update:claims']);
  const props = defineProps({
    claim: { required: false },
    visit: { required: false },
    coverage: { required: false },
    enrollment: { required: false },
    paid:Number,
    selected: { required: true },
    payer: { required: true },
    byId: {
      default: () => {
        return {}
      }
    },
    claims: Object,
    balance: Number,
    amount: Number,
    payToday:Number,
    hasBeenAdj: { required: true },
    splits: {
      default: () => {
        return {}
      }
    },
    discounts: {
      default: () => {
        return {}
      }
    }
  })

  const { item: fullClaim } = idGet({
    store: claimStore,
    value: computed(() => props.claim),

    useAtcStore
  })
  const { item: fullVisit } = idGet({
    store: visitStore,
    value: computed(() => props.visit || fullClaim.value?.visit || {,
    useAtcStore
  })
  })

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => fullClaim.value?.plan || fullVisit.value?.plan)
  ,
    useAtcStore
  })
  const { item: enrollment } = idGet({
    store: enrollmentStore,
    value: computed(() => props.enrollment || fullClaim.value?.enrollment || {,
    useAtcStore
  })
  })
  const { item: provider } = idGet({
    store: providerStore,
    value: computed(() => fullClaim.value?.provider || fullVisit.value?.provider || {,
    useAtcStore
  })
  })

  const { canEdit } = clientCanU({
    subject: plan,
    or: true,
    cap_subjects: computed(() => [plan.value._id]),
    caps: computed(() => [[`plans:${plan.value?._id}`, ['claimsAdmin']]]),
    login
  })

  const setById = (path, total) => {
    const obj = {...props.byId || {}};
    obj[path] = total;
    emit('update:by-id', obj)
  }

  const adj = ref(false);
  const updateClaims = (val) => {
    emit('update:claims', val);
    adj.value = false;
  }

  const isAdj = computed(() => {
    if (props.payer === 'admin') {
      const { ok } = canEdit.value || {}
      if (ok && adj.value) return true;
      return !props.hasBeenAdj
    } else return false;
  })


</script>

<style lang="scss" scoped>

  th, td {
    border-bottom: solid .3px #999;
    padding: 5px 8px;
  }


</style>
