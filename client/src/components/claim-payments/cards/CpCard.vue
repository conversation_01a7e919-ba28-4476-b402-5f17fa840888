<template>
  <div class="_fw">
    <div class="_form_grid" v-if="cp?._id">
      <div class="_form_label">From</div>
      <div class="q-px-sm">
        <org-chip :model-value="cp._fastjoin?.org || cp.org"></org-chip>
      </div>
      <div class="_form_label">To</div>
      <div class="q-px-sm">
        <provider-chip :model-value="cp._fastjoin?.provider || cp.provider"></provider-chip>
      </div>
      <div class="_form_label">On</div>
      <div class="q-px-sm">
        {{formatDate(cp.confirmedAt || cp.createdAt, 'MM-DD-YYYY')}}
      </div>
      <div class="_form_label">Status</div>
      <div class="q-px-sm">
        <cp-status :model-value="cp.status"></cp-status>
      </div>
      <div class="_form_label">Amount</div>
      <div class="q-px-sm">
        <div class="tw-six font-1r text-primary">{{dollarString(cp.amount, '$', 2)}}</div>
      </div>
      <div class="_form_label">Balance</div>
      <div class="q-px-sm">
        <div class="tw-six font-1r text-accent">{{dollarString(balance, '$', 2)}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {useClaims} from 'stores/claims';
  import {useClaimPayments} from 'stores/claimPayments';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';

  import {formatDate} from 'src/utils/date-utils';
  import {dollarString} from 'src/utils/global-methods';
  import {getClaimPrice} from 'components/claims/utils';
  import CpStatus from 'components/claim-payments/cards/CpStatus.vue';

  const claimStore = useClaims();
  const cpStore = useClaimPayments();

  const props = defineProps({
    modelValue: { required: true}
  })

  const { item:cp } = idGet({
    store: cpStore,
    value: computed(() => props.modelValue),
    refreshOn: (val) => !val?._fastjoin?.org,
    params: ref({ runJoin: { cp_org: true, cp_provider: true, cp_plan: true }})
  })

  const { item:claim } = idGet({
    store: claimStore,
    value: computed(() => cp.value?.claim)
  ,
    useAtcStore
  })

  const balance = computed(() => getClaimPrice(claim.value).total)
</script>

<style lang="scss" scoped>

</style>
