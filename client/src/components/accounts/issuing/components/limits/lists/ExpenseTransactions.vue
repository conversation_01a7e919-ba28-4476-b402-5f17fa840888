<template>
  <div class="_fw">
    <div class="row items-center">
      <q-icon v-if="!loading" class="q-mr-sm" size="18px" color="primary" name="mdi-filter"></q-icon>
      <q-spinner color="accent" class="q-mr-sm" v-else size="20px"></q-spinner>
      <div class="q-pa-sm tw-six text-grey-7">Card Transactions</div>
    </div>

    <div class="row items-center q-py-sm">
      <div class="flex items-center">
        <q-btn dense flat icon="mdi-calendar" color="primary"></q-btn>
        <inline-date @update:model-value="reload()"
                     :input-attrs="{ label: 'From Date', borderless: true, class: '_inp', dense: true}"
                     v-model="dateFrom"></inline-date>
        <q-separator color="white" vertical class="q-mx-xs"></q-separator>
        <inline-date @update:model-value="reload()"
                     :input-attrs="{ label: 'To Date', borderless: true, class: '_inp', dense: true }"
                     v-model="dateTo"></inline-date>
      </div>
    </div>
    <q-table
        :rows-per-page-options="[0]"
        flat
        :columns="cols"
        :rows="transactions.data"
        hide-no-data
        hide-bottom
        hide-pagination
    >
      <template v-slot:header="scope">
        <q-th></q-th>
        <q-th v-for="col in scope.cols" :key="col.name" :props="scope">
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope" @dblclick="openItem(scope.row)">
          <q-td></q-td>
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component v-if="col?.component" :is="col.component" v-bind="col.attrs(scope.row, col)"
                       v-on="col.listeners ? col.listeners(scope.row) : {}"></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div v-if="!transactions?.data?.length" class="q-pa-md text-italic font-1r">No Transactions Found</div>
    <div class="row q-pa-md" v-if="transactions.has_more">
      <q-chip color="a1" class="tw-six text-accent" clickable @click="loadMore()">
        <q-spinner color="accent" v-if="loading"></q-spinner>
        <span class="q-mx-sm" v-if="!loading">Load More</span>
        <span class="q-mx-sm" v-else>Loading More...</span>
      </q-chip>
    </div>

    <common-dialog setting="smmd" :model-value="!!viewing" @update:model-value="toggleViewing">
      <transaction-card :org-id="org?._id" :care-card-id="care_card?._id" :model-value="viewing"></transaction-card>
    </common-dialog>

  </div>
</template>

<script setup>
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import TdChip from 'components/common/tables/TdChip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import TransactionCard from '../cards/TransactionCard.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {formatDate} from 'src/utils/date-utils';
  import {useBanking} from 'stores/banking';
  import {dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {useExpenses} from 'stores/expenses';

  const bankStore = useBanking();
  const expenseStore = useExpenses();

  const props = defineProps({
    card: { required: true },
    org: { required: true },
    modelValue: { required: true }
  })

  const { item:expense } = idGet({
    store: expenseStore,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { limit_cards: true } }),
    refreshOn: (val) => !val?._fastjoin?.limit_cards
  })

  const viewing = ref(undefined);
  const toggleViewing = (val) => {
    if (!val) viewing.value = undefined
  }
  const openItem = (val) => {
    if(val) viewing.value = val;
  }
  const transactions = ref({ data: [] })

  const dateFrom = ref(new Date(new Date().getTime() - (1000 * 60 * 60 * 24 * 30)));

  const dateTo = ref(undefined);

  const dateRange = computed(() => {
    const obj = {
      from_date: new Date(dateFrom.value).toISOString()
    }
    if (dateTo.value) obj.to_date = new Date(dateTo.value).toISOString();
    return obj;
  })

  const page_size = ref(25);

  const loading = ref(false);

  const query = computed(() => {
    const q = {limit_id: expense.value.ramp_limit, page_size: page_size.value, ...dateRange.value };
    if(props.card) q.card_id = props.card.id || props.card;
    return q
  })
  const reload = async () => {
      loading.value = true;
      transactions.value = await bankStore.get(expense.value.owner, {
        banking: {
          ramp: {
            method: 'transactions',
            args: ['list', query.value]
          }
        }
      })
          .catch(err => {
            console.error(`Error loading transactions: ${err.message}`)
            return { data: [] }
          })
      loading.value = false;
      return;
  }

  const loadMore = async () => {
   transactions.value = await bankStore.get(expense.value.owner, {
     banking: {
       ramp: {
         method: 'transactions',
         args: ['list', query.value, transactions.value.next]
       }
     }
   })
       .catch(err => {
         console.error(`Error loading transactions: ${err.message}`)
         return { data: [] }
       })
  }



  const cols = computed(() => {
    return [
      {
        name: 'date',
        label: 'Date',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              class: 'alt-font tw-five',
              label: formatDate(row.accounting_date, 'M/D/YY')
            }
          }
        }
      },
      {
        name: 'vendor',
        label: 'Vendor',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: row.merchant_name,
            }
          }
        }
      },
      {
        name: 'amount',
        label: 'Amount',
        component: TdChip,
        attrs: (row) => {
          const gt = row.amount > 0;
          return {
            chipAttrs: {
              color: 'white',
              class: `tw-seven ${gt ? 'text-green' : 'text-accent'}`,
              label: `${gt ? '+' : ''}${dollarString(Math.abs((row.amount || 0)) / 100, '$', 2)}`
            }
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        name: a.name || a.label,
        sortable: true,
        align: 'left',
        field: a.name,
        ...props.columnSettings,
        ...a
      };
    })
  })
</script>

<style lang="scss" scoped>

</style>
