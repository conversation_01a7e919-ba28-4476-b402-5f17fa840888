<template>
  <div class="_fw mw500">
    <q-chip color="ir-bg2" v-if="loading">
      <q-spinner class="q-mr-sm" color="primary"></q-spinner>
      <span>Checking</span>
    </q-chip>
    <template v-else>
      <default-chip v-if="modelValue" :store="pplStore" :model-value="modelValue" :use-atc-store="useAtcStore">
        <template v-slot:right>
          <q-btn class="q-ml-sm" v-if="!searching" size="xs" dense flat color="red" icon="mdi-close" @click="searching = true"></q-btn>
        </template>
      </default-chip>
      <q-slide-transition>
        <div class="_fw" v-if="searching || !modelValue">
          <q-input dense filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
            <template v-slot:append v-if="modelValue">
              <q-btn dense flat icon="mdi-close" color="red" size="sm" @click="searching = false"></q-btn>
            </template>
          </q-input>
          <q-list separator dense>
            <q-item v-for="(p, i) in p$.data" :key="`p-${i}`" clickable @click="addOwner(p)">
              <q-item-section>
                <q-item-label>{{p.name}}</q-item-label>
                <q-item-label caption>{{p.email}}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-slide-transition>
    </template>
  </div>


</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {usePpls} from 'stores/ppls';
  import {budgetTree} from 'components/accounts/issuing/components/budgets/utils/budget-tree';
  import {useAtcStore} from 'src/stores/atc-store';
  import {useBanking} from 'stores/banking';
  import {usePlans} from 'stores/plans';
  import {contextItems} from 'layouts/utils/context-items';
  import {useEnvStore} from 'stores/env';
  import {$errNotify} from 'src/utils/global-methods';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  const pplStore = usePpls();
  const bankStore = useBanking();
  const planStore = usePlans();
  const envStore = useEnvStore();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },//emits the ppls _id of the cardholder
    budget: { required: true },
  })

  const { getPlanId } = contextItems(envStore);

  const { org, ca, budget, parent } = budgetTree(computed(() => props.budget))

  const { item: plan } = idGet({
    store: planStore,
    value: getPlanId
  ,
    useAtcStore
  })

  const searching = ref(false);
  const { item: limit_owner } = idGet({
    store: pplStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const allPeople = ref(1);
  const baseQuery = computed(() => {
    if (allPeople.value) {
      const planOrgs = [...plan.value.orgs || [], plan.value.org].filter(a => !!a);
      const list = [];
      if(org.value._id) list.push(org.value._id);
      if (planOrgs.includes(org.value._id)) {
        for (const id of planOrgs) {
          if (id !== org.value._id) list.push(id);
        }
      }
      const obj = { inOrgs: { $in: list } }
      if(limit_owner.value._id) obj._id = { $ne: limit_owner.value._id };
      return obj;
    }
    const ids = [...budget.value?.members || [], ...parent.value?.members || [], ...ca.value?.members || []]
    const obj =  { _id: { $in: ids, $ne: limit_owner.value?._id } }
    if(limit_owner.value._id) obj._id.$ne = limit_owner.value._id;
    return obj;
  })


  const { search, searchQ } = HQuery({ keys: ['name', 'email'] })
  const { h$:p$ } = HFind({
    store: pplStore,
    limit: ref(10),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          ...baseQuery.value
        }
      }
    })
  })

  const loading = ref(false);
  const addOwner = async (val) => {
    if (val.ramp_user_id) {
      emit('update:model-value', val._id);
    }
    else {
      loading.value = true;
     const added_ramp = await bankStore.get(org.value._id, { banking: { ramp_ppl_id: val._id,
          ramp: {
            method: 'users',
            args: ['create', { first_name: val.firstName, last_name: val.lastName, email: val.email }]
          }
        }
      })
          .catch(err => {
            console.error(`Error creating ramp user: ${err.message}`)
            $errNotify('Error adding user as expense owner - try again')
            return undefined;
          })
      loading.value = false;
      if(added_ramp) {
        emit('update:model-value', val._id);
      }
    }
  }


</script>

<style lang="scss" scoped>

</style>
