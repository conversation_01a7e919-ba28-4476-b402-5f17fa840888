<template>
  <div class="_fw">

    <q-checkbox v-model="showCancelled" label="Show Cancelled"></q-checkbox>
    <div class="_fw">
      <data-table
          :open-item="openItem"
          pre-menu
          add-label="Add Expense"
          :columns="columns"
          :store="expenseStore"
          dialog-setting="right"
          :params="params"
          :limit="10"
          :search-attrs="{ filled: true, dense: true }"
          :editing="editing"
      >
        <template v-slot:search>
          <q-input dense filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </template>
        <template v-slot:form="scope">
          <div class="_fw bg-white">
            <expense-form
                :care-account="ca"
                @update:model-value="openItem"
                :model-value="scope.editing"
                :budget="budget"
            ></expense-form>
          </div>
        </template>
      </data-table>
    </div>

    <common-dialog setting="right" :model-value="!!preview" @update:model-value="togglePreview">
      <div class="_fw q-pa-md">
        <div class="row justify-end">
          <q-btn flat no-caps @click="setEditing">
            <span class="q-mr-sm">Edit Card</span>
            <q-icon name="mdi-pencil-box" color="accent"></q-icon>
          </q-btn>
          <q-btn flat no-caps @click="openLimit(preview)">
            <span class="q-mr-sm">Open As Page</span>
            <q-icon color="accent" name="mdi-open-in-new"></q-icon>
          </q-btn>
        </div>
        <q-separator class="q-my-md"></q-separator>
        <care-card :expense-id="preview?._id"></care-card>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import ExpenseForm from 'components/accounts/issuing/components/limits/forms/ExpenseForm.vue';
  import DataTable from 'components/common/tables/DataTable.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import CareCard from './CareCard.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useBudgets} from 'stores/budgets';
  import {useCareAccounts} from 'stores/care-accounts';
  import {usePpls} from 'stores/ppls';
  import {HQuery} from 'src/utils/hQuery';
  import {dollarString} from 'src/utils/global-methods';
  import {useRouter} from 'vue-router';
  import {useExpenses} from 'stores/expenses';

  const store = useBudgets();
  const caStore = useCareAccounts();
  const pplStore = usePpls();
  const router = useRouter();
  const expenseStore = useExpenses();

  const props = defineProps({
    budget: { required: true }
  })

  const editing = ref(undefined);
  const preview = ref(undefined);
  const togglePreview = (val) => {
    if(!val) preview.value = '';
  }
  const setEditing = () => {
    editing.value = preview.value;
    preview.value = undefined;
  }
  const openItem = (val) => {
    if(val) preview.value = val;
  }
  const openLimit = (val) => {
    const { href } = router.resolve({ name: 'limit-page', params: { limitId: val._id }})
    window.open(href, '_blank');
  }

  const { item: budget } = idGet({
    store,
    value: computed(() => props.budget)
  ,
    useAtcStore
  })

  const { item: parent } = idGet({
    store,
    value: computed(() => budget.value?.parent)
  ,
    useAtcStore
  })

  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => budget.value?.careAccount || parent.value?.careAccount)
  ,
    useAtcStore
  })

  const expenseIds = computed(() => budget.value?.expenses || [])

  const { search, searchQ } = HQuery({ keys: ['name', 'last4']})
  const showCancelled = ref(false);
  const params = computed(() => {
    const query = {
      ...searchQ.value,
      _id: { $in: expenseIds.value }
    };
    if(!showCancelled.value) query.status = { $ne: 'canceled' }
    return {
      runJoin: { limit_owner: true },
      query
    }
  })

  const columns = computed(() => {
    return [
      {
        label: 'Name',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: row.name }
          }
        }
      },
      {
        label: 'Owner',
        component: DefaultChip,
        attrs: (row) => {
          return {
            store: pplStore,
            modelValue: row._fastjoin?.limit_owner || row.limit_owner,
            chipAttrs: { color: 'transparent' }
          }
        }
      },
      {
        label: 'Status',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: (row.status || 'Not Set').toUpperCase() }
          }
        }
      },
      {
        label: 'Funds',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: dollarString((row.amount || 1)/100, '$', 2) }
          }
        }
      },
      {
        label: 'Spent',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: dollarString(((row.spent || 0) + (row.spent_pending || 0))/100, '$', 2) }
          }
        }
      }
    ]
  })

</script>

<style lang="scss" scoped>

</style>
