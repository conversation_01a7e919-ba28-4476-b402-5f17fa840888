<template>
  <div class="relative-position">
    <div v-if="card?.status === 'cancelled'" class="__cancelled flex flex-center">
      <div class="text-red-8 text-italic font-1r">Card Cancelled</div>
    </div>
    <div class="__card" v-if="idx > -1">
      <!--        TOP Section-->
      <div class="row items-start">
        <div class="col-6 q-pa-md">
          <div class="text-xxs __bg">{{ budget?.name || '' }}</div>
          <div class="text-sm tw-six" v-if="card?.state !== 'TERMINATED'">{{ dollarString((limit - spent)/100, '$', 2) }}</div>
          <div class="text-sm tw-six" v-else>Canceled</div>
          <div class="text-xxs __bg">available funds</div>
          <div class="text-xxs __bg">{{ dollarString((expense.recurs || 0)/100, '$', 2) }} Monthly</div>
        </div>
        <div class="col-6 q-pa-md">
          <div class="row justify-end">
            <q-img class="h50 w50" fit="contain" :src="logo"></q-img>
          </div>
        </div>
      </div>

      <!--        NUMBER section-->
      <div class="q-pt-lg row items-center q-px-md">
        <div class="col-9 q-pa-sm relative-position num-font">
          <div class="row items-center">
            <div class="col-10 col-md-9">
              <div :style="show ? 'height: 0; overflow: hidden;' : ''" class="__f">
                <span class="_hide">1234  5678  9101</span>&nbsp;&nbsp;{{ card?.last4 }}
              </div>
              <div v-show="show" id="card-number"></div>
            </div>
            <div id="card-number-copy">
            </div>
          </div>
        </div>
        <div class="col-3 q-pa-sm">
          <div class="row justify-end">
            <q-btn @click="show = !show" dense flat :icon="show ? 'mdi-eye-off' : 'mdi-eye'"></q-btn>
          </div>
        </div>
      </div>

      <!--        EXP/CVC Section-->
      <div class="row items-center">
        <div class="col-7 q-pa-sm">
          <div class="flex items-center">
            <div class="font-1-2r text-uppercase alt-font">
              VALID<br>THRU
            </div>
            <div class="__f q-pa-sm">
              {{ validThru }}
            </div>
          </div>
        </div>
        <div class="col-5 q-pa-sm num-font relative-position">
          <div class="row items-center relative-position">
            <div class="font-1-2r alt-font q-pa-sm">CVC</div>
            <div v-if="!show" class="__f">
              <span class="_hide">123</span>
            </div>
            <div v-show="show" id="card-cvc"></div>
            <div id="cvc-copy">
            </div>
          </div>

        </div>
      </div>
      <div class="b-r q-pa-md">
        <q-img v-if="card?.brand" style="height: 20px; width: 50px;" fit="contain" :src="brands[card?.brand]"></q-img>
        <q-spinner size="25px" v-else color="primary"></q-spinner>
      </div>
    </div>
    <div class="q-py-sm" v-if="expense._fastjoin?.limit_cards?.length > 1 || idx === -1">
      <q-list dense separator>
        <q-item-label v-if="!expense._fastjoin?.limit_cards?.length" header class="text-italic">No Cards Added</q-item-label>
        <q-item v-show="idx !== i" v-for="(c, i) in expense._fastjoin?.limit_cards || []" :key="`card-${i}`" clickable @click="setIdx(i)">
          <q-item-section>
            <q-item-label>{{ c.display_name.split('| ')[1] }}</q-item-label>
            <q-item-label caption>ending {{c.last_four}}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </div>
</template>

<script setup>
  import cc_logo from 'src/assets/commoncare_icon.svg'
  import {computed, ref, watch} from 'vue';
  import {getFile} from 'src/utils/fs-utils';
  import {dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {useExpenses} from 'stores/expenses';
  import {useAtcStore} from 'src/stores/atc-store';

  const expenseStore = useExpenses();

  const emit = defineEmits(['update:active']);
  const props = defineProps({
    modelValue: { required: true },
    org: { required: false },
    budget: { required: true },
    active: { required: false }
  })

  const { item:expense } = idGet({
    store: expenseStore,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { limit_cards: true } }),
    refreshOn: (val) => !val?._fastjoin?.limit_cards
  })

  const err = ref('');
  const idx = ref(-1)
  const setIdx = (i) => {
    idx.value = i;
    emit('update:active', i)
  }

  watch(() => props.active, (nv, ov) => {
    if (nv !== ov) idx.value = nv;
  }, { immediate: true })

  const card = computed(() => (expense.value._fastjoin?.limit_cards || [])[idx.value] || {})

  const brands = ref({
    'Visa': 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fvisa.svg?alt=media&token=8cd62502-f635-4a48-91d8-1b0d621072bb',
    'Mastercard': 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fmc.svg?alt=media&token=b3a6ddec-c0ee-436e-9e9f-0be6c97d27dd'
  })

  const limit = computed(() => expense.value?.amount || 0)
  const spent = computed(() => {
    return (expense.value?.spent || 0) + (expense.value?.spent_pending || 0)
  })

  const logo = computed(() => {
    if (props.org?.avatar) {
      const oa = getFile(null)({ obj: props.org, path: ['avatar'], type: 'image' })
      if (oa) return oa;
      else return cc_logo;
    } else return cc_logo;
  })

  const show = ref(false);

  const validThru = computed(() => {
    if (!card.value) return '00/00'
    else return card.value.expiration
  })


</script>

<style lang="scss" scoped>

  .__card {
    position: relative;
    border-radius: 20px;
    width: min(98vw, 400px);
    height: min(calc(98vw / 1.568), calc(400px / 1.568));
    //border: solid 1px black;
    //box-shadow: 0 0 30px -1px black;
    box-shadow: 0 10px 30px -8px #999;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 45% 22% 23%;
    align-items: center;
    align-content: center;
    padding: 10px 10px 30px 10px;
    background: white;
  }

  .__bg {
    font-weight: 600;
    color: #999;
  }

  .__f {
    font-size: 16px;
  }


  .__cancelled {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 18;
    border-radius: inherit;
    background: rgba(255, 255, 255, .5);
    backdrop-filter: blur(10px);
  }
</style>
