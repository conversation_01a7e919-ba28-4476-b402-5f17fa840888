<template>
  <div>
    <avatar-row :size="size" :model-value="item" :path="path" :avatar-store="pplStore" :use-atc-store="useAtcStore">
      <template v-slot:menu="scope">
        <q-menu>
        <div class="q-pa-md w150 bg-p12 text-white">
          <div class="row justify-center">
            <default-avatar dark :model-value="scope.item" :use-atc-store="useAtcStore"></default-avatar>
          </div>
          <div class="q-py-sm text-center tw-six font-7-8r">{{scope.item.name}}</div>
          <q-btn v-if="adding" dark flat no-caps @click="remove(scope.item)">
            <span class="q-mr-sm">Remove</span>
            <q-icon name="mdi-close" color="red"></q-icon>
          </q-btn>
        </div>
        </q-menu>
      </template>
      <template v-slot:right>
        <q-avatar :size="size + 'px'" @click="dialog = true" class="cursor-pointer text-white tw-six" color="p3"
                  v-if="adding || list.length > limit">
          <span v-if="list.length > limit">+{{ Math.max((list.length || 0) - limit, 0) }}</span>
          <q-icon name="mdi-plus" v-else-if="adding"></q-icon>

          <common-dialog v-model="dialog" size="smmd">
            <div class="_fw bg-white q-pa-md">
              <div class="q-pb-md q-px-md tw-six text-grey-7 font-1r">Add {{names[path]}}</div>
              <slot name="dialog">
                <q-input dense filled v-model="search.text">
                  <template v-slot:prepend>
                    <q-icon name="mdi-magnify"></q-icon>
                  </template>
                </q-input>
                <q-list separator>
                  <default-item
                      @click="handleAdd(p)"
                      v-for="p in p$.data"
                      :key="`p-${path}-${p._id}`"
                      :model-value="p"
                      :use-atc-store="useAtcStore"
                  ></default-item>
                </q-list>
              </slot>
            </div>
          </common-dialog>
        </q-avatar>
      </template>
    </avatar-row>
  </div>
</template>

<script setup>
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, onBeforeUnmount, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {HFind} from 'src/utils/hFind';
  import {useAtcStore} from 'src/stores/atc-store';
  import {HQuery} from 'src/utils/hQuery';
  import {$errNotify} from 'src/utils/global-methods';

  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    path: { required: true },
    adding: Boolean,
    size: { default: 25 },
    store: { required: true }
  })

  const dialog = ref(false);
  const { item } = idGet({
    store: props.store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const names = ref({
    'managers': 'Managers',
    'approvers': 'Approvers',
    'members': 'Members',
    'writers': 'Editors'
  })

  const list = computed(() => (item.value || {})[props.path] || [])
  const limit = computed(() => {
    return Math.min(list.value.length || 0, 10)
  })

  const { search, searchQ } = HQuery({})
  const { h$: p$ } = HFind({
    store: pplStore,
    pause: computed(() => !dialog.value || !props.adding),
    limit,
    params: computed(() => {
      const query = {
        ...searchQ.value,
      }
      if (!props.adding) query._id = { $in: list.value }
      return {
        query
      }
    })
  })

  const addList = ref([]);
  const runAdd = async () => {
    if(addList.value.length){
      await props.store.patch(item.value._id, { $addToSet: { [props.path]: { $each: addList.value } } })
          .catch(err => $errNotify(`Error adding ${props.path.slice(0, props.path.length - 1)}: ${err.message}`));
      addList.value = [];

    }
  }
  const addTo = ref();
  const handleAdd = (v) => {
    addList.value.push(v._id);
    const newList = [...item.value[props.path] || [], v._id];
    props.store.patchInStore(item.value._id, { $set: { [props.path]: newList }})
    if(addTo.value) clearTimeout(addTo.value);
    addTo.value = setTimeout(() => {
      runAdd()
    }, 2500)
  }

  const pulling = ref([]);
  const runPull = async () => {
    if(pulling.value.length){
      await props.store.patch(item.value._id, { $pull: { [props.path]: pulling.value } })
          .catch(err => $errNotify(`Error removing ${props.path.slice(0, props.path.length - 1)}: ${err.message}`));
      pulling.value = []

    }
  }
  const pullTo = ref();
  const remove = (v) => {
    pulling.value.push(v._id);
    const newList = [...item.value[props.path] || []];
    const idx = newList.indexOf(v._id);
    newList.splice(idx, 1);
    props.store.patchInStore(item.value._id, { $set: { [props.path]: newList }})
    if(pullTo.value) clearTimeout(pullTo.value);
    pullTo.value = setTimeout(() => {
      runPull()
    }, 2500)
  }

  onBeforeUnmount(() => {
    runPull()
    runAdd()
  })

</script>

<style lang="scss" scoped>

</style>
