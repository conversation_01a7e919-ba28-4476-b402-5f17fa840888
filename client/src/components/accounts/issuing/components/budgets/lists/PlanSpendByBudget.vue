<template>
  <div class="row justify-center bg-ir-bg1 mnh90">
    <div class="_sent pd6 pw2">
      <div class="row items-center">
        <div class="font-7-8r tw-six text-ir-mid q-pr-sm">Plan Year:</div>
        <q-chip color="transparent" flat :class="current ? 'tw-six text-primary' : 'text-ir-deep'" label="Current" dense
                square clickable @click="current = true"></q-chip>
        <div>|</div>
        <q-chip color="transparent" flat :class="!current ? 'tw-six text-primary' : 'text-ir-deep'" label="Upcoming"
                dense square clickable @click="current = false"></q-chip>
      </div>

      <div class="row items-center q-py-sm">
        <q-icon size="20px" name="mdi-filter" color="accent"></q-icon>
        <budget-chip picker :options="b$.data" emit-value v-model="budgetFilter"></budget-chip>
        <q-btn dense flat size="xs" color="red" icon="mdi-close" v-if="budgetFilter" @click="budgetFilter = undefined"></q-btn>
      </div>

      <div class="__table" v-for="(b, i) in b$.data" :key="`budget-${i}`">
        <table >
          <tbody>
<!--            DON'T NEED CARE ACCOUNTS HERE BC ONLY 1 PER COMPANY CURRENTLY-->
<!--            <tr>-->
<!--              <td>-->
<!--                <div class="flex items-center">-->
<!--                  <div> {{ b._fastjoin?.careAccount?.name }}</div>-->
<!--                  <q-img :src="caIcon" class="w15 h15 q-ml-sm" fit="contain"></q-img>-->
<!--                </div>-->
<!--              </td>-->
<!--            </tr>-->
            <tr class="__start">
              <td class="tw-six">
                <div class="flex items-center text-primary">
                  <div>
                    {{ b.name }}
                  </div>
                  <q-icon name="mdi-piggy-bank" color="primary" size="15px" class="q-ml-sm"></q-icon>

                </div>
              </td>
              <td></td>
            </tr>
            <tr class="text-ir-mid __bb">
              <td>Benefit</td>
              <td>Requirement</td>
            </tr>
            <tr  v-for="(ben, idx) in budgetById[b._id]" :key="`ben-${i}-${idx}`" :class="`${idx < budgetById[b._id].length -1 ? '__bb' : ''}`">
              <td v-for="(d, i) in cols" :key="`d-${i}`">
                <span>{{ d.value(b, ben, idx) }}</span>
              </td>
            </tr>
            <tr class="__total">
              <td>
                Total Requirement
              </td>
              <td>{{ dollarString(totalsById[b._id].total, '$', 2) }}</td>
            </tr>
            <tr :class="`__scheduled ${totalsById[b._id].total > (b.assigned_recurs || 0) ? '__warn' : ''}`">
              <td>
                Budget Scheduled
              </td>
              <td>{{ dollarString(b.assigned_recurs, '$', 2) }}</td>
            </tr>
            <tr class="__balance">
              <td>
                Budget Balance
              </td>
              <td>{{ dollarString(getAvailable(b), '$', 2) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>
</template>

<script setup>
  import BudgetChip from 'components/accounts/issuing/components/budgets/cards/BudgetChip.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useCareAccounts} from 'stores/care-accounts';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {useBudgets} from 'stores/budgets';

  import {$limitStr, dollarString} from 'src/utils/global-methods';
  import {useCoverages} from 'stores/coverages';
  import {cafeKeys, getCurrentPlanYear} from 'components/plans/utils';
  import {getAvailable} from 'components/accounts/issuing/components/budgets/utils';

  const caStore = useCareAccounts();

  const planStore = usePlans();
  const budgetStore = useBudgets();
  const coverageStore = useCoverages();

  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => LocalStorage.getItem('care_account_id')),
    useAtcStore
  })

  const current = ref(true);

  const budgetFilter = ref()

  const { h$: p$ } = HFind({
    store: planStore,
    limit: ref(10),
    params: computed(() => {
      const query = { org: ca.value.owner }
      return {
        query
      }
    })
  })

  /**
   * {budgetId: [planId:string, benefitPath:string]}
   */
  const budgetById = computed(() => {
    const ids = {};
    for (let i = 0; i < p$.data.length; i++) {
      for (const path of ['cafe', 'coverages']) {
        for (const k in p$.data[i][path] || {}) {
          const id = p$.data[i][path][k].budget
          if (id) {
            ids[id] = [...ids[id] || [], [p$.data[i]._id, `${path}.${k}`]];
          }
        }
      }
    }
    return ids;
  })

  const budgetIds = computed(() => budgetFilter.value ? [budgetFilter.value] : Object.keys(budgetById.value || {}))

  const { h$: b$ } = HFind({
    store: budgetStore,
    pause: computed(() => !budgetIds.value.length),
    limit: computed(() => budgetIds.value.length),
    params: computed(() => {
      return {
        runJoin: { budget_parent: true },
        query: { _id: { $in: budgetIds.value } }
      }
    })
  })

  const coverageParams = computed(() => {
    const cids = [];
    for (let i = 0; i < p$.data.length; i++) {
      for (const k in p$.data[i].coverages || {}) {
        cids.push(k)
      }
    }
    return {
      query: { _id: { $in: Array.from(new Set(cids)) } }
    }
  })
  const { h$: c$ } = HFind({
    store: coverageStore,
    limit: computed(() => coverageParams.value.query._id.$in.length),
    params: coverageParams
  })

  const contributionMap = {
    'cafe': (c, k) => (c.byPlan || {})[k],
    'coverages': (c, k) => (c.byCoverage || {})[k]?.needed
  }

  /**
   * Array of contribution objects from each relevant plan enrollment for all plans
   */
  const contributions = computed(() => {
    const conts = [];
    const inc = current.value ? 0 : 1;
    for (let i = 0; i < p$.data.length; i++) {
      let year = getCurrentPlanYear(p$.data[i]) + inc;
      const cont = (p$.data[i].enrollments || {})[`${year}_0`]?.contributions
      if (cont) conts.push(cont);
    }
    return conts;
  })

  /**
   * {
   *   id:budgetId: {
   *     list: separated by benefit [requirement, requirement, ...]
   *     total: requirement
   *   }
   * }
   */
  const totalsById = computed(() => {
    const obj = {};
    for (const k in budgetById.value) {
      obj[k] = { list: [], total: 0 };
      let allTotal = 0;
      for (const benKey of budgetById.value[k] || []) {
        const spl = benKey[1].split('.');
        let total = 0;
        for (let i = 0; i < contributions.value.length; i++) {
          total += contributionMap[spl.slice(-2)[0]](contributions.value[i], spl.slice(-1)[0]) || 0
        }
        obj[k].list.push(total)
        allTotal += total
      }
      obj[k].total = allTotal;
    }
    return obj;
  })

  const cols = computed(() => {

    return [
      {
        label: 'Benefit',
        value: (b, benKey) => {
          const plan = $limitStr(planStore.getFromStore(benKey[0])?.value?.name || '', 30, '...')
          if (benKey[1].includes('coverages')) {
            const id = benKey[1].split('.').slice(-1)[0];
            return $limitStr(coverageStore.getFromStore(id).value?.name, 20, '...') + ' | ' + plan
          } else return cafeKeys[benKey[1].split('.').slice(-1)[0]]?.shortName + ' | ' + plan
        }
      },
      {
        label: 'Requirement',
        value: (b, benKey, benIdx) => {
          return dollarString(totalsById.value[b._id].list[benIdx], '$', 2)
        }
      }
    ]
  })

</script>

<style lang="scss" scoped>

  .__table {
    width: 100%;
    overflow-x: scroll;
    padding: max(2vw, 25px) 2vw;
    margin: 10px 0;
    border-radius: 10px;
    background: white;
    box-shadow: 0 3px 10px var(--ir-light);

    table {
      width: 100%;
      border-collapse: collapse;

      tr {
        th {
          padding: 4px 8px;
          font-weight: 600;
          color: var(--ir-deep);
          text-align: left;
        }

        td {
          font-family: var(--alt-font);
          padding: 4px 8px;
          border-bottom: 2px solid var(--ir-bg);
          border-radius: 4px 0 0 4px;

          &:last-child {
            width: 60%;
            border-radius: 0 4px 4px 0;
          }
        }
      }

      .__bb {
        td {
          border-bottom: solid .3px var(--ir-mid);
        }
        &:last-child {
          td {
            border-bottom: none;
          }
        }
      }

      .__total {
        td {
          background: var(--ir-bg1);
          color: var(--ir-text);
          font-weight: 600;
        }
      }
      .__scheduled {
        td {
          background: var(--q-p0);
          color: var(--q-p6);
          font-weight: 600;
        }
      }
      .__warn {
        td {
          background: var(--q-s1) !important;
          color: var(--q-s9) !important;
        }
      }
      .__balance {
        td {
          background: var(--q-a0);
          color: var(--q-a7);
        }
      }
      .__start {
        td {
          font-size: 1.1rem;
          font-family: var(--main-font);
          //background: var(--q-p0);
        }
      }
    }
  }
</style>
