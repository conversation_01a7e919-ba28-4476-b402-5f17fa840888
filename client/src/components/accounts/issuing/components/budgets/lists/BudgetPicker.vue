<template>
  <q-select
      v-bind="{
    class: '_fw mw400',
    useInput: true,
    options: b$.data,
     modelValue,
      ...$attrs,
      multiple:false
  }"
      @input-value="search.text = $event"
      @update:model-value="emitUp">
    <template v-slot:selected-item>
      <div class="alt-font tw-five font-1r">{{ budget?.name }}</div>
    </template>
    <template v-slot:no-option>
      <div class="q-pa-md text-italic font-3-4r">No Budgets Found</div>
    </template>
    <template v-slot:before-options></template>
    <template v-slot:option="scope">
      <q-item :key="scope.opt._id" clickable @click="emitUp(scope.opt)">
        <q-item-section avatar>
          <q-icon color="accent" name="mdi-piggy-bank"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-grey-7 tw-six">{{ scope.opt.name }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
    <template v-slot:after v-if="$slots.after">
      <slot name="after"></slot>
    </template>
  </q-select>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useBudgets} from 'stores/budgets';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {useAtcStore} from 'src/stores/atc-store';

  const budgetStore = useBudgets()

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: true },
    multiple: Boolean,
    emitValue: Boolean,
    query: Object,
    params: Object,
    iconAttrs: Object
  })

  const { item: budget } = idGet({
    store: budgetStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })


  const { search, searchQ } = HQuery({ keys: ['name'] })

  const { h$: b$ } = HFind({
    store: budgetStore,
    limit: ref(5),
    params: computed(() => {
      const query = {
        parent: { $exists: false },
        ...searchQ.value,
        ...props.query
      }
      if (props.modelValue) query._id = { $ne: props.modelValue?._id || props.modelValue }
      return {
        ...props.params,
        query
      }
    })
  })


  const emitUp = (val, remove) => {
    const v = props.emitValue ? val._id : val;
    // if (props.multiple) {
    //   if (!props.modelValue?.length) emit('update:model-value', [v]);
    //   else {
    //     const list = [...props.modelValue || []]
    //     const idx = idList.value.indexOf(val._id);
    //     if (idx > -1) {
    //       list.splice(idx, 1);
    //       emit('update:model-value', list);
    //     } else {
    //       list.push(v);
    //       emit('update:model-value', list);
    //     }
    //   }
    // } else
    emit('update:model-value', remove ? undefined : v);
  }
</script>

<style lang="scss" scoped>

</style>
