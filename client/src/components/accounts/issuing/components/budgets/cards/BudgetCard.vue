<template>
  <div class="_fw">
    <div class="tw-six font-1r">{{ budget?.name }}</div>
    <div class="q-py-md">
      <div class="tw-six text-uppercase font-3-4r">
        Funds: <span class="text-primary font-1r">{{ dollarString(available, '$', 0) }}</span> &nbsp; Monthly: <span
          class="text-accent font-1r">{{ dollarString(amtPath(budget, 'recurs'), '$', 0) }}</span>
      </div>
      <div class="__meter">
        <div class="__spent" :style="{ width: percent + '%' }"></div>
        <div class="__spent_p" :style="{ width: pending + '%' }"></div>
        <div class="__assigned" :style="{ width: assigned + '%' }"></div>
      </div>

      <div class="row">
        <q-chip dense color="transparent">
          <q-avatar size="10px" color="p7"></q-avatar>
          <span class="q-ml-sm">Spent:&nbsp; <b>{{dollarString(amtPath(budget, 'spent'), '$', 0)}}</b></span>
        </q-chip>
      </div>
      <div class="row">
        <q-chip dense color="transparent">
          <q-avatar size="10px" color="p2"></q-avatar>
          <span class="q-ml-sm">Pending:&nbsp; <b>{{dollarString(amtPath(budget, 'spent_pending'), '$', 0)}}</b></span>
        </q-chip>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {useBudgets} from 'stores/budgets';
  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {amtPath, getAvailable} from 'src/components/accounts/issuing/components/budgets/utils';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useBudgets();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: budget } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const percent = computed(() => {
    const { amount = 0, spent = 0, spent_sub = 0 } = budget.value || {}
    return Math.round(((spent + spent_sub)/amount) * 100)
  })

  const pending = computed(() => {
    const { amount = 0, spent = 0, spent_pending = 0, spent_sub = 0, spent_pending_sub = 0 } = budget.value || {}
    return Math.round(((spent + spent_pending + spent_sub + spent_pending_sub)/amount) * 100)
  })

  const assigned = computed(() => {
    const { amount = 0, spent = 0, spent_pending = 0, spent_sub = 0, spent_pending_sub = 0, assigned_amount = 0 } = budget.value || {}
    return Math.round(((spent + spent_pending + spent_sub + spent_pending_sub + assigned_amount)/amount) * 100)
  })

  const available = computed(() => getAvailable(budget.value))

</script>

<style lang="scss" scoped>
  .__meter {
    background: var(--q-p3);
    width: 100%;
    margin: 3px 0;
    border: solid 1px black;
    border-radius: 4px;
    position: relative;
    height: 20px;
    overflow: hidden;
  }

  .__spent {
    position: absolute;
    z-index: 4;
    top: 0;
    bottom: 0;
    left: 0;
    background: var(--q-p7);
    max-width: 100%;
  }

  .__spent_p {
    position: absolute;
    //border-top: dotted 2px white;
    //border-bottom: dotted 2px white;
    z-index: 3;
    top: 0;
    bottom: 0;
    left: 0;
    //background: var(--q-p7);
    max-width: 100%;
    height: 80px;
  }
  .__pend, .__spent_p {
    background: repeating-linear-gradient(
            45deg,
            black 0px,
            black 2px,
            var(--q-primary) 2px,
            var(--q-primary) 9px
    );
  }
  .__assigned {
    //border-top: solid 1px white;
    //border-bottom: solid 1px white;
    position: absolute;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0;
    background: var(--q-primary);
    max-width: 100%;
  }
</style>
