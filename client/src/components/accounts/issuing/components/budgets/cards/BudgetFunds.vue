<template>
  <div class="_fw">
    <div class="font-1r row items-center">
      <!--      <div>-->
      <!--        <div class="font-3-4r">Budget</div>-->
      <!--        <div class="font-1-1-4r">{{dollarString(amtPath(budget, 'amount'), '$', 2) }}</div>-->
      <!--      </div>-->

      <q-space></q-space>
      <div class="flex items-center">
        <q-chip dense color="transparent" size="sm">
          <q-avatar size="10px" color="p3"></q-avatar>
          <span class="q-ml-sm font-7-8r alt-font">Available</span>
        </q-chip>
        <div class="font-1-1-2r tw-six">{{ dollarString(available, '$', 2) }}</div>
      </div>

    </div>
    <div class="__meter">
      <div class="__spent" :style="{ width: percent + '%' }"></div>
      <div class="__spent_p" :style="{ width: pending + '%' }"></div>
      <div class="__assigned" :style="{ width: assigned + '%' }"></div>
    </div>
    <div class="row items-start font-7-8r">
      <div class="col-8">
        <q-chip dense color="transparent">
          <q-avatar size="10px" color="p7"></q-avatar>
          <span class="q-ml-sm">Spent:&nbsp; <b>{{ dollarString(amtPath(budget, 'spent') + amtPath(budget, 'spent_sub'), '$', 2) }}</b></span>
        </q-chip>
        <q-chip dense color="transparent">
          <q-avatar size="10px" color="primary" style="border: dotted 2px black;"></q-avatar>
          <span class="q-ml-sm">Pending:&nbsp; <b>{{
              dollarString(amtPath(budget, 'spent_pending') + amtPath(budget, 'spent_pending_sub'), '$', 2)
            }}</b></span>
        </q-chip>
      </div>
      <q-space></q-space>
      <div>
        <q-chip dense color="transparent">
          <q-avatar size="10px" color="accent"></q-avatar>
          <span class="q-ml-sm">Monthly:&nbsp; <b>{{ dollarString(amtPath(budget, 'recurs'), '$', 2) }}</b></span>
        </q-chip>
      </div>
    </div>

    <div class="_fw q-pt-md">
      <div class="font-3-4r alt-font text-grey-7 tw-six text-uppercase">Assigned Funds</div>
      <q-chip color="transparent" class="font-7-8r">
        <q-avatar size="10px" color="primary"></q-avatar>
        <span class="q-ml-sm">Assigned: <b>{{ dollarString(amtPath(budget, 'assigned_amount'), '$', 0) }}</b></span>
      </q-chip>
      <q-chip color="transparent" class="font-7-8r">
        <q-avatar size="10px" color="a7"></q-avatar>
        <span class="q-ml-sm">Monthly: <b>{{ dollarString(amtPath(budget, 'assigned_recurs'), '$', 0) }}</b></span>
      </q-chip>
    </div>
  </div>
</template>

<script setup>

  import {dollarString} from 'src/utils/global-methods';
  import {amtPath} from '../utils';
  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useBudgets} from 'stores/budgets';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useBudgets();
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: budget } = idGet({
    store,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const percent = computed(() => {
    const { amount = 0, spent = 0, spent_sub = 0 } = budget.value || {}
    return Math.round(((spent + spent_sub)/amount) * 100)
  })

  const pending = computed(() => {
    const { amount = 0, spent = 0, spent_pending = 0, spent_sub = 0, spent_pending_sub = 0 } = budget.value || {}
    return Math.round(((spent + spent_pending + spent_sub + spent_pending_sub)/amount) * 100)
  })

  const assigned = computed(() => {
    const { amount = 0, spent = 0, spent_pending = 0, spent_sub = 0, spent_pending_sub = 0, assigned_amount = 0 } = budget.value || {}
    return Math.round(((spent + spent_pending + spent_sub + spent_pending_sub + assigned_amount)/amount) * 100)
  })

  const available = computed(() => {
    const { amount = 0, spent = 0, spent_pending = 0, spent_sub = 0, spent_pending_sub = 0, assigned_amount = 0 } = budget.value || {}
    return (amount - spent - spent_pending - spent_sub - spent_pending_sub - assigned_amount) / 100
  })
</script>

<style lang="scss" scoped>

  .__meter {
    width: 100%;
    margin: 3px 0;
    //border: solid 1px black;
    border-radius: 4px;
    position: relative;
    height: 40px;
    overflow: hidden;
    background: var(--q-p3);
  }

  .__spent {
    position: absolute;
    z-index: 4;
    top: 0;
    bottom: 0;
    left: 0;
    background: var(--q-p7);
    max-width: 100%;
  }

  .__spent_p {
    position: absolute;
    //border-top: dotted 2px white;
    //border-bottom: dotted 2px white;
    z-index: 3;
    top: 0;
    bottom: 0;
    left: 0;
    //background: var(--q-p7);
    max-width: 100%;
    height: 80px;
  }
  .__pend, .__spent_p {
    background: repeating-linear-gradient(
            45deg,
            black 0px,
            black 2px,
            var(--q-primary) 2px,
            var(--q-primary) 9px
    );
  }
  .__assigned {
    //border-top: solid 1px white;
    //border-bottom: solid 1px white;
    position: absolute;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0;
    background: var(--q-primary);
    max-width: 100%;
  }
</style>
