<template>
  <div class="_fw">
    <div class="row items-center">
      <q-icon v-if="!loading" class="q-mr-sm" size="18px" color="primary" name="mdi-filter"></q-icon>
      <q-spinner color="accent" class="q-mr-sm" v-else size="20px"></q-spinner>
      <div class="q-pa-sm tw-six text-grey-7">Account Transactions</div>
    </div>


    <div class="row items-center q-py-sm">
      <div class="flex items-center">
        <q-btn dense flat icon="mdi-calendar" color="primary"></q-btn>
        <inline-date @update:model-value="reload()" :input-attrs="{ label: 'From Date', borderless: true, class: '_inp', dense: true}" v-model="dateFrom"></inline-date>
        <q-separator color="white" vertical class="q-mx-xs"></q-separator>
        <inline-date @update:model-value="reload()" :input-attrs="{ label: 'To Date', borderless: true, class: '_inp', dense: true }" v-model="dateTo"></inline-date>
      </div>
      <div class="flex items-center q-px-sm">
        <q-btn dense flat icon="mdi-list-status" color="primary"></q-btn>
        <transaction-status @update:model-value="reload()" v-model="query.status" picker></transaction-status>
        <q-btn dense flat size="sm" color="red" v-if="query.status" icon="mdi-close" @click="removeStatus"></q-btn>
      </div>
    </div>
    <q-table
        :rows-per-page-options="[0]"
        flat
        :columns="cols"
        :rows="transactions.data"
        hide-no-data
        hide-bottom
        hide-pagination
    >
      <template v-slot:header="scope">
        <q-th></q-th>
        <q-th v-for="col in scope.cols" :key="col.name" :props="scope">
          {{col.label}}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope" @dblclick="openItem(scope.row)">
          <q-td></q-td>
          <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
            <component v-if="col?.component" :is="col.component" v-bind="col.attrs(scope.row, col)" v-on="col.listeners ? col.listeners(scope.row) : {}"></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div v-if="!transactions?.data?.length" class="q-pa-md text-italic font-1r">No Transactions Found</div>
    <div class="row q-pa-md" v-if="transactions.has_more">
      <q-chip color="a1" class="tw-six text-accent" clickable @click="loadMore()">
        <q-spinner color="accent" v-if="loading"></q-spinner>
        <span class="q-mx-sm" v-if="!loading">Load More</span>
        <span class="q-mx-sm" v-else>Loading More...</span>
      </q-chip>
    </div>

  </div>
</template>

<script setup>
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import TdChip from 'components/common/tables/TdChip.vue';
  import TransactionStatus from './TransactionStatus.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import { formatDate } from 'src/utils/date-utils';
  import {useBanking} from 'stores/banking';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {useCareAccounts} from 'stores/care-accounts';

  const bankStore = useBanking();
  const caStore = useCareAccounts();
  const props = defineProps({
    modelValue: { required: true },
    moov_account: { required: true }
  })

  const { item:care_account } = idGet({
    store: caStore,
    value: computed(() => props.modelValue),
    params: ref({ runJoin: { with_wallet: true }}),
    refreshWhen: computed(() => !props.modelValue?._fastjoin?.wallet)
  })
  const account = computed(() => props.moov_account)
  const wallet = computed(() => care_account.value?._fastjoin?.wallet)
  const transactions = ref({ data: [] })

  const count = ref(50)
  const dateFrom = ref(new Date(new Date().getTime() - (1000 * 60 * 60 * 24 * 30)));
  const dateTo = ref(undefined);
  const query = ref({
    count: count.value,
    skip: 0
  })

  const loading = ref(false);

  const reload = async () => {
    query.value.skip = 0;
    setTimeout(async () => {
      const q = { ...query.value };
      if(dateFrom.value) q.createdStartDateTime = new Date(dateFrom.value);
      if(dateTo.value) q.createdEndDateTime = new Date(dateTo.value);
      loading.value = true;
      const res = await bankStore.get(account.value.accountID, { banking: { moov: { method: 'get_wallet_transactions', args: [account.value.accountID, wallet.value.walletID, q]}}})
          .catch(err => {
            console.error(`Error loading transactions: ${err.message}`)
            return { transactions: [] }
          })
      transactions.value.data = res.transactions || []
      loading.value = false;
    }, 1000)
  }

  const removeStatus = () => {
    delete query.value.status;
    reload()
  }
  const loadMore = async () => {
    query.value.skip += count.value
    const q = { ...query.value };
    if(dateFrom.value) q.createdStartDateTime = new Date(dateFrom.value);
    if(dateTo.value) q.createdEndDateTime = new Date(dateTo.value);
    const val = await bankStore.get(account.value.id, { banking: { moov: { method: 'get_wallet_transactions', args: [wallet.value.walletID, q]}}})
        .catch(err => {
          console.log(`Error loading transactions: ${err.message}`)
          $errNotify(`Error loading: ${err.message}`)
          return { transactions: [] }
        })
    transactions.value.data = [...transactions.value.data, ...val.data]
  }

  watch(wallet, (nv, ov) => {
    if(nv && nv.walletID !== ov?.walletID){
      reload()
    }
  }, { immediate: true })

  const cols = computed(() => {
    return [
      {
        name: 'date',
        label: 'Date',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: formatDate(row.createdOn, 'M-D-YY h:mm a'),
            }
          }
        }
      },
      {
        name: 'desc',
        label: 'Desc.',
        component: TdText,
        attrs: (row) => {
          return {
            col: {
              value: (row.memo || 'No Description'),
            }
          }
        }
      },
      {
        name: 'status',
        label: 'Status',
        component: TransactionStatus,
        attrs: (row) => {
          return {
            modelValue:row.status
          }
        }
      },
      {
        name: 'amount',
        label: 'Amount',
        component: TdChip,
        attrs: (row) => {
          const gt = row.grossAmount >= 0;
          return {
            chipAttrs: {
              color: 'white',
              class: `tw-seven ${gt ? 'text-green' : 'text-red-10'}`,
              label: `${gt ? '+' : ''}${dollarString((row.grossAmount || 0)/100, '$', 2)}`
            }
          }
        }
      }
    ].map(a => {
          return {
            label: a.name,
            name: a.name || a.label,
            sortable: true,
            align: 'left',
            field: a.name,
            ...props.columnSettings,
            ...a
          };
        })
  })
</script>

<style lang="scss" scoped>

</style>
