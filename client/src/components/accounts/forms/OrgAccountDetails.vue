<template>
  <div class="_form_grid _f_g_r">
    <!--      OWNER-->
    <div class="_form_label">Legal Name</div>
    <div class="q-pa-sm">
      <q-input v-model="form.legalName" @update:model-value="autoSave('legalName')"></q-input>
    </div>

    <!--      DESCRIPTION-->
    <div class="_form_label">Website or Description</div>
    <div class="q-pa-sm">
      <q-input autogrow v-model="form.website" placeholder="https://yourwebsite.com"
               @update:model-value="autoSave('website')"></q-input>
    </div>

    <!--    ADDRESS-->
    <div class="_form_label">Address</div>
    <div class="q-pa-sm">
      <tomtom-autocomplete
          allow-add
          v-model="form.address"
          @update:model-value="autoSave('address')"
          :use-tomtom-geocode="useTomtomGeocode"
      ></tomtom-autocomplete>
    </div>

    <!--    EIN-->
    <div class="_form_label">EIN</div>
    <div class="q-pa-sm">
      <ssn-input ein v-model="form.ein" @update:model-value="autoSave('ein')"></ssn-input>
    </div>
  </div>
</template>

<script setup>
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useOrgs} from 'stores/orgs';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useTomtomGeocode} from 'stores/tomtom-geocode';


  const orgStore = useOrgs();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => props.modelValue)
  ,
    useAtcStore
  })

  const formFn = (defs) => {
    return {
      legalName: defs?.name,
      ...defs
    }
  }

  const { save, form } = HForm({
    store: orgStore,
    value: org,
    validate: true,
    formFn,
    vOpts: ref({
      legalName: { name: 'Legal Name', v: ['gt:1'] },
      website: { name: 'Website or Description', v: ['notEmpty'] },
      address: { name: 'Address', v: ['notEmpty'] },
      ein: { name: 'EIN', v: ['exactLength:10'] }
    })
  })

  const { autoSave } = HSave({ form, store: orgStore })

</script>

<style lang="scss" scoped>

</style>
