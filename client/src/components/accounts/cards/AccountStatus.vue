<template>
  <div class="_fw q-pb-md">
    <div class="q-pa-sm text-ir-deep tw-six">⚠️ Finish verifying your account</div>

    <template v-if="['sent-credit', 'new'].includes(v.status)">
      <div class="font-7-8r tw-five q-pa-sm">{{methodMessages(true)[v.verificationMethod]}}</div>
      <template v-if="['ach', 'instant'].includes(v.verificationMethod)">
        <q-input input-class="font-7-8r" hint="MV####" class="w200 mw100" dense filled label="Enter Code" v-model="code" @keyup.enter="confirmVerify">
          <template v-slot:append>
            <q-btn dense flat icon="mdi-check-circle" color="ir-deep" @click="confirmVerify"></q-btn>
          </template>
        </q-input>
      </template>
      <template v-else></template>
    </template>
    <template v-else-if="['successful', 'verified'].includes(v.status)">
      <div class="q-pa-sm font-7-8r tw-five">Account verification successful</div>
      <q-btn no-caps class="tw-six" color="accent" push @click="addVerified()" :disable="loading">
        <span>Activate Account</span>
        <q-spinner class="q-ml-sm" color="white" v-if="loading"></q-spinner>
      </q-btn>
    </template>
    <template v-else-if="v.status === 'max-attempts-exceeded'">
      <div class="q-pa-sm font-7-8r tw-five">You have exceeded the maximum number of verification attempts. For security purposes contact us to finish verifying the account manually.</div>

    </template>
    <template v-else>
      <div v-if="v.status" class="font-7-8r tw-five q-pa-sm text-s7">Last attempt response: {{response}}</div>
      <div class="font-7-8r tw-five q-pa-sm">{{methodMessages(false).ach}}</div>

      <q-btn color="accent" no-caps class="tw-six" @click="startVerify" :disable="loading">
        <span>{{v.status ? 'Re' : ''}}Start Verification</span>
        <q-spinner class="q-ml-sm" color="white" v-if="loading"></q-spinner>
      </q-btn>
    </template>
  </div>
</template>

<script setup>
  import {resultMap} from 'components/accounts/utils/verify-account';
  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useBanking} from 'stores/banking';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {$errNotify} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';

  const bankStore = useBanking();
  const bankAccountStore = useBankAccounts();
  const orgStore = useOrgs();

  const isAfter415PMEST = () => {
    const now = new Date();

    const estOffset = 5 * 60; // EST is UTC-5
    const edtOffset = 4 * 60; // EDT is UTC-4 (for daylight saving time)

    const isDST = new Date().toLocaleTimeString('en-US', { timeZone: 'America/New_York', hour12: false }).startsWith('16');
    const currentOffset = isDST ? edtOffset : estOffset;

    // Convert the current time to EST/EDT
    const nowEST = new Date(now.getTime() - currentOffset * 60 * 1000);

    // Create a date for today at 4:15 PM EST
    const fourFifteenEST = new Date(nowEST);
    fourFifteenEST.setHours(16, 15, 0, 0);

    // Compare the times
    return nowEST > fourFifteenEST;
  }

  const methodMessages = (post) => {
    return {
      'instant': `We ${post ? 'sent' : 'will send'} a micro instant payment to your bank account - check for the code in the description and return here to enter it.`,
      'ach': `We ${post ? 'sent' : 'will send'} a micro ACH payment to your bank account${post ? `- which should arrive ${isAfter415PMEST() ? 'today' : 'tomorrow'}` : ''}. Check for the code in the description and return here to enter it.`,
      'micro': `We ${post ? 'sent' : 'will send'} 2 small deposits to your bank account which should arrive ${isAfter415PMEST() ? 'today' : 'tomorrow'}. When they arrive, return here to enter the amounts to verify your account.`
    }
  }

  const emit = defineEmits(['update:start', 'update:confirm'])
  const props = defineProps({
    modelValue: { required: true }
  })

  const { item:bankAccount } = idGet({
    value: computed(() => props.modelValue),
    store: bankAccountStore,
    useAtcStore
  });

  const v = computed(() => bankAccount.value?.verification || {})
  const response = computed(() => {
    const { codes, path } = resultMap[v.value.verificationMethod] || {}
    const exceptionDetails = v.value.exceptionDetails || {}
    return `${codes[exceptionDetails[path]] || 'Unknown code'} - ${exceptionDetails.description || 'No further details'}`
  })
  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => bankAccount.value?.owner),
    useAtcStore
  })

  const moovAccountID = computed(() => org.value?.treasury?.id)

  const loading = ref(false)

  const addVerified = async () => {
    loading.value = true;
    await bankAccountStore.patch(bankAccount.value._id, { $set: { ['verification.verified']: true }})
        .catch(err => {
          console.error(`Error setting verified: ${err.message}`)
          $errNotify(err.message);
        })
    loading.value = false;

  }

  const startVerify = async () => {
    loading.value = true;
    if(!bankAccount.value.moov_link_id || bankAccount.value.moov_link_id === 'connect'){
      await bankAccountStore.patch(bankAccount.value._id, { moov_link_id: 'connect'})
          .catch(err => {
            console.error(`Error connecting bank account: ${err.message}`)
            $errNotify(err.message);
          })
      loading.value = false;
      return;
    }
    await bankStore.get(moovAccountID.value, { banking: { moov: { method: 'init_verify', args: [moovAccountID.value, bankAccount.value.moov_link_id, bankAccount.value._id ]}}})
        .catch(err => {
          console.error(`Error sending verify: ${err.message}`)
          $errNotify(err.message);
        })
    await bankAccountStore.get(bankAccount.value._id)
    emit('update:start')
    loading.value = false;
  }

  const code = ref('');
  const confirmVerify = async () => {
    if(loading.value) return;
    if(code.value.length < 4) return $errNotify('Code should be at least 4 characters')
    loading.value = true;
    await bankStore.get(moovAccountID.value, { banking: { moov: { method: 'complete_verify', args: [code.value, moovAccountID.value, bankAccount.value.moov_link_id, bankAccount.value._id ]}}})
        .catch(err => {
          console.error(`Error sending verify: ${err.message}`)
          $errNotify(err.message);
        })
    emit('update:confirm')

    loading.value = false;
  }

  const checkVerify = async (tries = 0) => {
    const nv = bankAccount.value;
    if (!nv.verification?.verified && nv.moov_link_id && nv.moov_link_id !== 'connect') {
      if(moovAccountID.value) {
        const status = await bankStore.get(moovAccountID.value, {
          banking: {
            moov: {
              method: 'check_verify',
              args: [moovAccountID.value, nv.moov_link_id, nv._id]
            }
          }
        })
        if (status && status.status !== nv.verfication?.status) await bankAccountStore.get(nv._id);
      } else if (tries < 10){
        setTimeout(() => {
          checkVerify(tries + 1)
        }, 1000)
      }
    }
  }

  watch(bankAccount, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
     checkVerify()
    }
  }, {immediate: true});


</script>

<style lang="scss" scoped>

</style>
