<template>
  <div class="_fw">
    <q-list separator>
<!--      <member-item @click="select(person._id)" clickable :model-value="person">-->
<!--        <template v-slot:side>-->
<!--          <q-item-section side>-->
<!--          <q-icon size="20px" v-if="isSelected(person?._id)" color="green" name="mdi-checkbox-marked-outline"></q-icon>-->
<!--          <q-icon size="20px" v-else color="grey-6" name="mdi-checkbox-blank-outline"></q-icon>-->
<!--          </q-item-section>-->
<!--        </template>-->
<!--      </member-item>-->
      <member-item clickable v-for="(prsn, i) in filteredMembers" :key="`member-${i}`" :model-value="prsn" @click="select(prsn)">
        <template v-slot:side>
          <q-item-section side>
          <q-icon size="20px" v-if="isSelected(prsn)" color="green" name="mdi-checkbox-marked-outline"></q-icon>
          <q-icon size="20px" v-else color="grey-6" name="mdi-checkbox-blank-outline"></q-icon>
          </q-item-section>
        </template>
      </member-item>
    </q-list>
    <div v-if="!filteredMembers.length" class="q-pa-md text-italic font-1r">No household members eligible for this coverage. Ask your plan administrator if you think this is a mistake.</div>
  </div>
</template>

<script setup>
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {usePpls} from 'stores/ppls';
  import { containsPoint } from 'src/utils/geo-utils';
  import {useCoverages} from 'stores/coverages';

  const coverageStore = useCoverages();
  const store = useHouseholds();
  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value', 'add', 'remove']);
  const props = defineProps({
    person: { required: true },
    modelValue: { required: true },
    plan: { required: true },
    coverage: { required: true},
    enrollment: { required: true }
  });

  const { item:household } = idGet({
    value: computed(() => props.person?.household),
    store
  ,
    useAtcStore
  })
  const isSelected = (v) => {
    const list = [...props.modelValue || []].filter(a => !!a);
    return list.includes(v._id);
  }

  const { item:cov } = idGet({
    store: coverageStore,
    value: computed(() => props.coverage),

    useAtcStore
  })

  const memberIds = computed(() => Object.keys(household.value?.members ||{}))

  const filteredMembers = ref([]);
  const setFilteredMembers = (ppl) => {
    if(!props.coverage || !props.plan || !props.person || !props.enrollment) filteredMembers.value = [];
    if(cov.value.geo){
      const {longitude, latitude, postal} = props.enrollment.address || {};
      const basePoint = [longitude, latitude];

      const getLngLat = (p) => {
        const { enrolled = {} } = props.enrollment || {}
        const { zip, point } = enrolled[p._id] || {}
        if(zip && zip === postal) return undefined
        if(!point){
          const { latitude, longitude } = p.address || {};
          if(latitude && longitude) return [longitude, latitude];
          else return undefined
        } else return point
      }

      const personIsIn = containsPoint(basePoint, cov.value.geo);
      if(!personIsIn) ppl.splice(0, 1);
      for(let i = 0; i < ppl.length; i++) {
        const point = getLngLat(ppl[i]);
        if(point){
          const isIn = containsPoint(point, cov.value.geo);
          if(!isIn) ppl.splice(i, 1);
        } else if(!personIsIn) ppl.splice(i, 1);
      }
    }
    filteredMembers.value = ppl;
  }

  watch(memberIds, async (nv, ov) => {
    if(nv?.length !== ov?.length) {
      let ppl = [];
      if(nv?.length) {
        const pplData = await pplStore.find({ query: { _id: { $in: nv }}});
        // console.log('got ppl data', pplData);
        ppl = pplData?.data || []
      }
      setFilteredMembers([props.person, ...ppl]);
    }
  }, { immediate: true })

  const select = (val) => {
    const list = [...props.modelValue || []];
    const idx = list.indexOf(val._id);
    if(idx > -1) {
      list.splice(idx, 1);
      emit('remove', val._id);
    } else {
      list.push(val._id);
      emit('add', val._id);
    }
    emit('update:model-value', list);
  }
</script>

<style lang="scss" scoped>

</style>
