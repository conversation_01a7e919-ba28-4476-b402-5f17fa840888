<template>
  <div class="_fw">
    <div class="row q-pb-sm">
      <q-chip clickable color="grey-1">
        <span class="font-3-4r text-uppercase tw-six">{{interval}}</span>
        <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
        <q-menu>
          <div class="w300 mw100 q-pa-md bg-white">
            <q-list separator>
              <q-item v-for="(int, i) in Object.keys(intervals)" :key="`int-${i}`" clickable @click="interval = int">
                <q-item-section>
                  <q-item-label class="tw-six font-3-4r text-uppercase">{{int}}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </q-chip>
    </div>
    <table>
      <tbody>
      <tr>
        <td>Benefit Cost</td>
        <td>{{dollarString((cafe.total + erCoverage)*factor, '$', 0)}}</td>
      </tr>
      <tr>
        <td>Payroll Deduction</td>
        <td class="text-s7">{{dollarString((cafe.employee)*factor, '$', 0)}}</td>
      </tr>
      <tr>
        <td>Employer Share</td>
        <td class="text-p7">{{dollarString((erCoverage + cafe.employer)*factor, '$', 0)}}</td>
      </tr>
      <tr>
        <td>Tax Savings</td>
        <td class="text-a7">{{dollarString(taxSavings * factor, '$', 0)}}</td>
      </tr>
      <tr>
        <td>Your Net Cost</td>
        <td>{{dollarString(max(0, (cafe.employee - taxSavings - (erCoverage)))*factor, '$', 0)}}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {useHouseholds} from 'stores/households';
  import {taxSummary} from 'components/households/utils/income';
  import {$capitalizeFirstLetter, dollarString} from 'src/utils/global-methods';
  import { getErContribution } from 'components/enrollments/utils/contributions';
  import {HFind} from 'src/utils/hFind';
  import {useCams} from 'stores/cams';
  import { compSources } from 'components/comps/utils';
  import { getElectedContributions, getEmployerFlexCredits } from 'components/plans/utils/calcs';
  import {useMarketplace} from 'stores/marketplace';
  import {useOrgs} from 'stores/orgs';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useHouseholds();
  const camsStore = useCams();
  const marketStore = useMarketplace()

  const props = defineProps({
    person: { required: true },
    hh: { required: false },
    plan: { required: true },
    enrollment: { required: true }
  })

  const { h$:c$ } = HFind({
    store: camsStore,
    params: computed(() => {
      return {
        query: { person: props.enrollment?.person },
        runJoin: { cams_groups: props.plan?.groups }
      }
    })
  })

  const max = (...args) => {
    return Math.max(...args)
  }

  const { item: household } = idGet({
    store,
    value: computed(() => props.hh || props.person?.household),
    refreshWhen: computed(() => !props.hh?._fastjoin?.cams),
    params: ref({
      runJoin: { total_income: true }
    })
  })

  const cafe = computed(() => {
    let base = 0;
    let ttl = 0;
    for (let i = 0; i < c$.data.length; i++) {
      const {amount = 0, total = 0} = compSources(c$.data[i], 'year')
      base += amount;
      ttl += total;
    }
    const {total} = getElectedContributions(props.enrollment)
    const employerCafe = getEmployerFlexCredits(props.plan, {
      enrollment: props.enrollment,
      totalIncome: ttl,
      baseIncome: base,
      contributions: total
    })
    return {
      total,
      employee: total - employerCafe,
      employer: employerCafe
    }
  })

  const erCoverage = computed(() => {
    let amt = 0;
    const arr = Object.keys(props.enrollment?.coverages || {});
    for(let i = 0; i < arr.length; i++){
      amt += getErContribution({ covId: arr[i], enrollment: props.enrollment, plan: props.plan });
    }
    return amt;
  })

  const {
    wTotalTax,
    totalTax,
  } = taxSummary(household, { plan: props.plan, enrollment: props.enrollment, toggle: true });

  const taxSavings = computed(() => Math.max(0, ((wTotalTax.value || totalTax.value || 0) - totalTax.value || 0))/12);

  const interval = ref('monthly');
  const intervals = {
    'monthly': 1,
    'bi-weekly': (12/26),
    'annually': 12,
    'weekly': 12/52
  }
  const factor = computed(() => intervals[interval.value] || 1);

  const ptc = ref(0);
  watch(household, async (nv, ov) => {
    if(nv?._id && nv._id !== ov?._id){
      if(!props.plan?.ale){
        let zip = (props.enrollment.enrolled || {})[nv.person]?.zip;
        if(!zip){
          const org = await useOrgs().get(props.plan.org);
          zip = org.address?.postal
        }
        if(zip) {
          const jd = await useJunkDrawers().find({ query: { $limit: 1, itemId: `zips|${zip.slice(0, 3)}` } })
          const dr = jd.data[0].data[zip];
          const place = {
            zipcode: zip,
            state: dr.state,
            countyfips: dr.fips
          }
          const getPrsn = (p) => {
            return {
              age: p.age,
              dob: p.dob,
              child: !!p.dependent,
              gender: $capitalizeFirstLetter(p.gender || 'male')
            }
          }
          let income = 0;
          const arr = Object.keys(nv._fastjoin?.income_sources || {})
          for(let i = 0; i < arr.length; i++){
            income += nv._fastjoin.income_sources[arr[i]].total
          }
          const people = Object.keys(nv.members || {}).map(a => {
            return getPrsn({ ...nv.members[a], ...(props.enrollment?.enrolled || {})[a]})
          })
          const household = { people, income}
          // console.log('calling', place, household);
          const newPtc = await marketStore.get(nv._id, { runJoin: { ptc_est: { household, place }}})
          // console.log('got eem', newPtc);
          ptc.value = newPtc.ptc || 0;
        }
      }
    }
  }, { immediate: true });


</script>

<style lang="scss" scoped>
 table {
   width: 100%;
   border-collapse: collapse;

   tr {
     td {
       padding: 3px 8px;
       font-size: 1rem;
       border-bottom: solid .3px #999;
     }
     td:last-child {
       font-weight: bold;
     }
   }
   tr:last-child {
     td {
       border-bottom: none;
     }
   }
 }
</style>
