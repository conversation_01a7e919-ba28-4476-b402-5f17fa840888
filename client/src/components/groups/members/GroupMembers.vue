<template>
  <q-page class="_bg_ow">
    <div class="row justify-center">
      <div class="_cent bg-white mnh80 pd4 pw2">

        <div class="_fw">

          <q-tab-panels transition-next="slide-right" transition-prev="slide-left" animated
                        class="_panel bg-transparent" :model-value="!!slide">
            <q-tab-panel :name="false">
              <div class="row items-center">
                <q-chip square dark class="tw-six">Employee Groups</q-chip>
                <q-btn flat dense no-caps class="text-weight-bold" @click="addNew()">
                  <q-icon name="mdi-plus" color="primary"></q-icon>
                </q-btn>
              </div>

              <div class="q-py-md _fw mw500">
                <q-input v-model="search.text">
                  <template v-slot:prepend>
                    <q-icon name="mdi-magnify"></q-icon>
                  </template>
                </q-input>
              </div>
              <table>
                <thead>
                <tr>
                  <th>Name</th>
                  <th>Members</th>
                  <th>Benefit Class</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(grp, i) in g$.data" :key="`grp-${i}`" @click="open(grp)">
                  <td>{{grp.name}}</td>
                  <td>{{grp.memberCount}}</td>
                  <td>
                    <q-checkbox size="sm" :model-value="!!grp.planClass"></q-checkbox>
                  </td>
                </tr>
                </tbody>
              </table>
              <pagination-row v-bind="{ h$:g$, pagination, pageRecordCount, pAttrs: { color: 'ir-text' } }"></pagination-row>

            </q-tab-panel>

            <q-tab-panel class="_panel bg-transparent" :name="true">
              <div class="_fw">
                <div class="q-py-xs"></div>
                <div class="t-l-a">
                  <q-btn icon="mdi-arrow-left" flat @click="open()"></q-btn>
                </div>
                <group-form @update:model-value="open($event)" :model-value="active" :org="org"></group-form>
              </div>
            </q-tab-panel>
          </q-tab-panels>

        </div>

      </div>
    </div>
  </q-page>
</template>

<script setup>
  import GroupForm from 'src/components/groups/forms/GroupForm.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref, watch} from 'vue';
  import {useRoute, useRouter} from 'vue-router';

  const route = useRoute();
  const router = useRouter();

  import {HFind} from 'src/utils/hFind';

  import {useGroups} from 'src/stores/groups';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {HQuery} from 'src/utils/hQuery';
  import {useEnvStore} from 'stores/env';

  const envStore = useEnvStore();
  const store = useGroups();
  const orgStore = useOrgs();
  const slide = ref(0);

  const props = defineProps({
    parent: { required: false }
  })

  const { item: org } = idGet({
    store:orgStore,
    value: computed(() => props.parent || envStore.getOrgId)
  ,
    useAtcStore
  })

  const { search, searchQ } = HQuery({})

  const { h$:g$, pagination, pageRecordCount } = HFind({
    store,
    params: computed(() => {
      const obj = org.value?.groups || {};
      const ids = Object.keys(obj).map(a => obj[a]);
      return {
        query: { _id: { $in: ids }, ...searchQ.value }
      }
    })
  })

  const idList = computed(() => g$.data.map(a => String(a._id).substring(0, 5)).join(''));
  watch(idList, (nv, ov) => {
    if(nv && nv !== ov){
      const time = new Date().getTime();
      for(const grp of g$.data){
        if(!grp.memberCount || (time - new Date(grp.memberCountAt).getTime() > 1000 * 60 * 10)){
          store.patch(grp._id, { updatedAt: new Date() }, { runJoin: { get_member_count: true }})
        }
      }
    }
  }, { immediate: true })

  const active = computed(() => {
    if (!!slide.value) return g$.data.filter(a => a._id === slide.value)[0];
    else return undefined;
  })

  const open = (grp) => {
    const params = route.params;
    const id = grp?._id;
    if (!id) {
      delete params.groupId;
      delete params.membersTab;
    } else params.groupId = id;
    const { href } = router.resolve({ ...route, params });
    window.history.pushState({}, '', href);
    slide.value = !!id ? id : 0;
  }
  const addNew = () => {
    slide.value = 1;
  };

  const groupId = computed(() => route.params.groupId);
  watch(groupId, (nv) => {
    if(nv) slide.value = nv;
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  table {
    width: 100%;
    border-collapse: collapse;

     tr {
       th,td {
         border-bottom: solid .3px var(--ir-light);
         text-align: left;
       }
       th {
         padding: 3px 8px;
         font-size: .8rem;
         font-weight: 600;
         color: var(--ir-deep);
       }
       td {
         padding: 4px 8px;
         font-size: .9rem;
         font-weight: 500;
         color: var(--ir-text);


       }
       &:last-child {
        td {
          border-bottom: none;
        }
       }
     }
    tbody {
      tr {
        cursor: pointer;
        background: var(--ir-bg);
        transition: all .3s;

        &:hover {
          td {
            background: var(--ir-bg2);
          }
        }
      }
    }
  }


</style>
