<template>
  <q-page class="_fw _bg_ow">
    <div class="row justify-center">
      <div class="_cent bg-white pd5 pw2 q-px-md mnh80">
        <org-plans :org="fullOrg"></org-plans>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import OrgPlans from 'components/plans/cards/OrgPlans.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'src/stores/orgs';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';

  const envStore = useEnvStore();
  const { getOrgId:orgId } = contextItems(envStore);

  const props = defineProps({
    org: { required: false }
  })

  const orgStore = useOrgs();

  const { item: fullOrg } = idGet({
    value: computed(() => props.org || orgId.value),
    store: orgStore
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
