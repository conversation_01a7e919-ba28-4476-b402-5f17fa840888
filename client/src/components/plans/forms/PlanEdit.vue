<template>
  <div class="_fw bg-white">

    <q-slide-transition>
      <div class="q-py-sm row justify-end" v-if="dirty?.length">
        <q-btn no-caps class="_s_btn" push @click="save">
          <span class="q-mr-sm">Save Changes</span>
          <q-spinner v-if="saving" color="white"></q-spinner>
          <q-icon v-else name="mdi-content-save"></q-icon>
        </q-btn>
      </div>
    </q-slide-transition>
    <div class="row">
      <div class="col-12 col-md-6 q-pa-md">

        <plan-form
            card-class="__c"
            :org="form?.org"
            :model-value="form"
            @update-path="setForm"
        ></plan-form>


      </div>
      <div class="col-12 col-md-6 q-pa-md">

        <div class="_f_l _f_chip">Cafeteria Options</div>
        <div class="__c">
          <div class="q-px-sm tw-five text-italic">*Default limits are the legal limits. Usually, it makes sense to let employees
            contribute up to the limit.
          </div>

          <q-list dense separator>

            <q-expansion-item group="0" v-for="(k, i) in Object.keys(cafeKeys)" :key="`k-${i}`">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section>
                    <q-item-label class="tw-six">{{ getCafeName(k) }}<span class="q-px-sm font-1-1-4r"
                                                                           v-if="!!_get(form, ['cafe', k, 'active'])">✅</span>
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <div class="q-pa-md">
                <cafe-form
                    :plan-id="plan?._id"
                    :model-value="_get(form,  ['cafe', k])"
                    :cafe-key="k"
                    @update:model-value="setForm(`cafe.${k}`,$event)"
                ></cafe-form>
              </div>
            </q-expansion-item>
          </q-list>
        </div>
        <div class="_f_l _f_chip">HRA Options</div>
        <div class="__c">
          <q-list separator>
            <q-expansion-item group="1" v-for="(k, i) in Object.keys(hraKeys)" :key="`hra-${i}`">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section>
                    <q-item-label class="tw-six">{{ getHraName(k) }}<span class="q-px-sm font-1-1-4r"
                                                                          v-if="!!_get(form, ['hra', k, 'active'])">✅</span>
                    </q-item-label>
                    <q-item-label caption>For {{ hraKeys[k].covers }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <div class="q-pa-md">
                <hra-form :plan-id="plan?._id" :model-value="_get(form,  ['hra', k])" :hra-key="k"></hra-form>
              </div>

            </q-expansion-item>

          </q-list>
        </div>
        <div class="_f_l _f_chip">Participant Groups</div>
        <div class="__c">
          <plan-groups :model-value="form"></plan-groups>
        </div>

        <div class="_f_l _f_chip">Active Date</div>
        <div class="__c">
          <div class="row items-end q-px-md">
<!--            <q-btn-->
<!--                :label="form?.active ? 'Deactivate Plan' : 'Activate Plan'"-->
<!--                rounded-->
<!--                :color="form.active ? 'negative' : 'positive'"-->
<!--                @click="setForm('active', !form.active)"-->
<!--            ></q-btn>-->
            <div class="w300 mw100">
              <inline-date
                  input-class="num-font tw-six"
                  label="Plan-Year Start"
                  :model-value="form.planYearStart"
                  @update:model-value="setForm('planYearStart', $event)"
              ></inline-date>
            </div>
          </div>
        </div>
      </div>
    </div>
    <q-slide-transition>
      <div class="q-py-sm row justify-end" v-if="dirty?.length">
        <q-btn no-caps class="_s_btn" push @click="save">
          <span class="q-mr-sm">Save Changes</span>
          <q-spinner v-if="saving" color="white"></q-spinner>
          <q-icon v-else name="mdi-content-save"></q-icon>
        </q-btn>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import CafeForm from 'components/plans/cafe/forms/CafeForm.vue';
  import PlanForm from 'components/plans/forms/PlanForm.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import HraForm from 'components/plans/hra/forms/HraForm.vue';
  import PlanGroups from 'components/plans/groups/forms/PlanGroups.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {getHraName, hraKeys} from '../utils';
  import {usePlans} from 'stores/plans';

  const store = usePlans();
  const envStore = useEnvStore();

  import {HForm} from 'src/utils/hForm';

  import {cafeKeys, getCafeName} from '../utils';
  import {_get} from 'symbol-syntax-utils';
  import {useEnvStore} from 'stores/env';

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: plan } = idGet({
    value: computed(() => props.modelValue || envStore.getPlanId),
    store,
    routeParamsPath: 'planId'
  ,
    useAtcStore
  })

  const dirty = ref([]);
  const { form } = HForm({
    value: plan,
    store
  })

  const saving = ref(false);
  const save = async () => {
    const patchObj = { $set: {} };
    let run;
    for (const p of dirty.value) {
      run = true;
      patchObj.$set[p] = _get(form.value, p)
    }
    if (run) {
      saving.value = true;
      await store.patch(form.value._id, patchObj);
      saving.value = false;
      dirty.value = []
    }
  }

  const setForm = (path, val) => {
    const paths = path?.split('.');
    if (paths.length > 1) {
      const rest = paths.slice(1);
      const obj = form.value[paths[0]] || {};
      const finalObj = rest.reduce((acc, key, idx) => {
        if (idx === path.length - 1) acc[key] = val;
        if (!acc[key]) acc[key] = {}
        return acc[key];
      }, obj)
      form.value[paths[0]] = finalObj;
    } else {
      if(typeof form.value[path] === 'object' && !Array.isArray(form.value[path])) form.value[path] = { ...form.value[path], ...val }
      else form.value[path] = val;
    }
    dirty.value.push(path);
  }


</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 0 0 10px 10px;
    background: white;
    box-shadow: 0 2px 8px -2px #999;
    padding: 2vh 1vw;
  }
</style>
