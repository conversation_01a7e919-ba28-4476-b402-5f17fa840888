<template>
  <div class="_fw">
    <div class="_f_g">

      <template v-if="!form?._id">
        <div class="_f_l _f_chip">Use Template</div>
        <div :class="cardClass">
          <div class="q-pa-md">
            <q-chip clickable @click="templateDialog = true" color="white">
              <q-icon name="mdi-file-document" color="blue"></q-icon>
              <span class="q-ml-sm">Choose From Plan Templates</span>
            </q-chip>
          </div>
        </div>
      </template>

      <!--      NAME-->
      <div class="_f_l _f_chip">Plan Name</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <q-input input-class="tw-six" @update:model-value="autoSave('name')" v-model="form.name"
                   placeholder="Enter plan name..."></q-input>
        </div>
      </div>

      <!--      AKA-->
      <template v-if="template">
        <div class="_f_l _f_chip">A.K.A.</div>
        <div :class="cardClass">

          <q-input @keyup.enter="addAka" v-model="akaPending" placeholder="Add alias name">
            <template v-slot:append>
              <q-btn outline icon="mdi-check" color="green-8" @click="addAka"></q-btn>
            </template>
          </q-input>
          <div class="q-py-sm row items-center">
            <q-chip color="white" v-for="(aka, i) in form?.aka || []" :key="`aka-${i}`" removable @remove="removeAka(i)"
                    :label="aka"></q-chip>
          </div>
        </div>
      </template>

      <!--      DESCRIPTION-->
      <div class="_f_l _f_chip">Short Description</div>
      <div :class="cardClass">
        <div class="q-pa-sm">

          <q-input
              autogrow
              maxlength="200"
              counter
              @update:model-value="autoSave('description')"
              v-model="form.description"
              placeholder="Short plan description..."
          ></q-input>
        </div>
      </div>

      <!--      ELIGIBILITY-->
      <div class="_f_l _f_chip">Eligibility</div>
      <div :class="`_fw ${cardClass}`">
        <div class="row">
          <div class="col-shrink q-pa-sm">
            <div class="font-1r tw-six text-ir-grey-7">Hours Required (per week)</div>
            <q-input
                class="w200 mw100"
                dense
                filled
                type="number"
                :model-value="form?.eligibility?.hours || 30"
                @update:model-value="setForm('eligibility.hours', $event)">
              <template v-slot:append>
                <div class="tw-six font-3-4r">hours</div>
              </template>
            </q-input>
          </div>
          <div class="col-shrink q-pa-sm">
            <div class="font-1r tw-six text-ir-grey-7">Minimum Tenure</div>
            <q-input class="w200 mw100" dense filled type="number" :model-value="form?.eligibility?.term || 90"
                     @update:model-value="setForm('eligibility.term', $event)">
              <template v-slot:append>
                <div class="tw-six font-3-4r">days</div>
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <!--      ALE-->
      <div class="_f_l _f_chip">Shared Responsibility Status</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <div class="_form_grid">
            <div class="_form_label">ALE Status</div>
            <div class="q-pa-sm">
              <div class="row items-center">
                <q-checkbox :model-value="!!form.ale" @update:model-value="setForm('ale', $event)"
                            label="Applicable Large Employer"></q-checkbox>
                <q-btn color="primary" dense flat icon="mdi-information">
                  <q-tooltip>
                    <ale-description></ale-description>
                  </q-tooltip>
                </q-btn>
              </div>
            </div>
            <div class="_form_label">FTE Count</div>
            <div class="q-pa-sm">
              <div class="_fw mw300">
                <money-input prefix="" placeholder="FTE Count" v-model="form.estFte" @update:model-value="autoSave('estFte')">
                </money-input>
              </div>
            </div>
          </div>
        </div>


      </div>


      <!--      PLAN NUMBER-->
      <div class="_f_l _f_chip">Plan Number</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <div class="col-6 q-pa-xs">
            <ssn-input v-model="form.info.numberEin" ein label="EIN"
                       @update:model-value="autoSave('info.numberEin')"></ssn-input>
          </div>
          <div class="col-6 q-pa-xs">
            <q-input
                label="Plan Number"
                @update:model-value="setForm('info.number', $event)" :model-value="form.info.number"></q-input>
          </div>

          <div class="font-3-4r">This number is usually a 3 digit number starting with 501 - that when combined with the
            EIN of the plan sponsor, is the ERISA ID for the plan (EIN+number). If you do not add a number, one will be
            assigned.
          </div>

        </div>
      </div>
      <!--      PLAN SPONSOR-->
      <div class="_f_l _f_chip">Plan Sponsor</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <div class="font-3-4r text-red" v-if="!hasSponsor">&#42;ERISA required</div>
          <plan-contact
              :model-value="form?.info?.sponsor"
              @update:model-value="setContact('sponsor', $event)"
          >
            <template v-slot:afterName>
              <div class="_form_label">EIN (Tax ID)</div>
              <div class="q-px-sm">
                <ssn-input
                    :label="undefined"
                    hide-bottom-space
                    placeholder="Plan sponsor EIN..."
                    ein
                    :model-value="form.info.sponsor?.ein"
                    @update:model-value="setContact('sponsor', { ein:  $event })"
                ></ssn-input>
              </div>
            </template>
          </plan-contact>

        </div>
      </div>

      <!--      PLAN ADMIN-->
      <div class="_f_l _f_chip">Plan Administrator</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <div class="row items-center justify-end">
            <q-chip label="Set to sponsor info" color="white"
                    icon="mdi-content-copy" clickable @click="copyContact('planAdmin')"></q-chip>
          </div>
          <plan-contact
              :model-value="form?.info?.planAdmin"
              @update:model-value="setContact('planAdmin', $event)"
          ></plan-contact>

        </div>
      </div>


      <!--      PLAN FIDUCIARY-->
      <div class="_f_l _f_chip">Plan Fiduciary</div>
      <div :class="cardClass">
        <div class="q-pa-sm">
          <div class="row items-center justify-end">
            <q-chip label="Set to sponsor info" color="white" icon="mdi-content-copy"
                    clickable @click="copyContact('fiduciary')"></q-chip>
          </div>
          <plan-contact :model-value="form?.info?.fiduciary"
                        @update:model-value="setContact('fiduciary', $event)"></plan-contact>

        </div>
      </div>

      <!--      <div class="_f_l _f_chip">Plan Legal Agent</div>-->
      <!--      <div class="q-pa-sm">-->
      <!--        <plan-contact :model-value="form?.info?.legalAgent" @update:model-value="setContact('legalAgent', $event)"></plan-contact>-->
      <!--      </div>-->

      <div class="q-py-md row justify-end" v-if="!template && !form?._id">
        <q-btn label="Continue To Plan Builder" icon-right="mdi-chevron-right" no-caps class="_p_btn tw-six" push
               @click="initSave"></q-btn>
      </div>
      <div class="q-py-md row justify-end" v-else-if="template">
        <q-btn label="Save" icon-right="mdi-check" no-caps class="_p_btn tw-six" push
               @click="save"></q-btn>
      </div>

    </div>

    <common-dialog maximized v-model="templateDialog">
      <plan-select @update:model-value="setTemplate"></plan-select>
    </common-dialog>
  </div>
</template>

<script setup>
  import PlanSelect from 'components/plans/forms/PlanSelect.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';
  import PlanContact from 'components/plans/forms/PlanContact.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import AleDescription from 'components/plans/utils/AleDescription.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HForm} from 'src/utils/hForm';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {_get, _pick} from 'symbol-syntax-utils';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';


  const store = usePlans();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value', 'save', 'update-path']);
  const props = defineProps({
    modelValue: { required: false },
    org: { required: false },
    template: Boolean,
    cardClass: { default: '_fw' }
  })

  const templateDialog = ref(false);

  const formFn = (defs) => {
    return {
      org: props.org,
      info: {},
      ...defs
    }
  }

  const { form, save } = HForm({
    store,
    formFn,
    value: computed(() => props.modelValue),
    beforeFn: (val) => {
      if (props.org) val.org = props.org?._id;
      if (props.template) {
        val.org = undefined;
        val.template = true
      }
      return val;
    },
    afterFn: (val) => {
      if (val?._id) emit('update:model-value', val);
    }
  })

  const setTemplate = (val) => {
    const obj = _pick(val, Object.keys(val).filter(a => !['_id', 'org', 'parent'].includes(a)));
    for (const key in obj) {
      if (!['_id', 'org', 'parent'].includes(key)) form.value[key] = obj[key];
    }
    form.value.template = false;
    templateDialog.value = false;
  };

  const autoSave = (path) => {
    setTimeout(() => {
      if (form.value._id) emit('update-path', path, _get(form.value, path))
    }, 100)
  }

  const setForm = (path, val) => {
    const spl = path.split('.')
    if(spl.length > 1){
      let obj = form.value[spl[0]];
      for(let i = 1; i < spl.length - 1; i++){
        if(!obj[spl[i]] || typeof obj[spl[i]] !== 'object'){
          obj[spl[i]] = {};
        }
        obj = obj[spl[i]];
      }
      obj[spl[spl.length-1]] = val;
    } else form.value[path] = val;
    autoSave(path);
  }

  const setContact = (path, val) => {
    form.value.info = { ...form.value.info, [path]: { ...form.value.info[path], ...val } }
    autoSave('info');
  }
  const copyContact = (from) => {
    form.value.info[from] = { ...form.value.info[from], ...form.value.info.sponsor }
    autoSave('info');
  }

  const hasSponsor = computed(() => {
    const { name, phone, address, email, ein } = form.value?.info?.sponsor || { name: '' };
    return !!name && !!phone && !!address && !!email && !!ein;
  })

  const { item: fullOrg } = idGet({
    value: computed(() => props.org),
    store: orgStore,
    onWatch: (item) => {
      const sponsor = form.value?.info?.sponsor || { name: undefined };
      const { name, email, phone, ein } = item || { name: undefined };
      let change;
      if (name && !sponsor.name) {
        sponsor.name = name
        change = true;
      }
      if (email && !sponsor.email) {
        sponsor.email = email
        change = true;
      }
      if (phone && !sponsor.phone) {
        sponsor.phone = phone.number?.e164
        change = true;
      }
      if (ein && !sponsor.ein) {
        sponsor.ein = ein;
        change = true;
      }
      if (ein && !form.value?.info?.numberEin) {
        form.value.info = { ...form.value.info, numberEin: ein }
        change = true;
      }
      if (change) {
        form.value.info = { ...form.value.info, sponsor };
        autoSave('info');
      }
    }
  })


  const initSave = async () => {
    const saved = await save();
    if (saved?._id) emit('new', saved);
  }

  const akaPending = ref('');
  const addAka = () => {
    if (akaPending.value?.length > 1) {
      form.value.aka = Array.from(new Set([...(form.value.aka || []), akaPending.value]))
      akaPending.value = ''
    }
  }
  const removeAka = (i) => {
    form.value.aka.splice(i, 1);
  }

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 0 0 10px 10px;
    background: white;
    box-shadow: 0 2px 8px -2px #999;
    padding: 2vh 1vw;
  }
</style>
