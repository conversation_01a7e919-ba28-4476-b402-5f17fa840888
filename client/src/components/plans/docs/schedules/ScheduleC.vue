<template>
  <div class="_fw">
    <div style="padding: 20px 0; font-size: 1.5rem; font-weight: 700; color: #6e6e6e">Schedule C</div>
    <div style="font-size: 1.25rem; font-weight: 600;">Participating Employee Groups</div>
    <div style="font-size: 1rem;">The following are eligible groups from {{org?.name || 'The Plan Sponsor'}} and its related or affiliated companies.</div>
    <div v-for="(orgId, i) in orgKeys" :key="`grp-${i}`" style="font-size: 1rem; padding: 10px 0;">
      <div style="font-weight: 600">{{ byOrgL[orgId].org?.name }}</div>
      <div style="font-size: .85rem; width: 100%;">
                  <span v-for="(grp, idx) in byOrgL[orgId].groups || []"
                        :key="`grp-${i}-${idx}`">{{ idx === 0 ? '' : ', ' }}{{ grp.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useOrgs} from 'stores/orgs';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = usePlans();
  const orgStore = useOrgs();

  const props = defineProps({
    plan: { required: false },
    byOrg: Object
  })

  const orgKeys = computed(() => Object.keys(props.byOrg?.value || props.byOrg || {}))

  const byOrgL = computed(() => props.byOrg?.value || props.byOrg);

  const limit = ref(20);

  const { item: fullPlan } = idGet({
    store,
    value: computed(() => props.plan),
    onWatch: (val) => {
      const l = Object.keys(val?.coverages || {})?.length;
      if(l) limit.value = l;
    },
    useAtcStore

  })

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => fullPlan.value.org)
  ,
    useAtcStore
  })

</script>

<style lang="scss" scoped>

</style>
