<template>
  <div class="_fw">
    <div style="padding: 20px 0; font-size: 1.5rem; font-weight: 700; color: #6e6e6e">Schedule B</div>
    <div style="padding: 25px;">
      <div style="font-weight: 600; font-size: 1.25rem;">Special excluded expenses under {{fullPlan?.name}}</div>
      <div style="font-style: italic; font-size: 1rem;">None Listed</div>
    </div>
  </div>
</template>

<script setup>
  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = usePlans();

  const props = defineProps({
    plan: { required: false }
  })

  const limit = ref(20);

  const { item: fullPlan } = idGet({
    store,
    value: computed(() => props.plan),
    onWatch: (val) => {
      const l = Object.keys(val?.coverages || {})?.length;
      if(l) limit.value = l;
    },
    useAtcStore
  })



</script>

<style lang="scss" scoped>

</style>
