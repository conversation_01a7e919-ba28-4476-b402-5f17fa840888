<template>
  <div class="_fw">
    <div class="row items-center">
      <q-spinner v-if="c$.isPending" color="primary" size="30px"></q-spinner>
      <div class="tw-six text-grey-7 font-1r q-py-sm q-px-md">Choose Account</div>
      <q-btn dense flat color="primary" icon="mdi-plus" @click="addDialog = true"></q-btn>
      <q-space></q-space>
      <q-chip color="transparent" icon-right="mdi-information" label="Wallets" clickable @click="infoDialog = true"></q-chip>
    </div>
    <q-separator class="q-my-sm"></q-separator>
    <div class="row items-center q-pa-sm q-gutter-sm">
      <div class="__c" @click="setCareAccount(acct)" v-for="(acct, i) in c$.data" :key="`acct-${i}`">
        <care-account-card
            :model-value="acct"
        ></care-account-card>
      </div>
    </div>
    <common-dialog setting="right" v-model="addDialog">
      <div class="_fw bg-white q-pa-md">
      <care-account-form @update:model-value="addDialog = false"></care-account-form>
      </div>
    </common-dialog>
    <common-dialog setting="small" :maximized="$q.screen.lt.sm" v-model="infoDialog">
      <care-account-description></care-account-description>
    </common-dialog>
  </div>
</template>

<script setup>
  import CareAccountCard from 'components/care-accounts/cards/CareAccountCard.vue';
  import CareAccountDescription from 'components/care-accounts/cards/CareAccountDescription.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CareAccountForm from 'components/care-accounts/forms/CareAccountForm.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useCareAccounts} from 'stores/care-accounts';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';
  import {useAtcStore} from 'src/stores/atc-store';

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const caStore = useCareAccounts();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value']);
  const { item: fullOrg } = idGet({
    store: orgStore,
    value: getOrgId,
    useAtcStore
  })

  const addDialog = ref(false);
  const infoDialog = ref(false);

  const caIds = computed(() => fullOrg.value?.careAccounts || []);
  const { h$: c$ } = HFind({
    store: caStore,
    limit: computed(() => caIds.value?.length),
    params: computed(() => {
      return {
        query: { _id: { $in: caIds.value } },
        runJoin: { with_wallet: true }
      }
    })
  })

  const setCareAccount = (val) => {
    LocalStorage.setItem('care_account_id', val._id);
    emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>
  .__c {
    padding: 10px 15px;
    border-radius: 9px;
    box-shadow: 0 5px 18px -10px #999;
    cursor: pointer;
    transition: all .1s;
    background: white;
    width: 100%;
    max-width: 350px;

    &:hover {
      background: #f8f8f8;
    }
  }
</style>
