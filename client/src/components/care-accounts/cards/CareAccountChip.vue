<template>
  <q-chip v-bind="{...$attrs}" color="transparent" clickable>
    <template v-if="ca?._id">
      <q-img :src="icon" class="w20 h20" fit="contain"></q-img>
      <span class="q-ml-xs">{{ short ? ca?.name || '' : faName(ca) }}</span>
      <q-btn v-if="!noClose" class="q-ml-sm" size="xs" flat dense @click="emitUp(undefined)" icon="mdi-close"
             color="red"></q-btn>
      <q-icon v-else name="mdi-menu-down" class="q-ml-sm"></q-icon>
    </template>
    <template v-else>
      <q-img class="w20 h20" fit="contain" :src="icon"></q-img>
      <span class="q-mx-sm">Select Account</span>
      <q-icon name="mdi-menu-down"></q-icon>
    </template>

  </q-chip>

  <!--  <common-dialog setting="right" v-model="addDialog">-->
  <!--    <div class="_fw bg-white q-pa-md">-->
  <!--      <care-account-form @update:model-value="addDialog = false"></care-account-form>-->
  <!--    </div>-->
  <!--  </common-dialog>-->

</template>

<script setup>
  import icon from 'assets/common_cent_grey.svg';
  // import CareAccountForm from 'components/care-accounts/forms/CareAccountForm.vue';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {faName} from 'components/accounts/treasury/utils';
  import {useCareAccounts} from 'stores/care-accounts';
  import {idGet} from 'src/utils/id-get';
  import {useAtcStore} from 'src/stores/atc-store';
  // import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  const caStore = useCareAccounts();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    noClose: Boolean,
    modelValue: { required: true },
    emitValue: Boolean,
    query: Object,
    params: Object,
    iconAttrs: Object,
    org: { required: true },
    limit: Number,
    short: Boolean
  })

  const addDialog = ref(false);

  const dialog = ref(false);
  const { search, searchQ } = HQuery({ keys: ['name'] })

  const { item: ca } = idGet({
    store: caStore,
    value: computed(() => props.modelValue),
    useAtcStore
  })

  const { h$: c$ } = HFind({
    store: caStore,
    limit: computed(() => props.limit || 5),
    pause: computed(() => !dialog.value),
    params: computed(() => {
      return {
        ...props.params,
        query: {
          ...searchQ.value,
          ...props.query,
          owner: props.org?._id || props.org
        }
      }
    })
  })


  const emitUp = (val) => {
    const v = props.emitValue ? val?._id : val;
    emit('update:model-value', v);
  }
</script>

<style lang="scss" scoped>

</style>
