<template>
  <q-chip v-bind="{clickable: true, color: 'ir-grey-2', ...$attrs}">
    <default-avatar v-if="modelValue" :model-value="modelValue._fastjoin?.provider" :use-atc-store="useAtcStore"></default-avatar>
    <template v-if="!multiple && modelValue">
    <span class="q-mx-xs">{{ $limitStr(modelValue.name, 30, '...') }}
          <q-tooltip>{{ b.name }}</q-tooltip>
    </span>
    <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="selectBundle(b)"></q-btn>
    </template>
    <template v-else>
      <span class="q-mr-xs">Select Bundle</span>
      <q-icon name="mdi-menu-down"></q-icon>
    </template>
    <q-menu>
      <div class="w400 mw100 q-pa-sm">
        <q-input dense v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator>
          <q-item v-for="(b, i) in b$.data" :key="`b-${i}`" clickable>
            <q-item-section>
              <q-item-label class="tw-five">{{ b.name }}</q-item-label>
              <q-item-label>
                <provider-chip v-if="b.provider" :model-value="b.provider"></provider-chip>
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div class="tw-six text-primary">{{ dollarString(b.price, '$', 0) }}</div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
  <template v-if="multiple">
    <q-chip color="ir-grey-2" v-for="(b, i) in mv$.data" :key="`mv-${i}`">
      <default-avatar :model-value="b._fastjoin?.provider" :use-atc-store="useAtcStore"></default-avatar>
      <span class="q-mx-xs">{{ $limitStr(b.name, 30, '...') }}
              <q-tooltip>{{ b.name }}</q-tooltip>
      </span>
      <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="selectBundle(b)"></q-btn>
    </q-chip>
  </template>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {useBundles} from 'stores/bundles';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {useAtcStore} from 'src/stores/atc-store';
  import {loginPerson} from 'stores/utils/login';
  import {HQuery} from 'src/utils/hQuery';
  import {$limitStr, dollarString} from 'src/utils/global-methods';
  const { login } = loginPerson()

  const bundleStore = useBundles();

  const props = defineProps({
    provider: { required: false },
    modelValue: { required: false },
    multiple: Boolean,
    emitValue: Boolean
  })

  const { canEdit } = clientCanU({
    subject: ref(undefined),
    or: true,
    caps: computed(() => [['bundles', ['READ']]]),
    login
  })

  const { search, searchQ } = HQuery({})
  const params = computed(() => {
    const query = { ...searchQ.value }
    if (props.provider) query.provider = props.provider;
    else if (!canEdit.value.ok) query.public = true;
    return { query, runJoin: { pb_provider: true } }
  })
  const { h$: b$ } = HFind({
    store: bundleStore,
    params
  })

  const { h$: mv$ } = HFind({
    store: bundleStore,
    params: computed(() => {
      const query = {};
      if (props.modelValue) {
        if (Array.isArray(props.modelValue)) {
          query._id = { $in: props.modelValue.map(a => a._id || a) };
        } else query._id = { $eq: props.modelValue._id || props.modelValue };
      }
      return {
        runJoin: { pb_provider: true },
        query
      }
    })
  })

  const selectBundle = (pr) => {
    emit('update:model-value', pr);
    if (props.multiple) {
      const list = [...props.modelValue || []];
      const idx = list.map(a => a._id || a).indexOf(pr._id);
      if (idx > -1) list.splice(idx, 1);
      else list.push(props.emitValue ? pr._id : pr);
      emit('update:model-value', list)
    } else emit('update:model-value', props.emitValue ? pr._id : pr)
    emit('update:searching', false)
  }
</script>

<style lang="scss" scoped>

</style>
