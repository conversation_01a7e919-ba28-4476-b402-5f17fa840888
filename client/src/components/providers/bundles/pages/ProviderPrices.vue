<template>
  <q-page>
    <provider-top></provider-top>
    <div class="row justify-center">
      <div class="_cent pd5 pw2">

        <q-tab-panels class="_panel" :model-value="!!route.params.bookId" animated>
          <q-tab-panel class="_panel" :name="false">

            <div class="row justify-end">
              <q-btn flat class="_inp" glossy no-caps @click="dialog = !dialog">
                <span class="q-mr-sm">Add Pricing</span>
                <q-icon color="primary" name="mdi-plus"></q-icon>
              </q-btn>
            </div>

            <div class="row">
              <div class="col-12 col-md-6">
                <q-input v-model="search.text" placeholder="Search Prices...">
                  <template v-slot:prepend>
                    <q-icon name="mdi-magnify"></q-icon>
                  </template>
                </q-input>
              </div>
            </div>
            <div class="row q-py-sm">
              <div class="col-12 col-md-4 q-pa-sm" v-for="(pb, i) in p$.data" :key="`pb-${i}`">
                <div class="__c"  @click="setBook(pb)">
                  <bundle-card :model-value="pb"></bundle-card>
                </div>
              </div>
            </div>

          </q-tab-panel>
          <q-tab-panel class="_panel" :name="true">

            <div class="row items-center">
              <q-btn dense flat icon="mdi-chevron-left" color="primary"
                     @click="router.push({ ...route, params: {}})"></q-btn>
              <q-space></q-space>
              <q-btn no-caps flat @click="editing = $route.params.bookId">
                <span class="q-mr-sm">Edit</span>
                <q-icon color="accent" name="mdi-pencil-box"></q-icon>
              </q-btn>
            </div>

            <bundle-page></bundle-page>

          </q-tab-panel>
        </q-tab-panels>

      </div>
    </div>

    <common-dialog :model-value="dialog || !!editing" @update:model-value="toggleDialog" setting="right">
      <div class="_fw bg-white q-pa-md">
        <bundle-form :model-value="editing" :provider="provider"></bundle-form>
      </div>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import ProviderTop from 'components/providers/cards/ProviderTop.vue';
  import BundleCard from 'components/providers/bundles/cards/BundleCard.vue';
  import BundleForm from 'components/providers/bundles/forms/BundleForm.vue';
  import BundlePage from 'components/providers/bundles/pages/BundlePage.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {HQuery} from 'src/utils/hQuery';
  import {useBundles} from 'stores/bundles';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useRoute, useRouter} from 'vue-router';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {useProviders} from 'stores/providers';

  const route = useRoute();
  const router = useRouter();

  const store = useBundles();
  const pStore = useProviders();

  const providerId = computed(() => LocalStorage.getItem('provider_id'))

  const { item: provider } = idGet({
    store: pStore,
    value: providerId
  ,
    useAtcStore
  })

  const editing = ref();
  const dialog = ref(false);
  const toggleDialog = (val) => {
    if (!val) {
      dialog.value = false;
      editing.value = undefined;
    }
  }

  const { search, searchQ } = HQuery({ keys: ['name', 'description'] })

  const { h$: p$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          provider: providerId.value
        }
      }
    })
  })

  const bookId = computed(() => route.params.bookId)
  const setBook = (pb) => {
    router.push({ ...route, params: { bookId: pb._id } })
  }
</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 10px;
    box-shadow: 0 2px 8px -2px #999;
    background: white;
    padding: 20px 1vw;
  }
</style>
