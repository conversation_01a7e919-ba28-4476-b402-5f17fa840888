<template>
  <q-chip v-if="!modelValue" v-bind="{ label: emptyLabel, color: 'transparent', ...chipAttrs}">
    <slot name="menu"></slot>
    <slot name="right"></slot>
  </q-chip>
  <q-chip
      v-else
      @remove="emit('remove')"
      v-bind="{...chipAttrs}"
  >
    <default-avatar
        :model-value="p"
        :dark="dark"
        :bg-in="dark ? 'black' : 'white'"
        :use-atc-store="useAtcStore"
    >
      <template v-slot:menu="scope">
        <q-popup-proxy>
          <div class="w400 mw100 bg-white q-pa-md">
            <provider-item :model-value="scope.item"></provider-item>
          </div>
        </q-popup-proxy>
      </template>
      <template v-slot:no-avatar>
        <div v-if="providerTypes[modelValue.primaryType]?.icon" class="_fa flex flex-center">
          {{ providerTypes[modelValue.primaryType].icon }}
        </div>
        <q-icon v-else size="25px" :color="dark ? 'white' : 'secondary'" name="mdi-hospital"></q-icon>
      </template>
    </default-avatar>
    <span class="q-ml-xs">{{ $limitStr(p.name || emptyLabel, limit, '...') }}</span>
    <slot name="right" :item="p"></slot>
    <slot name="menu" :item="p"></slot>
  </q-chip>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useProviders} from 'stores/providers';
  import {useAtcStore} from 'src/stores/atc-store';
  import {$limitStr} from 'src/utils/global-methods';
  import {providerTypes} from 'components/providers/utils/types';

  const store = useProviders();
  const emit = defineEmits(['remove'])
  const props = defineProps({
    modelValue: { required: true },
    chipAttrs: Object,
    emptyLabel: String,
    dark: Boolean,
    limit: { default: 50 }
  })

  const { item: p } = idGet({
    store,
    value: computed(() => props.modelValue),
    refreshOn: (val) => !val?._fastjoin?.files,
    params: ref({
      runJoin: { provider_avatar: true }
    })
  })
</script>

<style lang="scss" scoped>

</style>
