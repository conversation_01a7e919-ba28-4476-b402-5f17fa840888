<template>
  <div class="_fw">
    <div class="__title">
      <default-avatar :model-value="opt" :use-atc-store="useAtcStore"></default-avatar>
      <div class="font-1r tw-six q-px-sm">{{ opt.name }}</div>
      <q-tooltip>{{opt.name}}</q-tooltip>
    </div>

    <div class="_fw q-py-sm">
      <div class="font-1r">
        {{ opt.locations[0].city }}, {{ opt.locations[0].region }}
      </div>
    </div>
    <div class="__contacts q-py-sm" v-if="!simple">

      <div class="__d" v-if="opt?.googleRating && review">
        <div class="">
          <q-img style="height: 20px; width: 20px;" fit="contain"
                 src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/oauth%2Fgoogle_login.svg?alt=media&token=cb214096-6766-4437-b43e-c972624c70e2"></q-img>
        </div>
        <div class="flex items-center">
          <q-rating color="yellow-8" :model-value="opt.googleRating"></q-rating>
          <div class="text-grey-7">{{ opt.googleRating }} ({{ opt.googleRatingCount || 1 }} Reviews)</div>
        </div>
      </div>
      <div class="__d __hgh" @click="openLink(opt.websiteUri)" v-if="opt?.websiteUri">
        <div>
          <q-icon size="20px" name="mdi-link"></q-icon>
        </div>
        <div class="text-p6 font-3-4r">
          {{ $limitStr(opt.websiteUri, 50, '...') }}
        </div>
      </div>
      <div v-if="opt?.phone" class="__hgh __d" @click="openLink(`tel:${opt.phone.number?.e164}`)">
        <div class="">
          <q-icon color="primary" name="mdi-phone" size="12px"></q-icon>
        </div>
        <div class="font-3-4r">
          {{ opt.phone.number?.national }}
        </div>
      </div>
    </div>

    <slot name="bottom" :item="opt"></slot>

  </div>
</template>

<script setup>
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';

  import {$limitStr} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useProviders} from 'stores/providers';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useProviders();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    simple: Boolean,
    labelClass: String,
    review: Boolean
  })

  const { item: opt } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const openLink = (val) => {
    window.open(val, '_blank')
  }
</script>

<style lang="scss" scoped>
  .__contacts {
    width: 100%;

    .__d {
      width: 100%;
      display: grid;
      grid-template-rows: auto;
      grid-template-columns: auto 1fr;
      align-items: center;

      div {
        padding: 5px;
      }
    }
  }

  .__hgh {
    cursor: pointer;
    transition: all .2s;

    &:hover {
      background: #efefef;
    }
  }

  .__title {
    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;

    > div:last-child {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
</style>
