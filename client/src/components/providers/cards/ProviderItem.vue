<template>
  <q-item
      v-bind="{clickable: true, ...$attrs}"
  >
    <q-item-section>
      <q-item-label @click="emit('update:model-value', opt)" :class="`tw-six font-7-8r ${labelClass}`">{{ opt?.name }}</q-item-label>
      <slot name="caption" v-bind="{ opt }">
      <q-item-label @click="emit('update:model-value', opt)" v-if="opt?.locations?.length" class="text-grey-9 font-7-8r">
        {{ opt.locations[0].address1 }}
        {{ opt.locations[0].address2 }} - {{ opt.locations[0].city }}, {{ opt.locations[0].region }}
      </q-item-label>
      </slot>
      <div class="__contacts" v-if="!simple">
        <div class="__d" v-if="opt?.googleRating && review">
          <div class="">
            <q-img style="height: 20px; width: 20px;" fit="contain"
                   src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/oauth%2Fgoogle_login.svg?alt=media&token=cb214096-6766-4437-b43e-c972624c70e2"></q-img>
          </div>
          <div class="flex items-center">
            <q-rating color="yellow-8" :model-value="opt.googleRating"></q-rating>
            <div class="text-grey-7">{{ opt.googleRating }} ({{ opt.googleRatingCount || 1 }} Reviews)</div>
          </div>
        </div>
        <div class="__d __hgh" @click="openLink(opt.websiteUri)" v-if="opt?.websiteUri">
          <div>
            <q-icon size="20px" name="mdi-link"></q-icon>
          </div>
          <div class="text-p6 font-3-4r">
            {{ $limitStr(opt.websiteUri, 50, '...') }}
          </div>
        </div>
        <div v-if="opt?.phone" class="__hgh __d" @click="openLink(`tel:${opt.phone.number?.e164}`)">
          <div class="">
            <q-icon color="primary" name="mdi-phone" size="12px"></q-icon>
          </div>
          <div class="font-3-4r">
            {{ opt.phone.number?.national }}
          </div>
        </div>
      </div>
    </q-item-section>

    <slot name="side" :item="opt"></slot>


  </q-item>
</template>

<script setup>

  import {$limitStr} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useProviders} from 'stores/providers';
  import {useAtcStore} from 'src/stores/atc-store';

  const store = useProviders();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    simple: Boolean,
    labelClass: String,
    review: Boolean
  })

  const { item: opt } = idGet({
    value: computed(() => props.modelValue),
    store
  ,
    useAtcStore
  })

  const openLink = (val) => {
    window.open(val, '_blank')
  }
</script>

<style lang="scss" scoped>
  .__contacts {
    width: 100%;

    .__d {
      width: 100%;
      display: grid;
      grid-template-rows: auto;
      grid-template-columns: auto 1fr;
      align-items: center;

      div {
        padding: 5px;
      }
    }
  }

  .__hgh {
    cursor: pointer;
    transition: all .2s;

    &:hover {
      background: #efefef;
    }
  }
</style>
