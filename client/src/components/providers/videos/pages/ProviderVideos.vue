<template>
  <q-page>
    <div class="row">
      <div class="col-12 col-md-6 q-pa-sm" v-for="(k, i) in Object.keys(tabs)" :key="`tab-${i}`">
        <div class="__c">
          <div class="__t">{{ tabs[k].label }}</div>
          <q-tab-panels class="_panel" v-model="tabs[k].tab" animated>
            <q-tab-panel class="_panel" name="list">
              <div class="q-pa-md">
                <q-chip color="transparent" clickable @click="tabs[k].tab = 'form'">
                {{ $possiblyPlural('Video', Object.keys((provider.videos || {})[k] || {})) }}
                  <q-icon class="q-ml-sm" color="primary" name="mdi-pencil-box"></q-icon>
                </q-chip>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="form">
              <div class="row">
                <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="tabs[k].tab = 'list'"></q-btn>
              </div>
              <provider-video-form :key-options="tabs[k].options" :path="k" :provider="provider"></provider-video-form>
            </q-tab-panel>
          </q-tab-panels>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import ProviderVideoForm from 'components/providers/videos/forms/ProviderVideoForm.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useProviders} from 'stores/providers';
  import {LocalStorage} from 'symbol-auth-client';
  import {$possiblyPlural} from 'src/utils/global-methods';

  const pStore = useProviders();

  const { item: provider } = idGet({
    store: pStore,
    value: computed(() => LocalStorage.getItem('provider_id'))
  ,
    useAtcStore
  })

  const tabs = ref({
    'general': {
      tab: 'list',
      label: 'General Videos',
      options: ['group_landing', 'patient_landing']
    },
    'memberships': {
      tab: 'list',
      label: 'Membership Care Videos',
      options: []
    },
    'bundles': {
      tab: 'list',
      label: 'Bundle Videos',
      options: []
    }
  })

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 12px -4px #999;
    padding: 30px 20px 20px 20px;
    position: relative;
    margin-top: 10px;
  }

  .__t {
    position: absolute;
    top: 0;
    left: 5%;
    transform: translate(0, -30%);
    border-radius: 6px;
    background: linear-gradient(170deg, var(--q-s7), var(--q-secondary));
    color: white;
    font-weight: 600;
    padding: 6px 8px;
    font-size: 1rem;
  }

  .__vid {
    position: relative;
    height: 180px;
    width: 320px;
    max-width: 95vw;
    max-height: calc(95vw * .5);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15);
  }
</style>
