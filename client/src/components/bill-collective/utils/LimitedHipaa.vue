<template>
  <div id="LimitedHIPAA" class="container">
    <h1>HIPAA Authorization Agreement</h1>
    <h2>Limited Authorization to Disclose Protected Health Information</h2>
    <div class="q-py-sm"></div>
    <p>
      I, <b>{{ form?.name || 'Enter your name below' }}</b>, hereby authorize <b>CommonCare</b> to contact my medical
      provider(s)
      and discuss my medical bills for the purpose of negotiating a reduction in the amount owed.
    </p>
    <p>
      I understand that this authorization is limited to the disclosure of information necessary to verify my account
      details and negotiate a payment arrangement or discount. This authorization does not permit CommonCare to access
      my full medical records.
    </p>
    <p>
      This authorization will remain in effect until the resolution of my medical bill(s) or until I provide written
      notice of termination of this authorization.
    </p>
    <p>
      I understand that I have the right to revoke this authorization at any time by providing written notice to
      CommonCare. I also understand that the revocation will not apply to information already disclosed under this
      authorization.
    </p>

    <!--    <div class="button-container">-->
    <!--      <button onclick="window.print()">Print Agreement</button>-->
    <!--    </div>-->
  </div>
  <div class="_form_grid q-py-md">
    <div class="_form_label">Your Name</div>
    <div class="q-px-sm">
      <q-input dense filled v-model="form.name" @update:model-value="autoSave('name')"></q-input>
    </div>
    <template v-if="eraser">
      <div class="_form_label">Relationship to patient</div>
      <div class="q-px-sm">
        <q-radio size="sm" :model-value="eraser.personRelationship" val="self" label="Self"
                 @update:model-value="setPersonRelationship"/>
        <q-radio size="sm" :model-value="eraser.personRelationship" val="spouse" label="Spouse/Partner"
                 @update:model-value="setPersonRelationship"/>
        <q-radio size="sm" :model-value="eraser.personRelationship" val="guardian" label="Parent/Guardian"
                 @update:model-value="setPersonRelationship"/>
      </div>
    </template>
    <div class="_form_label">Your Email</div>
    <div class="q-px-sm">
      <email-field dense filled v-model="form.email" @update:model-value="addEmail"></email-field>
    </div>
    <div class="_form_label">Your Phone</div>
    <div class="q-px-sm">
      <phone-input :input-attrs="{ dense: true, filled: true }" v-model="form.phone"
                   @update:model-value="addPhone"></phone-input>
    </div>
  </div>
  <q-slide-transition>
    <div class="_fw q-py-sm" v-if="readyToSign">
      <div class="font-7-8r">By signing below you agree to use of our electronic signature terms - that you agree to
        both
        the terms of this agreement and limited HIPAA authorization. You also allow this signature to be placed as a
        facsimile
        image on this HIPAA agreement to be submitted to your medical provider(s) with whom we are negotiating on your
        behalf.
      </div>

      <template v-if="!sigSvg">
        <div class="font-3-4r tw-six text-ir-grey-7">Draw Signature
          <q-icon class="_i_i" size="25px" name="mdi-signature" color="primary"></q-icon>
        </div>
        <div class="__sig">
          <signature-draw
              v-bind="sizes"
              @update:model-value="addSignature"></signature-draw>
        </div>
      </template>
      <q-slide-transition>
        <div class="_fw flex items-center" v-if="sigSvg">
          <div id="SigDisplay"></div>
          <q-btn dense flat color="red" icon="mdi-close" @click="resign"></q-btn>
        </div>
      </q-slide-transition>
    </div>
  </q-slide-transition>


</template>

<script setup>
  import SignatureDraw from 'components/bill-collective/utils/SignatureDraw.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, nextTick, ref, watch} from 'vue';
  import {useBillErasers} from 'stores/bill-erasers';
  import {checkOrAdd} from 'src/fingerprints';
  import {idGet} from 'src/utils/id-get';
  import {loginPerson} from 'stores/utils/login';
  import {usePpls} from 'stores/ppls';
  import {HForm, HSave} from 'src/utils/hForm';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import {dataURLToBlob} from 'components/common/uploads';
  import {checkDeleteBillEraserDB, getEraserDbFiles} from 'components/bill-collective/utils/eraser-db';

  const { person } = loginPerson()
  const pplStore = usePpls();
  const eraserStore = useBillErasers()

  const props = defineProps({
    name: String,
    session: String,
  })

  const sizes = ref({ height: '150px', width: '500px', maxHeight: '60vh', maxWidth: '100%' });

  const { item: eraser } = idGet({
    store: eraserStore,
    value: computed(() => props.session)
  ,
    useAtcStore
  })
  const { item: eraserPerson } = idGet({
    store: pplStore,
    value: computed(() => eraser.value?.person)
  ,
    useAtcStore
  })

  const recaptureFiles = async () => {
    const list = await getEraserDbFiles()
    if (list?.length) {
      const payload = new FormData();
      for (let i = 0; i < list.length; i++) {
        if (list[i].url) {
          const blob = dataURLToBlob(list[i].url)
          payload.append('files', blob);
        }
      }
      const res = await axiosFeathers().patch(`/bill-erasers/${eraser.value._id}`, payload, { params: { core: restCore(), runJoin: { add_files: true }}})
          .catch(err => console.error(`Error adding files: ${err.message}`))
      if(res.data) checkDeleteBillEraserDB()
    }
  }

  const addSignature = async (signature) => {
    const terms = document.getElementById('ReviewTerms')?.innerText;
    // console.log('got terms?', terms);
    const { data } = await checkOrAdd()
    const mandate = {};
    mandate.ip = data.ipInfo.ip;
    mandate.user_agent = window.navigator.userAgent;
    mandate.date = new Date().getTime();
    mandate.copy = terms;
    const hipaa = document.querySelector('#LimitedHIPAA').innerText;
    const patchObj = { mandate, hipaa, signature }
    const id = eraser.value._id;
    eraserStore.patchInStore(id, patchObj);
    eraserStore.patch(id, patchObj)
        .catch(err => console.log(`Error adding signature: ${err.message}`));
    recaptureFiles()
  }

  const sigSvg = ref()
  const signature = computed(() => eraser.value?.signature);

  const resetPad = () => {
    nextTick(() => {
      const el = document.querySelector('#SigDisplay');
      if (el) {
        el.innerHTML = signature.value;
        const svg = el.querySelector('svg');
        const width = el.offsetWidth;
        const height = el.offsetHeight;

        const bbox = svg.getBBox();

        // Set the viewBox based on the bounding box
        svg.setAttribute('viewBox', `${bbox.x} ${bbox.y} ${bbox.width} ${bbox.height}`);

        // Match the SVG size to the container
        // svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
        svg.setAttribute('width', width);
        svg.setAttribute('height', height);
      }
    })
  }
  watch(signature, (nv, ov) => {
    if (nv && nv !== ov) {
      sigSvg.value = nv;
      resetPad()
    }
  }, { immediate: true })

  const resign = () => {
    sigSvg.value = undefined
  }

  const formFn = (defs) => {
    const obj = {};
    if (person.value) {
      ['name', 'phone', 'email'].forEach(a => {
        if (person.value[a]) obj[a] = person.value[a]
      })
    }
    return {
      ...obj,
      ...defs
    }
  }
  const { form, save } = HForm({
    store: pplStore,
    value: eraserPerson,
    formFn,
    afterFn: (val) => {
      console.log('saved person', val);
      if (val) {
        eraserStore.patch(eraser.value._id, { person: val._id })
      }
      return val;
    }
  })

  const { autoSave } = HSave({ form, save, store: pplStore, pause: computed(() => !form.value._id) })

  const addPhone = (val) => {
    form.value.phone = val;
    if (!form.value._id) save()
    else autoSave('phone')
  }

  const emailTo = ref();
  const addEmail = (val) => {
    form.value.email = val;
    if (!form.value._id) {
      if (emailTo.value) clearTimeout(emailTo.value)
      emailTo.value = setTimeout(() => {
        save()
      }, 3000)
    } else autoSave('email')
  }

  const setPersonRelationship = (val) => {
    eraserStore.patch(eraser.value._id, { personRelationship: val })
  }

  const readyToSign = computed(() => !!eraser.value?.personRelationship && form.value?._id)

  watch(readyToSign, (nv) => {
    if (nv) resetPad()
  }, { immediate: true })

</script>

<style lang="scss" scoped>


  .container {
    max-width: 800px;
    margin: auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #f9f9f9;
  }

  h1 {
    font-size: var(--text-md);
    line-height: 2rem;
  }

  h2 {
    font-size: var(--text-sm);
    font-weight: 600;
    line-height: 1.4rem;
  }

  h1, h2 {
    text-align: center;
  }

  p {
    margin-bottom: 10px;
  }

  label {
    font-weight: bold;
    display: block;
    margin-top: 10px;
  }

  input, textarea {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
  }


  @media print {
    .container {
      border: none;
      background-color: white;
    }
  }

  .__sig {
    width: 100%;
    max-width: 500px;
  }

  #SigDisplay {
    height: 50px;
    width: 200px;
  }
</style>
