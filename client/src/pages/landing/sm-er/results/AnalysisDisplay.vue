<template>
  <div id="AnalysisDisplay" class="_fw">
    <q-img class="h60 w60" v-show="!dark" :src="cc_logo" fit="contain"></q-img>
    <div class="row justify-center" v-if="!printView">
      <div class="_cent pw2">

        <div class="__tw">
          <q-spinner v-if="pending" color="primary" size="30px"></q-spinner>
          <div class="_fw" v-else>
            <div class="text-xs text-center">Estimated tax subsidy for your employee health plan</div>
            <div class="text-lg alt-font text-center tw-eight text-primary">{{ dollarString(total * 12, '$', 0) }}</div>
            <div class="_fw row justify-center q-pt-sm">
              <q-chip clickable color="ir-grey-2"
                      @click="$router.push({ name: 'smb-onboard', params: { quoteId: $route.quoteId }})">
                <span class="tw-six">Start your plan</span>
                <span class="q-ml-sm">🚀</span>
              </q-chip>
            </div>
            <div class="row justify-center">
              <q-btn v-if="!printing" icon="mdi-printer" dense flat no-caps @click="runPrint"></q-btn>
              <q-spinner v-else color="primary" size="20px"></q-spinner>
            </div>
          </div>
        </div>
        <q-slide-transition>
          <div class="__tw" v-if="!pending">
            <div class="t-r">
              <q-btn dense flat no-caps @click="emit('back')">
                <q-icon class="q-ml-sm" color="accent" name="mdi-pencil-box"></q-icon>
              </q-btn>
            </div>
            <div class="q-py-md row items-center">
              <name-logo-only
                  :input-class="docReq.orgName ? 'font-1r tw-six' : 'font-1r tw-six text-accent'"
                  placeholder="Your Company Name"
                  :id="docReq.org"
                  @update:image-in="setImg"
                  @update:name-in="setName"
                  :image-in="docReq.orgAvatar"
                  :name-in="docReq.orgName"
                  @update:id="setOrgId"
              >
                <template v-slot:hint>
                  <div class="font-3-4r tw-six q-px-xs">Employee Health Plan</div>
                </template>
              </name-logo-only>
            </div>
            <div class="text-xxs tw-five text-italic q-pb-sm">Click any employee to see their individual experience
            </div>
            <div class="row justify-end q-py-md" v-if="changes">
              <q-chip @click="refresh" clickable class="tw-five" color="transparent">
                <span class="q-mr-sm">Your data changed - Re-run estimate</span>
                <q-icon size="25px" v-if="!loading" color="accent" name="mdi-refresh"></q-icon>
                <q-spinner color="accent" size="25px" v-else></q-spinner>
              </q-chip>
            </div>
            <div class="__table">
              <table>
                <thead>
                <tr>
                  <th v-for="(h, i) in cols" :key="`h-${i}`">{{ h.label }}
                    <span v-if="h.tooltip" class="text-accent">&#42;</span>
                    <q-tooltip v-if="h.tooltip">
                      <div class="mw300 text-xxs tw-six" v-html="h.tooltip"></div>
                    </q-tooltip>
                  </th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(ee, i) in docReq?.employees || []" :key="`ee-${i}`" class="alt-font cursor-pointer"
                    @click="emit('one', ee.uid)">
                  <td v-for="(d, i) in cols" :key="`td-${i}`" :class="d.class">
                    {{ d.format(ee) }}
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </q-slide-transition>
        <div class="__ww">
          <q-list :dark="dark" separator>
            <q-expansion-item :content-inset-level=".5" v-for="(faq, i) in faqs" :key="`q-${i}`"
                              expand-icon="mdi-menu-down" :model-value="open.includes(i)"
                              @update:model-value="setOpen(i)">
              <template v-slot:header>
                <q-item class="_fw">
                  <q-item-section>
                    <q-item-label class="tw-six text-sm">{{ faq.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <div :class="`q-px-sm q-pb-lg font-1r tw-five text-${dark ? 'a2' : 'a9'}`">
                <div v-html="faq.body"></div>
              </div>
            </q-expansion-item>
          </q-list>
        </div>
        <div class="__ww">
          <div class="row q-py-lg">
            <q-chip dense square class="text-md tw-six bg-primary text-white">We make this easy</q-chip>
          </div>
          <div :class="`row text-${dark ? 'a1' : 'a10'}`">
            <div class="col-12 col-md-4 q-pa-sm">
              <div class="__c">
                <div>
                  <q-icon class="__ic" name="mdi-clipboard-text"/>
                  <div>
                    Plan documents
                  </div>
                </div>
                <div>
                  We generate your plan documents so you have a tight legal structure for your plan
                </div>
              </div>
            </div>
            <div class="col-12 col-md-4 q-pa-sm">
              <div class="__c">
                <div>
                  <q-icon class="__ic" name="mdi-clipboard-account"/>
                  <div>
                    Enrollment
                  </div>
                </div>
                <div>
                  We handle enrolling employees turn-key, you just connect their final elections to payroll
                </div>
              </div>

            </div>
            <div class="col-12 col-md-4 q-pa-sm">
              <div class="__c">
                <div>
                  <q-icon class="__ic" name="mdi-clipboard-list"/>
                  <div>Plan management</div>
                </div>
                <div>
                  Reporting, COBRA, special enrollment - everything your plan needs, we handle that.
                </div>
              </div>

            </div>
          </div>

          <div :class="`q-pt-md _fw row items-center text-${dark ? 'a1' : 'a10'}`">
            <div class="col-12 col-md-6 q-pa-sm">
              <div class="__c __fp">
                <div class="font-1-1-8r tw-six">One flat price: <span class="font-2r text-primary alt-font tw-eight">&nbsp;$5<span
                    class="q-ml-xs font-1-1-4r">PEPM</span></span></div>
                <div class="font-7-8r tw-six">Based on your census, that would be
                  {{ dollarString(5 * (docReq.employees || ['1']).length, '$', 0) }}/mo
                </div>
                <div class="font-7-8r tw-five text-a3">(You can pay or pass it through to plan participants)</div>
              </div>
            </div>
            <div class="col-12 col-md-6 q-pa-sm">
              <div class="row justify-center">
                <div class="text-center q-pb-md font-7-8r tw-six text-a3">30 days free trial</div>

              </div>
              <div class="row justify-center">
                <q-btn size="lg" class="bg-primary text-white tw-six" push
                       @click="$router.push({ name: 'smb-onboard', params: { quoteId: $route.quoteId}})">
                  <span class="q-mr-sm">Get Started</span>
                  <span class="font-1-1-4r">🚀</span>
                </q-btn>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="_fa">
      <div class="row q-px-md q-py-sm">
        <q-btn color="white" dense flat icon="mdi-close" @click="printView = false"></q-btn>
      </div>
      <template v-if="!tooBig">
      <file-preview :model-value="{ url: pdfUrl, info: { type: 'application/pdf' } }"></file-preview>
      </template>
      <template v-else>
        <div class="q-pa-lg font-1r text-italic">File too large to preview - it was opened or downloaded automatically</div>

      </template>
    </div>
  </div>
</template>

<script setup>
  import cc_logo from 'src/assets/commoncare_icon.svg'
  import NameLogoOnly from 'components/orgs/forms/NameLogoOnly.vue';
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useDocRequests} from 'stores/doc-requests';
  import {dollarString} from 'src/utils/global-methods';
  import {handlePrint} from 'components/plans/docs/utils/print';

  const drStore = useDocRequests();

  const emit = defineEmits(['edit', 'update:changes']);
  const props = defineProps({
    modelValue: { required: true },
    rerun: { type: Boolean },
    changes: { type: Boolean }
  })

  const loading = ref(false);

  const { item: docReq, pending } = idGet({
    store: drStore,
    value: computed(() => props.modelValue),
    refreshWhen: computed(() => props.rerun || props.changes),
    params: ref({ runJoin: { smb_ptc: { redo: props.rerun } } }),
    onLoad: (val) => {
      if (val) emit('update:changes', false);
      loading.value = false;
    }
  })

  const dark = ref(true);

  const open = ref([]);
  const setOpen = (i) => {
    if (open.value.includes(i)) open.value = open.value.filter(o => o !== i);
    else open.value.push(i);
  }

  const refresh = async () => {
    loading.value = true;
    const fresh = await drStore.get(docReq.value._id, { runJoin: { smb_ptc: { redo: false } } })
        .catch(err => {
          console.error(`Error refreshing census estiamte: ${err.message}`)
          loading.value = false
        })
    loading.value = false;
    if (fresh) emit('update:changes', false);
  }

  const setImg = (val) => {
    drStore.patchInStore(docReq.value._id, { orgAvatar: val });
    drStore.patch(docReq.value._id, { orgAvatar: val });
  }
  const setName = (val) => {
    if (docReq.value.orgName !== val) {
      drStore.patchInStore(docReq.value._id, { orgName: val });
      drStore.patch(docReq.value._id, { orgName: val });
    }
  }
  const setOrgId = (val) => {
    if (docReq.value.org !== val) {
      drStore.patchInStore(docReq.value._id, { org: val });
      drStore.patch(docReq.value._id, { org: val });
    }
  }
  const cols = computed(() => [
    // {
    //   label: 'Name',
    //   key: 'name',
    //   format: (ee) => ee.name
    // },
    // {
    //   label: 'Email',
    //   key: 'email',
    //   format: (ee) => ee.email
    // },
    {
      label: 'Age',
      key: 'age',
      format: (ee) => ee.age
    },
    {
      label: 'Pay',
      key: 'wage',
      format: (ee) => dollarString(ee.wage, '$', 0)

    },
    {
      label: 'Hours',
      key: 'hours',
      format: (ee) => dollarString(ee.hours, '', 0)
    },
    {
      label: 'Est. HH Income',
      key: 'hh_income',
      format: (ee) => dollarString(Math.max(0, ee.hh_income), '$', 0),
      class: '__hh',
      tooltip: 'Since credits are based on household income, we estimate additional income based on national averages at zip code, income level, and household size. <br><br>This estimate is as accurate as possible, but we gather actual household income from your employees when you set your plan up.'
    },
    {
      label: 'Household Size',
      key: 'household_size',
      format: (ee) => 1 + (ee.married === 'Y' ? 1 : 0) + (ee.deps || 0)
    },
    {
      label: 'Credit',
      key: 'ptc',
      format: (ee) => dollarString((ee.ptc || 0) * 12, '$', 0),
      class: '__credit'
    }
  ])

  const total = computed(() => {
    const ees = docReq.value?.employees || [];
    let t = 0;
    for (let i = 0; i < ees.length; i++) {
      t += ees[i].ptc || 0;
    }
    return t
  })

  const faqs = computed(() => [
    {
      label: 'What? Is this real money?',
      body: '100% real. The premium tax credit is a pillar of the ACA. The reason you may not have thought of it for your group plan is because group health insurance policies disqualify employees from receiving it.<div class="q-pt-sm">This is why a key detail to remember is that group health insurance is not the same thing as a group health plan. A plan is your legal and accounting structure for helping employees cover medical costs.</div><div class="q-pt-sm">When you have less than 50 full-time employees, a critical part of doing that is ensuring they have access to the premium tax credit. We help you facilitate that for them through your company health plan.</div>'
    },
    {
      label: 'What do I have to file to claim this?',
      body: 'Nothing. Health insurers are setup to receive these credits directly instead of premium payments. They send each insured a tax form 1095 at year-end.<div class="q-pt-sm">What IS important is that you have a thorough onboarding process so employees get the correct credit on their policy. We have you covered there.</div>'
    },
    {
      label: 'Can I still offer other benefits?',
      body: 'Yes, of course. This credit frees up substantial funds - which you can now use as you choose. When you use our CareWallet for your plan, employees can easily select any benefit, or even simply take their benefit spend home as bonus pay.<div class="q-pt-sm">It\'s important to remember that health insurance is not the best way to navigate healthcare. It\'s there as a safety net. If you want better benefits for actually improving the quality of your healthcare, see our direct care options. They are a great option to use alongside insurance coverage or health shares.</div>'
    },
    {
      label: 'What about my employee who don\'t qualify?',
      body: `While it\'s unfortunate that some higher income earners don\'t qualify for a premium tax credit, there are 2 reasons they still benefit from this setup.<ol><li>You have ${dollarString(total.value * 12, '$', 0)} in subsidy that you didn\'t have before. You can afford to generously compensate anyone who is feeling underpaid by this new arrangement.</li><li>You can still use our plan structure to offer them better options than they are used to - and tax advantage for their qualified coverage choices, including our direct care options, integrated HSA, and more.</li></ol>`
    }
  ])

  const { print, printing, pdfUrl, printView, tooBig } = handlePrint(ref('AnalysisDisplay'))

  const runPrint = async () => {
    dark.value = false;
    let list = [];
    for (let i = 0; i < faqs.value.length; i++) {
      list.push(i)
    }
    open.value = list;
    setTimeout(async () => {
      open.value = list;
      try {
        await print()
      } catch (e) {
        console.error(`Error printing: ${e.message}`)
      }
      dark.value = true;

    }, 500)
  }

</script>

<style lang="scss" scoped>

  .__table {
    width: 100%;
    overflow-x: scroll;
  }

  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        padding: 2px 4px;
        text-align: right;
        font-size: var(--text-xxs);
      }

      td {
        font-size: var(--text-xxs);
        padding: 5px 10px;
        text-align: right;
        border-bottom: solid 1px #999;

      }

      &:nth-child(even) {
        background: #f6f6f6;
      }

      &:first-child {
        td {
          &:last-child {
            border-radius: 5px 5px 0 0;
          }
        }
      }

      &:last-child {
        td {
          border-bottom: none;

          &:last-child {
            border-radius: 0 0 5px 5px;
          }
        }
      }
    }
  }

  .__hh {
    color: var(--q-a7);
    //background: var(--q-a0);
  }

  .__credit {
    font-weight: 600;
    background: var(--q-p0);
    color: var(--q-p6);
  }

  .__tw {
    position: relative;
    width: 100%;
    padding: 30px min(25px, 2vw);
    border-radius: 12px;
    box-shadow: 2px 2px 8px #dedede;
    background: white;
    margin: 20px 0;
    color: #101010;
  }

  .__ww {
    position: relative;
    width: 100%;
    padding: 30px min(25px, 2vw);
  }

  .__c {
    padding: 25px 15px;
    border-radius: 12px;
    //background: linear-gradient(0deg, var(--q-a1), var(--q-a0));
    //border: solid 3px var(--q-p2);
    //box-shadow: 0 4px 14px var(--ir-light);

    > div {
      &:first-child {
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
      }
    }
  }

  .__ic {
    color: var(--q-primary);
    margin-right: .5rem;
    //background: -webkit-linear-gradient(180deg, var(--q-accent), var(--q-primary));
    //-webkit-background-clip: text;
    //-webkit-text-fill-color: transparent;
  }

  .__fp {
    padding: 25px 20px;
    //background: linear-gradient(165deg, var(--q-a1) 20%, var(--q-a2));
  }
</style>
