<template>
  <div class="_fw __tw">

    <slot name="top">
      <div class="q-pa-sm">
        <name-logo-only
            :input-class="docReq.orgName ? 'font-1r tw-six' : 'font-1r tw-six text-accent'"
            placeholder="Your Company Name"
            :id="docReq.org"
            @update:image-in="setImg"
            @update:name-in="setName"
            :image-in="docReq.orgAvatar"
            :name-in="docReq.orgName"
            @update:id="setOrgId"

        >
          <template v-slot:hint>
            <div class="font-3-4r tw-six q-px-xs">Employee Health Plan</div>
          </template>
        </name-logo-only>
        <q-separator class="q-my-sm"></q-separator>
      </div>
      <div class="_fw q-py-sm pw2">
        <div class="text-sm tw-six flex items-center">
          <div>Hi,</div>
          <q-input
              placeholder="Enter Name..."
              class="q-mx-sm"
              dense
              hide-bottom-space
              filled
              borderless
              :model-value="ee.name?.split(' ')[0]"
              :style="{ width: `${(ee.name?.split(' ')[0].length || 12) + 3}ch`}"
              input-class="tw-six text-sm">
          </q-input>
          <div>welcome to your <span class="alt-font text-primary">Care Wallet</span></div>
        </div>
        <div class="text-xxs">Let's explore your benefits to optimize your compensation</div>
      </div>

    </slot>

    <q-separator class="q-my-sm"></q-separator>

    <div class="row items-center">
      <div class="col-12 q-px-md q-py-sm">
        <zip-picker class="text-xs" :model-value="ee.zip" @zip-data="setZipData">
          <template v-slot:left>
            <q-icon name="mdi-map-marker" color="red" class="q-mr-sm"></q-icon>
          </template>
        </zip-picker>
      </div>
    </div>

    <div class="_fw q-py-md pw2">
      <template v-if="ee.ptc > 50">
        <div class="text-sm tw-six">Tax Credits</div>
        <div class="text-xxs">Working for a small employer means there are tax credits to pay insurance premiums instead
          of using your payroll toward it.
        </div>
      </template>
      <div class="text-xxs tw-six q-pt-sm">Based on your household information:</div>

      <div class="__gr">
        <div>Your Annual Income:</div>
        <div class="q-pr-sm">
          <money-input prefix="$" borderless dense input-class="text-right tw-six text-xs" :model-value="ee.income"
                       @update:model-value="eeChanges.income = $event" @blur="setChanges('income')"></money-input>
        </div>
      </div>
      <div class="__gr">
        <div>Other Household Income</div>
        <div class="q-pr-sm">
          <money-input borderless dense input-class="text-right tw-six text-xs"
                       :model-value="(ee.hh_income || ee.income) - ee.income"
                       @update:model-value="eeChanges.hh_income = (ee.income || 0) + $event"
                       @blur="setChanges('hh_income')"></money-input>
        </div>
      </div>
      <div class="__gr">
        <div>Age:</div>
        <div class="tw-six text-xs">{{ ee.age }}
          <q-icon v-bind="{size: '18px', name: 'mdi-menu-down', class: '_i_i'}"/>
          <q-menu>
            <div class="w200 mw100">
              <q-list separator>
                <q-item v-for="i in 50" :key="`age-${i}`" clickable @click="setAge(i + 17, 'age')">
                  <q-item-section>
                    <q-item-label class="tw-six font-1r">{{ i + 17 }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </q-menu>
        </div>
      </div>
      <div class="__gr">
        <div>Spouse:</div>
        <div class="flex items-center tw-six text-xs">
          <q-checkbox size="sm" :model-value="ee.married === 'Y'"
                      @update:model-value="setAge($event ? 'Y' : 'N', 'married')"></q-checkbox>
          <div v-if="ee.married === 'Y'">{{ ee.spouseAge || ee.age }}
            <q-icon v-bind="{size: '18px', name: 'mdi-menu-down', class: '_i_i'}"/>
            <q-menu>
              <div class="w200 mw100">
                <q-list separator>
                  <q-item v-for="i in 50" :key="`age-${i}`" clickable @click="setAge(i + 17, 'spouseAge')">
                    <q-item-section>
                      <q-item-label class="tw-six font-1r">{{ i + 17 }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </div>

        </div>
      </div>
      <div class="__gr">
        <div>Dependents:</div>
        <div class="q-py-xs">
          <q-chip dense color="ir-grey-2" square clickable>
            <span class="tw-six text-xs">{{ ee.deps || 0 }}</span>
            <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
            <q-menu>
              <div class="w300 mw100">
                <q-list separator>
                  <q-item clickable v-for="i in 10" :key="`dep-${i}`" @click="setAge(i, 'deps')">
                    <q-item-section>
                      <q-item-label class="text-xs tw-six">{{ i }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-chip>
        </div>
      </div>

      <div v-if="ee.ptc > 50" class="text-xxs tw-six q-py-sm">You have <span
          class="tw-eight text-sm text-primary alt-font">{{ dollarString(ee.ptc, '$', 0) }}</span><span
          class="text-xs tw-six text-primary">/mo</span>
        to use toward plans like these:
      </div>
      <div v-else class="q-py-sm text-xxs tw-six">Choose from traditional ACA plans like these:</div>

      <q-list separator>
        <template v-if="loading">
          <q-item v-for="i in 3" :key="`sk-${i}`">
            <q-item-section avatar>
              <q-skeleton type="circle" size="40px"></q-skeleton>
            </q-item-section>
            <q-item-section>
              <q-skeleton></q-skeleton>
            </q-item-section>
          </q-item>
        </template>
        <template v-else>
          <q-item v-for="k in ['bronze', 'silver', 'gold']" :key="`m-${k}`">
            <q-item-section avatar>
              <metal-avatar :model-value="k"></metal-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label caption class="text-xxs tw-five">{{ metals[k]?.carrierName }}</q-item-label>
              <q-item-label class="text-xxs tw-six">{{ metals[k]?.title || metals[k]?.name }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <span class="tw-six text-xs alt-font text-primary">{{ dollarString(metals[k]?.premium, '$', 0) }}<span
                  class="text-xxs">/mo</span></span>
            </q-item-section>
          </q-item>
        </template>
      </q-list>

      <div class="text-xxs q-py-md">and over 100 more like them</div>

      <div class="_fw q-pt-md">
        <div class="text-xxs tw-six q-py-sm">Or choose from alternative coverage options like these:</div>

        <q-list separator>
          <template v-if="loading">
            <q-item v-for="i in 3" :key="`sk-${i}`">
              <q-item-section avatar>
                <q-skeleton type="circle" size="40px"></q-skeleton>
              </q-item-section>
              <q-item-section>
                <q-skeleton></q-skeleton>
              </q-item-section>
            </q-item>
          </template>
          <template v-else>
            <q-item v-for="(hs, i) in hs$.data" :key="`hs-${i}`">
              <q-item-section avatar>
                <q-img v-if="hs.carrierLogo" class="h40 w40" fit="contain" :src="byId.bySource[hs._id]?.url"></q-img>
              </q-item-section>
              <q-item-section>
                <q-item-label caption class="text-xxs tw-five">{{ hs.carrierName }}</q-item-label>
                <q-item-label class="text-xxs tw-six">{{ hs.name }}</q-item-label>

              </q-item-section>
              <q-item-section side>
                <span class="tw-six text-xs alt-font text-primary">{{
                    dollarString(getCoverageRate({ coverage: hs, enrolled: people }), '$', 0)
                  }}<span class="text-xxs">/mo</span></span>
              </q-item-section>
            </q-item>
          </template>
        </q-list>
      </div>
      <div class="_fw q-mt-lg __rem">
        <div class="q-py-sm">
          <div class="text-xs tw-five">These plans are great safety nets - but when you actually need healthcare</div>
          <div class="row items-center text-xs tw-six">
            <div>A plan administered using</div>
            <q-img class="h20 w20 q-mx-sm" fit="contain" :src="icon"></q-img>
            <div>always means</div>
          </div>
        </div>
        <q-list separator>
          <q-item>
            <q-item-section avatar>
              <div class="text-lg">🩺</div>
            </q-item-section>
            <q-item-section>
              <q-item-label caption class="text-xxs tw-five">Doctors who are paid for outcomes, not billable
                transactions
              </q-item-label>
              <q-item-label class="text-xxs tw-six">Direct Care Memberships</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section avatar>
              <div class="text-lg">📄</div>
            </q-item-section>
            <q-item-section>
              <q-item-label caption class="text-xxs tw-five">Never pay the first bill - let us reprice it first
              </q-item-label>
              <q-item-label class="text-xxs tw-six">Plan Bill Eraser</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section avatar>
              <div class="text-lg">🩻</div>
            </q-item-section>
            <q-item-section>
              <q-item-label caption class="text-xxs tw-five">Wholesale price meds, labs, scans, and other services
              </q-item-label>
              <q-item-label class="text-xxs tw-six">Employer Cash Price Networks</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script setup>
  import icon from 'src/assets/commoncare_icon.svg'
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import MetalAvatar from 'components/enrollments/ichra/cards/MetalAvatar.vue';
  import NameLogoOnly from 'components/orgs/forms/NameLogoOnly.vue';
  import ZipPicker from 'components/common/geo/pickers/ZipPicker.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useDocRequests} from 'stores/doc-requests';
  import {computed, ref, watch} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {useMarketplace} from 'stores/marketplace';
  import {getCmsPerson, isNewPlace} from 'components/market/household/utils';
  import {HFind} from 'src/utils/hFind';
  import {useCoverages} from 'stores/coverages';
  import {getCoverageRate, getFixedRateKey, getPremium} from 'src/components/coverages/utils/display';
  import {getStateCode} from 'components/common/geo/data/states';
  import {useUploads} from 'stores/uploads';
  import {manageFindUploads} from 'components/utils/uploads/file-manager';

  const drStore = useDocRequests();
  const marketStore = useMarketplace();
  const coverageStore = useCoverages();
  const uploadStore = useUploads();

  const emit = defineEmits(['back', 'update:aptc'])
  const props = defineProps({
    req: { required: false },
    uid: String,
    defEe: Object
  })

  const { item: docReq } = idGet({
    store: drStore,
    value: computed(() => props.req)
  ,
    useAtcStore
  })

  const setImg = (val) => {
    drStore.patchInStore(docReq.value._id, { orgAvatar: val });
    drStore.patch(docReq.value._id, { orgAvatar: val });
  }
  const setName = (val) => {
    if(docReq.value.orgName !== val){
      drStore.patchInStore(docReq.value._id, { orgName: val });
      drStore.patch(docReq.value._id, { orgName: val });
    }
  }
  const setOrgId = (val) => {
    if(docReq.value.org !== val){
      drStore.patchInStore(docReq.value._id, { org: val });
      drStore.patch(docReq.value._id, { org: val });
    }
  }

  const eeChanges = ref({})

  const eeIdx = computed(() => {
    const ees = docReq.value?.employees || [];
    for (let i = 0; i < ees.length; i++) {
      if (ees[i].uid === props.uid) {
        return i
      }
    }
    return -1;
  });

  const ee = computed(() => {
    const ees = docReq.value?.employees || [];
    return { ...props.defEe, ...ees[eeIdx.value] || {}, ...eeChanges.value }
  })

  const loading = ref(true);
  const market = ref({ bronze: { data: [] }, silver: { data: [] }, gold: { data: [] } })
  const metals = computed(() => {
    return {
      bronze: (market.value.bronze?.data || [])[0],
      silver: (market.value.silver?.data || [])[0],
      gold: (market.value.gold?.data || [])[0]
    }
  })

  const refreshMarket = async () => {
    loading.value = true;
    const place = { state: ee.value.state, zipcode: ee.value.zip, countyfips: ee.value.countyfips }
    const people = [getCmsPerson(ee.value.age, false, ee.value.smoker)]
    if (ee.value.married === 'Y') people.push(getCmsPerson(ee.value.spouseAge || ee.value.age, false, ee.value.smoker))
    for (let i = 0; i < ee.value.deps || 0; i++) {
      people.push(getCmsPerson(10, false, false))
    }
    const household = { income: ee.value.hh_income || ee.value.income, people }
    const m = await marketStore.find({ runJoin: { quick_quote: { limit: 1 }}, query: { household, place } })
        .catch(err => console.error(`Error checking marketplace: ${err.message}`))
    if (m) {
      market.value = m;
      emit('update:aptc', m.aptc)
    }
    loading.value = false;
    return m;
  }



  const saveTo = ref();
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      drStore.patch(docReq.value._id, { employees: docReq.value.employees })
    }, 3000)
  }
  const setChanges = async (path) => {
    const original = (docReq.value?.employees || [])[eeIdx.value] || props.defEe;
    const ov = original[path];
    const nv = eeChanges.value[path];
    if ((nv && !ov) || (ov && !nv) || (JSON.stringify(nv) !== JSON.stringify(ov))) {
      let m;
      try {
        m = await refreshMarket()
      } catch (e) {
        console.error(`Error refreshing marketplace: ${e.message}`)
      } finally {
        console.log('set ptc', m.aptc);
        if (m) eeChanges.value.ptc = m.aptc;
        if (docReq.value._id) {
          const newEe = { ...ee.value, ...eeChanges.value }
          const ees = [...docReq.value.employees || []];
          ees.splice(eeIdx.value, 1, newEe)
          drStore.patchInStore(docReq.value._id, { employees: ees });
          maybeSave()
        }
      }
    }
  }
  const setAge = (val, path) => {
    eeChanges.value[path] = val;
    setChanges(path)
  }

  const { h$: hs$ } = HFind({
    store: coverageStore,
    limit: ref(5),
    params: computed(() => {
      return {
        query: {
          type: 'hs',
          public: true,
          sim: true,
          $sort: { fortyPremium: 1 }
        }
      }
    })
  })

  const { byId } = manageFindUploads({ sources:hs$, paths: ['carrierLogo'] })

  const people = computed(() => {
    const age = ee.value.age || 40
    const list = [{ age, relation: 'self'}];
    if(ee.value.married?.toLowerCase().includes('y')) list.push({ age: ee.value.spouseAge || age, relation: 'spouse' })
    for(let i = 0; i < ee.value.deps || 0; i++) list.push({ age: 12, relation: 'child', child: true });
    return list
  })

  const stats = computed(() => {
    return {
      city: ee.value.city,
      place: {
        state: ee.value.state,
        countyfips: ee.value.countyfips,
        zipcode: ee.value.zip
      }
    }
  })

  const setStat = (evt, val) => {
    if (evt === 'city') ee.value.city = val;
    else if (evt === 'place') {
      ee.value.state = val.state;
      ee.value.zip = val.zipcode;
      ee.value.countyfips = val.countyfips;
    }
    setChanges('countyfips')
  }

  const setZipData = (data) => {
    // console.log('set zip data', data);
    if(data?.zip){
      eeChanges.value.zip = data.zip;
      const newPlace = { zipcode: data.zip, state: getStateCode(data.state), countyfips: data.fips, }
      if(isNewPlace(newPlace, stats.value.place)){
        setStat('place', newPlace)
      }
    }
  }

  watch(ee, (nv, ov) => {
    if (nv && nv.uid !== ov?.uid) refreshMarket()
  }, { immediate: true });
</script>

<style lang="scss" scoped>

  .__tw {
    position: relative;
    width: 100%;
    padding: 30px min(25px, 2vw);
    border-radius: 12px;
    //box-shadow: 2px 2px 8px #dedede;
    background: white;
    margin: 10px 0;
  }

  .__gr {
    padding: 3px 10px;
    margin: 10px 0;
    display: grid;
    min-height: 45px;
    align-items: center;
    grid-template-columns: 1fr auto;
    background: var(--q-ir-grey-2);
  }

  .__rem {
    padding: 20px;
    border-top: solid 10px var(--q-p1);
  }
</style>
