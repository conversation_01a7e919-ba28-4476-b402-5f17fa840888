<template>
  <div class="_fw">

    <div class="__tw">
      <div class="row justify-end items-center" v-if="!noAdd">
        <q-chip clickable color="transparent" @click="csvDialog = true">
          <q-icon name="mdi-upload" color="primary" class="q-mr-sm"></q-icon>
          <span>Upload File</span>
        </q-chip>
        <q-checkbox v-model="reqName" label="Include Name"></q-checkbox>
        <q-checkbox v-model="oneName" label="Name one column"></q-checkbox>
      </div>
      <slot name="top"></slot>

      <div :class="`__errs ${Object.keys(useErrs).length ? '__on' : ''}`">
        <div class="text-red tw-six text-xxs">Address data errors to see results</div>
      </div>

      <div class="__tbl">

        <q-btn v-if="sArray.length" flat size="sm" no-caps class="tw-six" @click="remove">
          <q-icon class="q-mr-sm" name="mdi-delete" color="red"></q-icon>

          <span>Remove {{ $possiblyPlural('Row', sArray) }}</span>
        </q-btn>
        <table :class="`__inp alt-font ${errsIn ? '__ers' : ''}`">
          <thead>
          <tr>
            <th style="text-align: right" class="cursor-pointer text-right">
              <q-icon size="20px" :color="allOn ? 'primary' : 'ir-grey-6'"
                      :name="`mdi-checkbox-${allOn ? 'marked' : 'blank-outline'}`" @click="selectAll(!allOn)"></q-icon>
            </th>
            <th v-for="(req, i) in reqs" :key="`req-${i}`" v-show="full || req.required">
              {{ req.label }}
              <q-tooltip>
                <div class="text-center mw300 text-xxs tw-five">{{ req.tooltip }}</div>
              </q-tooltip>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td class="bg-ir-grey-3">Ex:</td>
            <td v-for="(req, i) in reqs" :key="`ex-${i}`" v-show="full || req.required">
              {{ req.format(req.ex, 0, req.key) }}
            </td>
          </tr>
          <!--          first row-->
          <tr v-show="!errsIn || errsIn[0]">
            <td class="cursor-pointer" @click.stop="selected[0] = !selected['0']">
              <span v-if="!selected['0']">1</span>
              <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
            </td>
            <td v-for="(req, idx) in reqs" :key="`ex-0-${idx}`" @click="setFocus([0, idx])" :id="`col-0-${idx}`"
                :class="useErrs[`0-${idx}`] ? '__err' : ''" v-show="full || req.required">
              <q-tooltip v-if="useErrs[`0-${idx}`]">
                <div class="w300 tw-six text-xs">Err: {{ useErrs[`0-${idx}`] }}</div>
              </q-tooltip>
              <q-tooltip v-else-if="errsIn && errsIn[0]" class="w300 tw-six text-xs">{{ errsIn[0] }}</q-tooltip>
              <input
                  class="text-right"
                  :id="`inp-0-${idx}`"
                  @paste="handlePaste(0, idx, $event)"
                  @keydown="keyDown($event)"
                  :value="req.format((csvData[0] || [])[idx], 0, req.key)"
                  @input="setData(0, idx, $event.target.value)"
                  @blur="checkData(0, idx)"
              >
            </td>

          </tr>
          <!--          ROWS 2 - end-->
          <tr v-for="(row, i) in [...csvData].slice(1)" :key="`row-${i + 1}`" :id="`row-${i + 1}`"
              v-show="!errsIn || errsIn[i+1]">
            <td class="cursor-pointer" @click.stop="selected[i+1] = !selected[i+1]">
              <span v-if="!selected[i+1]">{{ i + 2 }}</span>
              <q-icon size="15px" v-else color="primary" name="mdi-checkbox-marked"></q-icon>
            </td>
            <td
                v-for="(req, idx) in reqs"
                :key="`col-${i + 1}-${idx}`"
                :id="`col-${i+1}-${idx}`"
                :class="useErrs[`${i+1}-${idx}`] ? '__err' : ''"
                v-show="full || req.required"
                @click="setFocus([i+1, idx])"
            >
              <q-tooltip v-if="useErrs[`${i+1}-${idx}`]">
                <div class="mw300 tw-six">Err: {{ useErrs[`${i + 1}-${idx}`] }}</div>
              </q-tooltip>
              <q-tooltip v-else-if="errsIn && errsIn[i+1]" class="w300 tw-six text-xs">{{ errsIn[i + 1] }}</q-tooltip>

              <input
                  class="text-right"
                  :id="`inp-${i+1}-${idx}`"
                  @paste="handlePaste(i+1, idx, $event)"
                  @keydown="keyDown($event)"
                  :value="req.format((csvData[i+1] || [])[idx], i+1, req.key)"
                  @input="setData(i+1, idx, $event.target.value)"
                  @blur="checkData(i+1, idx)"
              >
            </td>
          </tr>
          </tbody>
        </table>

      </div>
    </div>
    <div v-if="csvData.length && saveBtn" class="row justify-end q-pt-lg q-px-md">
      <q-btn class="_pl_btn tw-six text-xs" no-caps push @click="save"
             :disable="loading || !!Object.keys(useErrs || {}).length">
        <span class="q-mr-sm">{{ saveBtnLabel || 'See Analysis' }}</span>
        <q-spinner color="white" v-if="loading"></q-spinner>
        <q-icon v-else name="mdi-chevron-right" color="white"></q-icon>
      </q-btn>
    </div>
  </div>

  <common-dialog setting="medium" v-model="csvDialog">
    <div class="_fw q-pa-md">
      <csv-upload
          :max-file-size="1024 * 1024 * 2.5"
          :response="response"
          :headers="uploadHeaders"
          :header-labels="headerLabels"
          :example-data="fields.example" :required="fields
          .required"
          @ready="uploadList"
          @clear="response = {}"
      ></csv-upload>
    </div>
  </common-dialog>

</template>

<script setup>
  import CsvUpload from 'components/common/uploads/csv/CsvUpload.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {$errNotify, $possiblyPlural} from 'src/utils/global-methods';
  import {computed, nextTick, ref, watch} from 'vue';
  import {multiCellArray} from 'src/utils/csv';
  import {idGet} from 'src/utils/id-get';
  import {fromCensus, toCensus, getReqs} from './utils';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';

  const emit = defineEmits(['update:id']);
  const props = defineProps({
    limit: { default: 1000 },
    servicePath: { required: true },
    store: { required: true },
    id: String,
    full: Boolean,
    saveBtn: Boolean,
    update: Function,
    errsIn: Object,
    saveBtnLabel: String,
    adders: { required: false },
    exclude: Array,
    required: Array,
    useName: Boolean,
    noAdd: Boolean
  })

  const { reqs, errs, useErrs, csvData, reqName, oneName, indexByKey, fieldIndexes } = getReqs({ adders: props.adders, required: props.required })

  watch(() => props.useName, (nv, ov) => {
    if (nv && nv !== ov) {
      reqName.value = true;
    }
  }, { immediate: true })

  const headerLabels = computed(() => {
    const obj = {};
    for(const k of reqs.value){
      obj[k.key] = k.label;
    }
    return obj
  })

  const fmt = (v, path, chck) => {
    // console.log('formatting', path, v)
    return chck(v.value);
  }
  const fields = computed(() => {
    const example = [];
    const required = [];
    for(let i = 0; i < reqs.value.length; i++){
      const req = reqs.value[i];
      /** Needed to restructure the v argument to account for the fact that the value passed to the format function validates at .value */
      example.push({ header: req.key, required: req.required, ex: req.ex });
      required.push({ field: req.key, format: req.rev, v: req.required ?  { check: 'format', type: undefined, format: (v) => fmt(v, req.key, req.check), required: req.required } : undefined });
    }
    example.push({ header: 'name', ex: 'John Smith' })

    return { example, required }
  })



  const useReqs = computed(() => !props.full ? reqs.value.filter(a => a.required) : reqs.value);

  const focused = ref([0, 0]);
  const hasFocused = ref(false);
  const lastTab = ref(false);
  const csvDialog = ref(false);
  const setFocus = (val, lt) => {
    focused.value = val;
    hasFocused.value = true;
    const el = document.getElementById(`inp-${val[0]}-${val[1]}`);
    if (el) el.focus();
    lastTab.value = lt;
  }
  const loading = ref(false);

  const selected = ref({});

  const sArray = computed(() => {
    let res = [];
    for (const k in selected.value) {
      if (selected.value[k]) res.push(k)
    }
    return res;
  })

  const allOn = computed(() => sArray.value.length === csvData.value.length)

  const { item } = idGet({
    store: props.store,
    value: computed(() => props.id),
    onWatch: (val) => {
      if (val?.employees) {
        csvData.value = toCensus(val.employees, reqs.value, fieldIndexes.value);
      }
    }
  })

  const versions = ref([]);

  const trySave = async (auto) => {
    if (!Object.keys(useErrs.value).length) {
      const employees = fromCensus(csvData.value, item.value?.employees || [], fieldIndexes.value)
      if (props.update) await props.update(employees, csvData.value, auto)
      return;

    } else $errNotify('Correct all data errors to continue')
  }
  const saveTo = ref();
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      if (item.value?._id) {
        trySave(true)
      }
    }, 5000);
  }
  const changeTo = ref()
  const changing = ref(false);
  const trackChange = (val) => {
    if(!changing.value) {
      versions.value.push(JSON.parse(JSON.stringify(val)));
      changing.value = true;
    }
    clearTimeout(changeTo.value);
    changeTo.value = setTimeout(() => {
      if(!changing.value) versions.value.push(JSON.parse(JSON.stringify(val)));
      maybeSave();
    }, 500);
  }

  const checkData = (row, col) => {
    if (!props.full && !reqs.value[col].required) {
      delete errs.value[row];
    } else if (!csvData.value[row] || !((csvData.value[row] || []).filter(a => !!a)).length) {
      delete errs.value[row];
      // if(csvData.value.length > 1) csvData.value.splice(row, 1);
    } else reqs.value[col].check(csvData.value[row][col], row, col);
    csvData.value[row][col] = reqs.value[col]?.rev(csvData.value[row][col], row);
  }

  const checkAll = () => {
    const data = csvData.value;
    for (let i = 0; i < data.length; i++) {
      for (let idx = 0; idx < Math.min(data[i].length, reqs.value.length); idx++) {
        checkData(i, idx)
      }
    }
  }

  const setData = (row, col, value) => {
    trackChange(csvData.value);

    // Ensure the row exists
    if(row > csvData.value.length + 1) {
      if(props.noAdd) return;
      while (csvData.value.length <= row) {
        csvData.value.push([]); // Add an empty array for missing rows
      }
    }

    for(let i = 0; i < col; i++) {
     const v = csvData.value[row][i];
     if (!v && v !== 0) csvData.value[row][i] = ''
    }

    // Set the value at the specified row and column
    csvData.value[row][col] = reqs.value[col]?.rev(value, row);
  }


  const handlePaste = (row, col, evt) => {
    evt.preventDefault()
    const d = multiCellArray(evt.clipboardData.getData('text').trim());
    // console.log('paste', row, col, d);

    /** function for iterating through a single row, filling blank cells, handling skipped cells due to hidden fields (non required - reqs[idx].required), and pasted values beginning in non-zero cols and extending partially or beyond the visible cols */
    const pasteRow = (rowIdx) => {
      // console.log('paste row', d, rowIdx)
      /** set the first where the paste occurred */
      for (let idx = 0; idx < d[rowIdx].length || 0; idx++) {

        // const skp = skip[idx + col] || 0
        const si = idx + col

        /** idx - col for the pasted value because that data only applies to the visible cells and - col is for non zero start paste */
        setData(row+rowIdx, si, d[rowIdx][idx])
        checkData(row+rowIdx, si)
      }
    }


    for (let i = 0; i < d.length; i++) {
      if(i > props.limit) return $errNotify('If you need more than 1,000 rows, contact us directly for processing')
      if (!Array.isArray(csvData.value[i + row])) csvData.value[i + row] = [];
      pasteRow(i);
    }
  }

  const remove = () => {
    const arr = Object.keys(selected.value);

    for (let i = arr.length; i > -1; i--) {
      if (selected.value[arr[i]]) {
        csvData.value.splice(Number(arr[i]), 1);
        selected.value[arr[i]] = false
      }
    }
    trackChange(csvData.value);
  }

  const selectAll = (val) => {
    if (val) {
      for (let i = 0; i < csvData.value.length; i++) {
        selected.value[i] = true;
      }
    } else selected.value = {};
  }

  const save = async () => {
    loading.value = true
    try {
      checkAll()
      await trySave()
    } catch (e) {
      console.error(`Error running analysis: ${e.message}`)
    } finally {
      loading.value = false;
    }
  }

  const undoIdx = ref(-1);

  const undo = () => {
    if (versions.value.length) {
      if(undoIdx.value < 0) undoIdx.value = versions.value.length - 1;
      csvData.value = [...versions.value[undoIdx.value]];
      undoIdx.value--
      maybeSave();
    }
  }

  const redo = () => {
    if (undoIdx.value < versions.value.length - 1) {
      undoIdx.value++;
      csvData.value = versions.value[undoIdx.value];
      maybeSave();
    }
  }

  const keyDown = (e) => {
    if (e.keyCode === 9) {
      e.preventDefault();
      let i = focused.value[0];
      let idx = focused.value[1];

      const lastVisible = Number(indexByKey.value[useReqs.value[useReqs.value.length - 1].key]);
      if (idx >= lastVisible) {
        if (i >= csvData.value.length - 1) {
          if(csvData.value.length > props.limit) return $errNotify('If you need more than 1,000 rows, contact us directly for processing')
          if(props.noAdd) return;
          csvData.value.push([]);
          // console.log('pushed', csvData.value)
        }
        i += 1;
        idx = Number(indexByKey.value[useReqs.value[0].key]);
      } else if (props.full || reqs.value[idx + 1]?.required) idx++
      else {
        idx = lastVisible;
        for (let i = idx + 1; i < reqs.value.length; i++) {
          if (reqs.value[i].required) {
            idx = i;
            break;
          }
        }
      }
      nextTick(() => setFocus([i, idx], true));
    } else if (e.key === 'z') {
      if (e.metaKey || e.ctrlKey) {
        e.preventDefault();
        if (e.shiftKey) redo();
        else undo();
      }
    } else if (['Enter', 'ArrowDown'].includes(e.key)) {
      e.preventDefault();
      if (hasFocused.value) {
        const row = focused.value[0];
        if (row < csvData.value.length - 1) {
          setFocus([focused.value[0] + 1, focused.value[1]])
        } else {
          if(csvData.value.length > props.limit) return $errNotify('If you need more than 1,000 rows, contact us directly for processing')
          if(props.noAdd) return;
          const l = csvData.value.length;
          csvData.value.push([]);
          for (let i = 0; i <= focused.value[1]; i++) {
            csvData.value[l][i] = '';
          }
          nextTick(() => setFocus([l, focused.value[1]]))
        }
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (focused.value[0] > 0) {
        setFocus([focused.value[0] - 1, focused.value[1]])
      }
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      const lastVisible = Number(indexByKey.value[useReqs.value[useReqs.value.length - 1].key]);
      if (focused.value[1] <= lastVisible - 1) {
        let idx = focused.value[1] + 1;
        if (!props.full) for (let i = idx; i < reqs.value.length; i++) {
          if (reqs.value[i].required) {
            idx = i;
            break;
          }
        }
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], Number(indexByKey.value[useReqs.value[0].key])])
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      const firstVisible = Number(indexByKey.value[useReqs.value[0].key]);
      if (focused.value[1] > firstVisible) {
        let idx = focused.value[1] - 1;
        if (!props.full) for (let i = idx; i >= 0; i--) {
          if (reqs.value[i].required) {
            idx = i;
            break;
          }
        }
        setFocus([focused.value[0], idx])
      } else setFocus([focused.value[0], Number(indexByKey.value[useReqs.value[useReqs.value.length - 1].key])])
    }
  }
  // window.addEventListener('keydown', keyDown);

  const uploadHeaders = computed(() => {
    const list = reqs.value.map(a => a.key).join(',')
    if(list.includes('name')) return list;
    return list + ',name'
  })

  const response = ref({})
  const uploadList = async (val, meta) => {
    let dr = item.value;
    if(!dr.value) dr = await props.store.create({})
    try {
      const res = await axiosFeathers().patch(`/${props.servicePath}/${dr._id}`, val, {
        params: {
          runJoin: { upload_employees: meta, add_files: true },
          core: restCore()
        }
      });
      response.value = res.data?._fastjoin?.uploaded;
      csvData.value = toCensus(res.data.employees, reqs.value, fieldIndexes.value)
      csvDialog.value = false;
      props.update(res.data.employees, { _id: dr._id })
    } catch (e) {
      console.error(`Error saving doc request: ${e.message}`)
      $errNotify(`Error - try again. ${e.message}`)
      if(!item.value._id) props.store.remove(dr._id, { disableSoftDelete: true })
    }
  }

</script>

<style lang="scss" scoped>

  .__tw {
    width: 100%;

    //background: var(--q-ir-grey-1);

    .__tbl {
      width: 100%;
      overflow-x: scroll;
    }
  }

  input {
    border: none;
    width: 6em;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }

  .__hide {
    opacity: 0;
    pointer-events: none;
    width: 0;
  }

  .__inp {
    width: 100%;
    border-collapse: collapse;


    tr {
      th {
        padding: 2px 4px;
        text-align: right;
        //border: solid 1px #999;
        font-size: .8rem;
        color: var(--q-ir-grey-7);
      }

      td {
        font-size: .9rem;
        padding: 4px 8px;
        text-align: right;
        border: solid 1px #999;
        background: white;

        &:first-child {
          background: var(--q-ir-grey-2) !important;
        }
      }

      &:first-child {
        td {
          background: var(--q-ir-grey-2);

          &:first-child {
            //border: none;
          }
        }
      }
    }
  }

  .__ers {
    tr {
      td {
        background: var(--q-ir-red-1) !important;
      }
    }
  }

  .__err {
    background: var(--q-ir-red-1) !important;
    color: var(-q-ir-red-10) !important;
  }

  .__errs {
    padding: 0;
    width: 100%;
    height: auto;
    max-height: 0;
    overflow: hidden;
    transition: all .3s ease;
  }

  .__on {
    max-height: 1500px;
    padding: 0 10px 20px 10px;
  }

</style>
