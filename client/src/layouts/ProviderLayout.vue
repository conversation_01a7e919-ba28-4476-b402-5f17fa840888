<template>
  <q-layout view="hHh Lpr lff" class="bg-transparent __acctLayout">
    <q-header class="__mh" bordered>
      <q-toolbar
          :style="{ background: 'var(--q-s9)', height: isScrolled ? '30px' : '80px', transition: 'all .3s ease-out' }">

        <div class="row items-center relative-position _fw _fh">

          <q-btn dense class="__menu_btn" text-color="white" icon="mdi-menu" flat
                 @click="drawer = !drawer"></q-btn>

          <div class="w300 mw60 text-black q-pa-sm">
            <org-context-item dark></org-context-item>
          </div>

        </div>
        <q-space></q-space>
        <div class="q-px-md">
          <profile-button
          ></profile-button>
        </div>

      </q-toolbar>
    </q-header>

    <q-drawer
        v-model="drawer"
        bordered
        :width="drawerWidth($q.screen)"
        show-if-above
    >
      <div class="__drawer alt-font q-pb-xl">

        <q-list separator>

          <q-item :inset-level=".3" clickable @click="router.push({ name: 'provider-dash'})">
            <q-item-section avatar>
              <q-icon color="s12" name="mdi-home"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six ${$route.name === 'provider-dash' ? 'text-s6' : ''}`">Home</q-item-label>
            </q-item-section>
          </q-item>

          <q-expansion-item
              :default-opened="link.on"
              :model-value="link.on"
              v-for="(link, i) in links"
              :key="`link-${i}`"
              hide-expand-icon
              dense
              style="padding: 0"
              :class="`bg-${link.on ? 'white' : 'transparent'}`"
              @update:model-value="openExp($event, link)">
            <template v-slot:header>
              <q-item class="_fw">
                <q-item-section avatar>
                  <q-icon color="s12" :name="link.icon"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label :class="`tw-six ${link.on ? ' text-s6' : ''}`">{{ link.label }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <div :class="`q-pb-md _fw bg-${link.on ? 'white' : 'transparent'}`">
              <q-list v-for="(item, idx) in Object.keys(link.items)" :key="`item-${i}-${idx}`">
                <!--                <div class="q-pl-lg q-py-sm text-p7 font-3-4r tw-six" header v-if="item?.length">{{ item }}</div>-->
                <template
                    v-for="(sub, index) in Object.keys(link.items[item])"
                    :key="`sub-${i}-${idx}-${index}`">
                  <q-item
                      :inset-level=".5"
                      clickable
                      @click="$router.push(link.items[item][sub].link?.route)">
                    <q-item-section>
                      <q-item-label :class="`tw-five text-${link.items[item][sub].on ? 's6' : ''}`">
                        {{ link.items[item][sub].label || sub }}
                      </q-item-label>
                    </q-item-section>

                  </q-item>
                  <q-list separator v-if="link.items[item][sub].on">
                    <q-item :inset-level=".25" v-for="(subub, ii) in link.items[item][sub].subs || []"
                            :key="`subub-${i}${idx}${index}${ii}`" clickable @click="$router.push(subub.link.route)">
                      <q-item-section avatar>
                        <q-avatar size="15px" color="s2"></q-avatar>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label :class="`tw-five text-${subub.on ? 'p6' : ''}`">
                          <q-badge class="alt-font tw-six" text-color="s9" :color="subub.on ? 's2' : 's0'">
                            {{ subub.label }}
                          </q-badge>
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </template>
              </q-list>
            </div>
          </q-expansion-item>
          <network-drawer></network-drawer>
        </q-list>
      </div>
    </q-drawer>

    <q-page-container>
      <router-view/>
    </q-page-container>

    <q-footer>
      <account-footer text="s9" bg="ir-grey-1" @link="goLink"></account-footer>
    </q-footer>
  </q-layout>
</template>

<script setup>
  import ProfileButton from 'src/layouts/utils/ProfileButton.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';
  import AccountFooter from 'layouts/AccountFooter.vue';
  import NetworkDrawer from 'components/networks/utils/NetworkDrawer.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, ref} from 'vue';
  import {providerNav} from './utils/provider-nav';
  import {visitorPrints} from './utils/prints';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';

  const orgStore = useOrgs();
  const envStore = useEnvStore();

  import {useRouter} from 'vue-router';
  import {toolScroll} from 'layouts/utils/tool-scroll';
  import {useEnvStore} from 'stores/env';
  const router = useRouter();

  const { isScrolled } = toolScroll({ breakpoint: 100 })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => envStore.getOrgId)
  ,
    useAtcStore
  })

  const { links } = providerNav(org.value, { })

  const drawer = ref(false);

  const { route } = visitorPrints({})

  const drawerWidth = (screen) => {
    if (screen.lt.md) return Math.max(screen.width * .8, 250)
    else return 230;
  }

  const goLink = (go) => {
    if (go.route) router.push(go.route)
  }
  const openExp = (val, link) => {
    if(val) router.push(link.link.route)
    else {
      setTimeout(() => {
        if(link.on) router.push(link.link.route)
      }, 50)
    }
  }

</script>

<style scoped lang="scss">
  .__acctLayout {
    width: 100vw;
    overflow: hidden;
  }

  .__menu {
    .__c {
      border-radius: 12px !important;
      border: solid 2px var(--q-secondary);
      width: 100%;
      background: white !important;
      transition: all .5s;
      min-width: 200px;
    }

    .__column {
      width: 200px;
    }
  }

  .__lg {
    padding: 2px 2vw;
  }

  .__drawer {
    background: var(--q-ir-grey-2);
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
    color: var(--q-s12);

    .__link {
      font-size: .6rem;
    }

    .__sub {
      padding: 5px 10px;
      background: #eee;
    }

  }

  .__mh {
    background-color: white;
    transition: all .5s ease;
  }

  .__main_head {
    background-color: transparent !important;
  }

  .__l {
    width: 150px;
    max-width: 50vw;
    max-height: 80%;
    cursor: pointer;
    transition: all .2s;
  }

  .__s {
    width: 150px;
  }

  .__i {
    width: 50px;
    max-height: 90%;
    cursor: pointer;
  }
</style>
