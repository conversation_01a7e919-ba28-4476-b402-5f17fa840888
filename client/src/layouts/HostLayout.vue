<template>
  <q-layout view="hHh Lpr lff" class="bg-transparent __mainlayout">
    <q-header :elevated="scrolled" :class="`__mh ${scrolled ? '' : '__main_head'}`">
      <q-toolbar
          :style="{ backgroundColor: scrolled ? 'white' : 'transparent', height: scrolled ? '30px' : '80px', transition: 'all .3s ease-out' }">

        <div class="row items-center _fw _fh">
          <div class=" _fw _fh pw1">
            <div class="row items-center _fh">
              <div class="col-10 col-md-6 _fh">
                <div class="_fh flex items-center">

                  <q-btn dense class="__menu_btn" text-color="black" icon="mdi-menu" flat
                         @click="drawer = !drawer"></q-btn>

                  <div class="w300 mw60 text-black q-pa-sm">
                    <host-context></host-context>
                  </div>
                </div>
              </div>

              <q-space></q-space>

              <div class="q-px-sm col-2">
                <div class="row justify-end">
                  <profile-button
                  ></profile-button>
                </div>
              </div>
            </div>
          </div>
        </div>

      </q-toolbar>
    </q-header>

    <q-drawer
        :breakpoint="800"
        v-model="drawer"
        bordered
        :width="drawerWidth($q.screen)"
        show-if-above
    >
      <div class="__drawer alt-font q-pb-xl">

        <q-list>

          <q-item :inset-level=".3" clickable @click="router.push('/')">
            <q-item-section avatar>
              <q-icon name="mdi-home"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six ${$route.name === 'org-dash' ? 'text-p6' : ''}`">Dashboard</q-item-label>
            </q-item-section>
          </q-item>

          <q-expansion-item
              :default-opened="link.on"
              :model-value="link.on"
              v-for="(link, i) in links"
              :key="`link-${i}`"
              hide-expand-icon
              dense
              style="padding: 0"
              :class="`bg-${link.on ? 'a1' : 'transparent'}`"
              @update:model-value="openExp($event, link)">
            <template v-slot:header>
              <q-item class="_fw">
                <q-item-section avatar>
                  <q-icon :name="link.icon"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label :class="`tw-six ${link.on ? ' text-accent' : ''}`">{{ link.label }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <div :class="`q-pb-md _fw bg-${link.on ? 'ir-a' : 'transparent'}`">
              <q-list v-for="(item, idx) in Object.keys(link.items)" :key="`item-${i}-${idx}`">
                <!--                <div class="q-pl-lg q-py-sm text-p7 font-3-4r tw-six" header v-if="item?.length">{{ item }}</div>-->
                <template
                    v-for="(sub, index) in Object.keys(link.items[item])"
                    :key="`sub-${i}-${idx}-${index}`">
                  <q-item
                      :inset-level=".5"
                      clickable
                      @click="$router.push(link.items[item][sub].link?.route)">
                    <q-item-section>
                      <q-item-label :class="`text-${link.items[item][sub].on ? 'accent tw-six' : ''}`">
                        {{ link.items[item][sub].label || sub }}
                      </q-item-label>
                    </q-item-section>

                  </q-item>
                  <q-list separator v-if="link.items[item][sub].on">
                    <q-item :inset-level=".65" v-for="(subub, ii) in link.items[item][sub].subs || []"
                            :key="`subub-${i}${idx}${index}${ii}`" clickable @click="$router.push(subub.link.route)">
                      <q-item-section>
                        <q-item-label class="tw-five text-a6">
                          <q-badge
                              class="alt-font tw-six"
                              :text-color="subub.on ? 'white' : 'a6'"
                              :color="subub.on ? 'accent' : 'ir-a'">
                            {{ subub.label }}
                          </q-badge>
                        </q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-avatar size="15px" color="a2"></q-avatar>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </template>
              </q-list>
            </div>
          </q-expansion-item>
          <network-drawer></network-drawer>
          <contract-drawer></contract-drawer>
        </q-list>
      </div>
    </q-drawer>

    <q-page-container>
      <template v-if="hasAccess || $route.meta?.common">
        <router-view/>
      </template>
      <template v-else>
        <div class="q-pa-lg">
          <div class="flex items-center">
            <ai-logo opaque dark></ai-logo>
            <div class="q-px-sm tw-six font-7-8r">Checking Access</div>
          </div>
        </div>
      </template>
    </q-page-container>


    <q-footer>
      <main-footer @link="goLink"></main-footer>
    </q-footer>
  </q-layout>
</template>

<script setup>
  import MainFooter from './MainFooter.vue';
  import ProfileButton from 'src/layouts/utils/ProfileButton.vue';
  import NetworkDrawer from 'components/networks/utils/NetworkDrawer.vue';
  import HostContext from 'components/hosts/utils/HostContext.vue';
  import ContractDrawer from 'components/contracts/utils/ContractDrawer.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, onMounted, ref, watch} from 'vue';
  import {hostNav} from './utils/host-nav';
  import {visitorPrints} from './utils/prints';

  const hostStore = useHosts();
  const refStore = useRefs();

  const { person, login } = loginPerson()

  const { links } = hostNav()

  import {useRouter} from 'vue-router';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {idGet} from 'src/utils/id-get';
  import {useHosts} from 'stores/hosts';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {fakeId} from 'src/utils/global-methods';
  import {useRefs} from 'stores/refs';
  import {trackContext} from 'layouts/utils/track-context';

  const { hostId, envStore } = trackContext()

  const router = useRouter();

  const drawer = ref(false);
  const scrolled = ref(false);


  const {} = visitorPrints({})


  const goLink = (go) => {
    if (go.route) router.push(go.route)
  }

  const drawerWidth = (screen) => {
    if (screen.lt.md) return Math.max(screen.width * .8, 250)
    else return 230;
  }

  const openExp = (val, link) => {
    if (val) router.push(link.link.route)
    else {
      setTimeout(() => {
        if (link.on) router.push(link.link.route)
      }, 50)
    }
  }

  onMounted(() => {
    document.addEventListener('scroll', () => {
      if (window.scrollY > 80) {
        scrolled.value = true;
      } else {
        scrolled.value = false;
      }
    });
  })

  const { item: host } = idGet({
    store: hostStore,
    value: hostId
  ,
    useAtcStore
  })


  const { canEdit } = clientCanU({
    subject: host,
    or: true,
    caps: computed(() => [[`orgs:${host.value.org}`, ['orgAdmin']], [`orgs:${host.value.org}`, ['WRITE']], [`hosts:${hostId.value}`, ['hostAdmin']], [`hosts:${hostId.value}`, ['refAdmin']], [`hosts:${hostId.value}`, ['teamAdmin']]]),
    cap_subjects: computed(() => [host.value.org, hostId.value]),
    login
  })

  const { h$: r$ } = HFind({
    store: refStore,
    limit: ref(1),
    pause: computed(() => canEdit.value.ok),
    params: computed(() => {
      return {
        query: { person: person.value._id || fakeId, host: hostId.value || fakeId }
      }
    })
  })

  const hasAccess = computed(() => canEdit.value.ok || r$.total)
  watch(hasAccess, (val) => {
    if (val) {
      if (r$.total) envStore.setRefId(r$.data[0]._id)
    }
  }, { immediate: true })

</script>

<style scoped lang="scss">
  .__mainlayout {
    width: 100vw;
    overflow: hidden;
  }

  .__menu {
    .__c {
      border-radius: 12px !important;
      border: solid 5px var(--q-primary);
      width: 100%;
      background: white !important;
      transition: all .5s;
      min-width: 200px;
    }

    .__column {
      width: 200px;
    }
  }

  .__drawer {
    width: 100%;
    height: 100vh;
    overflow-y: scroll;

    .__title {
      padding: 10px 25px;
      background: var(--q-p0);
      color: var(--q-p9);
      font-weight: 600;
      font-size: .8rem;
    }

    .__link {
      font-size: .6rem;
    }

    .__sub {
      padding: 5px 10px;
      background: #eee;
    }

  }

  .__mh {
    background-color: white;
    transition: all .5s ease;
  }

  .__main_head {
    background-color: transparent !important;
  }

  .__l {
    width: 50px;
    max-height: 80%;
    cursor: pointer;
    transition: all .2s;
  }

  .__s {
    width: 40px;
  }

  .__i {
    width: 50px;
    max-height: 90%;
    cursor: pointer;
  }
</style>
