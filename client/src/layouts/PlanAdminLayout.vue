<template>
  <q-layout view="hHh Lpr lff" class="bg-transparent __mainlayout">
    <q-header :elevated="isScrolled" :class="`__mh ${isScrolled ? '' : '__main_head'}`">
      <q-toolbar
          :style="{ backgroundColor: 'var(--q-a12)', height: isScrolled ? '30px' : '80px', transition: 'all .3s ease-out' }">

        <div class="__tool">

          <template v-if="isAuthenticated">
          <q-btn dense class="__menu_btn" text-color="white" icon="mdi-menu" flat
                 @click="drawer = !drawer"></q-btn>

          <div class="mw300 text-black q-pa-sm">
            <org-context-item dark></org-context-item>
          </div>
          </template>
          <template v-else>
            <div>
              <q-img class="w50 h50" fit="contain" :src="white_icon"></q-img>
            </div>
            <div></div>
          </template>
          <div class="q-px-md row justify-end">
            <profile-button
            ></profile-button>
          </div>
        </div>


      </q-toolbar>
    </q-header>

    <q-drawer
        v-if="isAuthenticated"
        v-model="drawer"
        bordered
        :width="drawerWidth($q.screen)"
        show-if-above
    >
      <div class="__drawer alt-font q-pb-xl">

        <q-list separator>

          <q-item :inset-level=".3" clickable @click="router.push('/')">
            <q-item-section avatar>
              <q-icon color="white" name="mdi-home"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label :class="`tw-six ${$route.name === 'org-dash' ? 'accent' : ''}`">Dashboard</q-item-label>
            </q-item-section>
          </q-item>

          <q-expansion-item
              :default-opened="link.on"
              :model-value="link.on"
              v-for="(link, i) in links"
              :key="`link-${i}`"
              hide-expand-icon
              dense
              style="padding: 0"
              :class="`bg-${link.on ? 'a10' : 'transparent'}`"
              @update:model-value="openExp($event, link)">
            <template v-slot:header>
              <q-item class="_fw">
                <q-item-section avatar>
                  <q-icon :name="link.icon"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label :class="`tw-six ${link.on ? ' text-a3' : ''}`">{{ link.label }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <div :class="`q-pb-md _fw bg-${link.on ? 'a10' : 'transparent'}`">
              <q-list v-for="(item, idx) in Object.keys(link.items)" :key="`item-${i}-${idx}`">
                <!--                <div class="q-pl-lg q-py-sm text-p7 font-3-4r tw-six" header v-if="item?.length">{{ item }}</div>-->
                <template
                    v-for="(sub, index) in Object.keys(link.items[item])"
                    :key="`sub-${i}-${idx}-${index}`">
                  <q-item
                      :inset-level=".5"
                      clickable
                      @click="$router.push(link.items[item][sub].link?.route)">
                    <q-item-section>
                      <q-item-label :class="`text-${link.items[item][sub].on ? 'a3 tw-six' : ''}`">
                        {{ link.items[item][sub].label || sub }}
                      </q-item-label>
                    </q-item-section>

                  </q-item>
                  <q-list dark separator v-if="link.items[item][sub].on">
                    <q-item :inset-level=".25" v-for="(subub, ii) in link.items[item][sub].subs || []"
                            :key="`subub-${i}${idx}${index}${ii}`" clickable @click="$router.push(subub.link.route)" class="bg-a7">
                      <q-item-section>
                        <q-item-label :class="`tw-five text-${subub.on ? 'p6' : ''}`">
                          <q-badge class="alt-font tw-six q-ml-md" :text-color="subub.on ? 'a9' : 'a2'"
                                   :color="subub.on ? 'a3' : 'a7'">
                            {{ subub.label }}
                          </q-badge>
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </template>
              </q-list>
            </div>
          </q-expansion-item>
          <network-drawer
              dark
              active-bg="a10"
              active-text="a3"
              dark-text="a9"
              light-text="a2"
              off-white-text="a0"
          ></network-drawer>
        </q-list>
      </div>
    </q-drawer>

    <q-page-container>
      <router-view/>
    </q-page-container>

    <q-footer>
      <admin-footer @link="goLink"></admin-footer>
    </q-footer>

    <ims-popup :open="chatWindow" @update:open="envStore.setChatWindow"></ims-popup>

  </q-layout>
</template>

<script setup>
  import white_icon from 'src/assets/commoncare_icon_white.svg'
  import ProfileButton from 'src/layouts/utils/ProfileButton.vue';
  import OrgContextItem from 'components/orgs/utils/OrgContextItem.vue';
  import AdminFooter from 'layouts/AdminFooter.vue';
  import NetworkDrawer from 'components/networks/utils/NetworkDrawer.vue';
  import ImsPopup from 'components/ims/in-app/cards/ImsPopup.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {computed, onMounted, ref} from 'vue';
  import {adminNav} from './utils/plan-admin-nav';
  import {visitorPrints} from './utils/prints';

  import {useRoute, useRouter} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {usePlans} from 'stores/plans';
  import {storeToRefs} from 'pinia';
  import {toolScroll} from 'layouts/utils/tool-scroll';
  import {trackContext} from 'layouts/utils/track-context';
  import {loginPerson} from 'stores/utils/login';

  const orgStore = useOrgs();
  const planStore = usePlans();
  const { envStore, orgId, planId } = trackContext()
  const { chatWindow } = storeToRefs(envStore);

  const router = useRouter();
  const route = useRoute();

  const drawer = ref(false);

  const { isScrolled } = toolScroll({ breakpoint: 100 })
  const { isAuthenticated } = loginPerson();

  const drawerWidth = (screen) => {
    if (screen.lt.md) return Math.max(screen.width * .8, 250)
    else return 230;
  }

  const { item: org } = idGet({
    store: orgStore,
    value: orgId
  ,
    useAtcStore
  })

  const { item:plan } = idGet({
    store: planStore,
    value: computed(() => planId)
  ,
    useAtcStore
  })

  const {} = visitorPrints({})
  const { links } = adminNav(org, { plan })


  const goLink = (go) => {
    if (go.route) router.push(go.route)
  }

  const openExp = (val, link) => {
    if(val) router.push(link.link.route)
    else {
      setTimeout(() => {
        if (link.on) router.push(link.link.route)
      }, 50)
    }
  }

  onMounted(() => {
    if(route.query.client_ucan){
      LocalStorage.setItem('client_ucan', route.query.client_ucan)
      window.localStorage.setItem('feathers-jwt', route.query.client_ucan)
      router.push({ ...route, query: { ...route.query, client_ucan: '' }})
    }
  })

</script>

<style scoped lang="scss">
  .__mainlayout {
    width: 100vw;
    overflow: hidden;
  }

  .__tool {
    position: relative;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: auto auto 1fr;
    align-items: center;

  }

  .__drawer {
    background: var(--q-a12);
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
    color: white;


    .__link {
      font-size: .6rem;
    }

    .__sub {
      padding: 5px 10px;
      background: #eee;
    }

  }

  .__mh {
    background-color: white;
    transition: all .5s ease;
  }

  .__main_head {
    background-color: transparent !important;
  }

  .__l {
    width: 150px;
    max-width: 50vw;
    max-height: 80%;
    cursor: pointer;
    transition: all .2s;
  }

  .__s {
    width: 150px;
  }

  .__i {
    width: 50px;
    max-height: 90%;
    cursor: pointer;
  }
</style>
